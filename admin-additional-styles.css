/**
 * ESTILOS ADICIONALES PARA EL PANEL DE ADMINISTRACIÓN
 * Estilos para notificaciones y elementos que pueden faltar
 */

/* ==================== NOTIFICACIONES MODERNAS ==================== */

.modern-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    padding: 16px;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border-left: 4px solid #6c757d;
}

.modern-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.modern-notification.notification-success {
    border-left-color: #28a745;
}

.modern-notification.notification-error {
    border-left-color: #dc3545;
}

.modern-notification.notification-warning {
    border-left-color: #ffc107;
}

.modern-notification.notification-info {
    border-left-color: #17a2b8;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
    color: white;
}

.notification-success .notification-icon {
    background: #28a745;
}

.notification-error .notification-icon {
    background: #dc3545;
}

.notification-warning .notification-icon {
    background: #ffc107;
}

.notification-info .notification-icon {
    background: #17a2b8;
}

.notification-content {
    flex: 1;
}

.notification-content h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.notification-content p {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.notification-close:hover {
    background: #f8f9fa;
    color: #666;
}

/* ==================== ESTILOS PARA CATEGORÍAS ==================== */

.category-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-icon-container {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.category-info {
    margin-bottom: 16px;
}

.category-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
}

.category-description {
    font-size: 14px;
    color: #666;
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.category-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
}

.category-stats span {
    font-size: 12px;
    color: #888;
    font-weight: 500;
}

.category-subcategories {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.subcategory-tag {
    background: #f8f9fa;
    color: #666;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.subcategory-tag.more {
    background: #e9ecef;
    color: #495057;
}

.category-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.category-card:hover .category-actions {
    opacity: 1;
}

.category-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.category-action-btn:hover {
    background: white;
    color: #333;
    transform: scale(1.1);
}

/* ==================== GRID DE CATEGORÍAS ==================== */

.categories-display-area {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

/* ==================== RESPONSIVE ==================== */

@media (max-width: 768px) {
    .modern-notification {
        left: 20px;
        right: 20px;
        min-width: auto;
        max-width: none;
        transform: translateY(-100%);
    }
    
    .modern-notification.show {
        transform: translateY(0);
    }
    
    .categories-display-area {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .category-card {
        padding: 16px;
    }
}

/* ==================== ANIMACIONES ==================== */

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==================== UTILIDADES ==================== */

.fade-in {
    animation: fadeInUp 0.5s ease forwards;
}

.slide-in-right {
    animation: slideInRight 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.slide-out-right {
    animation: slideOutRight 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}
