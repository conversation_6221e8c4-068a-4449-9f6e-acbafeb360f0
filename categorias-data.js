/**
 * ARCHIVO DE DATOS DE CATEGORÍAS Y SUBCATEGORÍAS
 * Sistema completo para diferentes tipos de locales comerciales
 */

// Categorías principales con sus subcategorías
const CATEGORIAS_DATA = {
    // TECNOLOGÍA Y ELECTRÓNICOS
    tecnologia: {
        id: 'tecnologia',
        nombre: 'Tecnología y Electrónicos',
        icono: 'fas fa-laptop',
        color: '#3b82f6',
        subcategorias: [
            'Smartphones y Celulares',
            'Laptops y Computadoras',
            'Tablets y E-readers',
            'Audio y Auriculares',
            'Cámaras y Fotografía',
            'Gaming y Videojuegos',
            'Wearables y Smartwatch',
            'Accesorios Tecnológicos',
            'Componentes PC',
            'Televisores y Monitores',
            'Electrodomésticos Inteligentes',
            'Drones y Robótica'
        ]
    },

    // MODA Y BELLEZA
    moda: {
        id: 'moda',
        nombre: 'Moda y Vestimenta',
        icono: 'fas fa-tshirt',
        color: '#ec4899',
        subcategorias: [
            'Ropa Femenina',
            'Ropa Masculina',
            'Ropa Infantil',
            'Calzado Deportivo',
            'Calzado Formal',
            'Calzado Casual',
            'Accesorios de Moda',
            'Bolsos y Carteras',
            'Joyería y Bisutería',
            'Relojes',
            'Ropa Interior',
            'Ropa de Baño',
            'Uniformes y Workwear'
        ]
    },

    belleza: {
        id: 'belleza',
        nombre: 'Belleza y Cuidado Personal',
        icono: 'fas fa-spa',
        color: '#f59e0b',
        subcategorias: [
            'Maquillaje y Cosméticos',
            'Cuidado de la Piel',
            'Cuidado Capilar',
            'Perfumes y Fragancias',
            'Cuidado Corporal',
            'Manicure y Pedicure',
            'Productos para Hombres',
            'Cuidado Facial',
            'Protección Solar',
            'Productos Naturales',
            'Herramientas de Belleza',
            'Tratamientos Especializados'
        ]
    },

    // ALIMENTACIÓN Y BEBIDAS
    alimentos: {
        id: 'alimentos',
        nombre: 'Alimentos y Bebidas',
        icono: 'fas fa-utensils',
        color: '#10b981',
        subcategorias: [
            'Productos Frescos',
            'Carnes y Embutidos',
            'Lácteos y Huevos',
            'Panadería y Repostería',
            'Bebidas Alcohólicas',
            'Bebidas No Alcohólicas',
            'Productos Congelados',
            'Conservas y Enlatados',
            'Snacks y Dulces',
            'Productos Orgánicos',
            'Comida Internacional',
            'Productos Dietéticos',
            'Condimentos y Especias'
        ]
    },

    restaurantes: {
        id: 'restaurantes',
        nombre: 'Restaurantes y Gastronomía',
        icono: 'fas fa-pizza-slice',
        color: '#ef4444',
        subcategorias: [
            'Comida Rápida',
            'Restaurante Familiar',
            'Comida Internacional',
            'Pizzería',
            'Cafetería',
            'Parrilla y Asados',
            'Comida Saludable',
            'Mariscos y Pescados',
            'Comida Vegetariana/Vegana',
            'Postres y Helados',
            'Bar y Tragos',
            'Delivery y Take Away'
        ]
    },

    // HOGAR Y DECORACIÓN
    hogar: {
        id: 'hogar',
        nombre: 'Hogar y Decoración',
        icono: 'fas fa-home',
        color: '#8b5cf6',
        subcategorias: [
            'Muebles de Sala',
            'Muebles de Dormitorio',
            'Muebles de Cocina',
            'Decoración y Adornos',
            'Textiles para el Hogar',
            'Iluminación',
            'Electrodomésticos',
            'Herramientas y Ferretería',
            'Jardín y Exterior',
            'Organización y Almacenaje',
            'Arte y Cuadros',
            'Alfombras y Tapetes'
        ]
    },

    // SALUD Y BIENESTAR
    salud: {
        id: 'salud',
        nombre: 'Salud y Bienestar',
        icono: 'fas fa-heartbeat',
        color: '#06b6d4',
        subcategorias: [
            'Farmacia y Medicamentos',
            'Suplementos Nutricionales',
            'Equipos Médicos',
            'Primeros Auxilios',
            'Cuidado Dental',
            'Productos Naturales',
            'Terapias Alternativas',
            'Equipos de Rehabilitación',
            'Productos para Diabéticos',
            'Cuidado Materno Infantil',
            'Productos Ortopédicos',
            'Higiene Personal'
        ]
    },

    // DEPORTES Y FITNESS
    deportes: {
        id: 'deportes',
        nombre: 'Deportes y Fitness',
        icono: 'fas fa-dumbbell',
        color: '#f97316',
        subcategorias: [
            'Ropa Deportiva',
            'Calzado Deportivo',
            'Equipos de Gimnasio',
            'Deportes Acuáticos',
            'Ciclismo',
            'Running y Atletismo',
            'Deportes de Equipo',
            'Artes Marciales',
            'Yoga y Pilates',
            'Deportes Extremos',
            'Camping y Outdoor',
            'Suplementos Deportivos'
        ]
    },

    // AUTOMOTRIZ
    automotriz: {
        id: 'automotriz',
        nombre: 'Automotriz y Transporte',
        icono: 'fas fa-car',
        color: '#374151',
        subcategorias: [
            'Repuestos y Autopartes',
            'Neumáticos',
            'Aceites y Lubricantes',
            'Accesorios para Auto',
            'Audio para Vehículos',
            'Herramientas Automotrices',
            'Productos de Limpieza',
            'Seguridad Vial',
            'Motocicletas',
            'Bicicletas',
            'Transporte Público',
            'Servicios Mecánicos'
        ]
    },

    // EDUCACIÓN Y CULTURA
    educacion: {
        id: 'educacion',
        nombre: 'Educación y Cultura',
        icono: 'fas fa-graduation-cap',
        color: '#7c3aed',
        subcategorias: [
            'Libros y Literatura',
            'Material Escolar',
            'Cursos y Capacitaciones',
            'Instrumentos Musicales',
            'Arte y Manualidades',
            'Juegos Educativos',
            'Software Educativo',
            'Idiomas',
            'Ciencias y Laboratorio',
            'Historia y Geografía',
            'Matemáticas',
            'Tecnología Educativa'
        ]
    },

    // ENTRETENIMIENTO
    entretenimiento: {
        id: 'entretenimiento',
        nombre: 'Entretenimiento y Ocio',
        icono: 'fas fa-gamepad',
        color: '#dc2626',
        subcategorias: [
            'Videojuegos',
            'Juguetes Infantiles',
            'Juegos de Mesa',
            'Películas y Series',
            'Música y Audio',
            'Eventos y Espectáculos',
            'Parques y Diversión',
            'Coleccionables',
            'Hobbies',
            'Fotografía',
            'Lectura',
            'Streaming y Digital'
        ]
    },

    // SERVICIOS PROFESIONALES
    servicios: {
        id: 'servicios',
        nombre: 'Servicios Profesionales',
        icono: 'fas fa-briefcase',
        color: '#059669',
        subcategorias: [
            'Consultoría Empresarial',
            'Servicios Legales',
            'Contabilidad y Finanzas',
            'Marketing y Publicidad',
            'Diseño Gráfico',
            'Desarrollo Web',
            'Fotografía Profesional',
            'Servicios de Limpieza',
            'Mantenimiento Técnico',
            'Traducción',
            'Recursos Humanos',
            'Seguros'
        ]
    },

    // MASCOTAS Y ANIMALES
    mascotas: {
        id: 'mascotas',
        nombre: 'Mascotas y Animales',
        icono: 'fas fa-paw',
        color: '#92400e',
        subcategorias: [
            'Alimento para Perros',
            'Alimento para Gatos',
            'Accesorios para Mascotas',
            'Juguetes para Mascotas',
            'Cuidado e Higiene',
            'Veterinaria',
            'Entrenamiento',
            'Camas y Casas',
            'Correas y Collares',
            'Peceras y Acuarios',
            'Aves y Pájaros',
            'Animales Exóticos'
        ]
    },

    // BEBÉS Y MATERNIDAD
    bebes: {
        id: 'bebes',
        nombre: 'Bebés y Maternidad',
        icono: 'fas fa-baby',
        color: '#fbbf24',
        subcategorias: [
            'Ropa de Bebé',
            'Alimentación Infantil',
            'Pañales e Higiene',
            'Juguetes para Bebés',
            'Cunas y Muebles',
            'Cochecitos y Sillas',
            'Cuidado Materno',
            'Productos de Lactancia',
            'Seguridad Infantil',
            'Decoración Infantil',
            'Libros Infantiles',
            'Accesorios de Viaje'
        ]
    },

    // OFICINA Y PAPELERÍA
    oficina: {
        id: 'oficina',
        nombre: 'Oficina y Papelería',
        icono: 'fas fa-file-alt',
        color: '#6366f1',
        subcategorias: [
            'Material de Escritura',
            'Papel y Cuadernos',
            'Archivadores y Organización',
            'Equipos de Oficina',
            'Mobiliario de Oficina',
            'Suministros de Impresión',
            'Tecnología de Oficina',
            'Material Escolar',
            'Arte y Diseño',
            'Embalaje y Envío',
            'Limpieza de Oficina',
            'Seguridad Laboral'
        ]
    },

    // CONSTRUCCIÓN Y FERRETERÍA
    construccion: {
        id: 'construccion',
        nombre: 'Construcción y Ferretería',
        icono: 'fas fa-hammer',
        color: '#78716c',
        subcategorias: [
            'Herramientas Manuales',
            'Herramientas Eléctricas',
            'Materiales de Construcción',
            'Pinturas y Barnices',
            'Plomería',
            'Electricidad',
            'Cerrajería',
            'Jardinería',
            'Seguridad Industrial',
            'Adhesivos y Selladores',
            'Tornillería',
            'Equipos de Medición'
        ]
    },

    // VIAJES Y TURISMO
    viajes: {
        id: 'viajes',
        nombre: 'Viajes y Turismo',
        icono: 'fas fa-plane',
        color: '#0ea5e9',
        subcategorias: [
            'Equipaje y Maletas',
            'Accesorios de Viaje',
            'Guías Turísticas',
            'Hospedaje',
            'Transporte',
            'Actividades Turísticas',
            'Seguros de Viaje',
            'Cambio de Moneda',
            'Fotografía de Viaje',
            'Ropa de Viaje',
            'Tecnología para Viajeros',
            'Souvenirs'
        ]
    },

    // AGRICULTURA Y JARDINERÍA
    agricultura: {
        id: 'agricultura',
        nombre: 'Agricultura y Jardinería',
        icono: 'fas fa-seedling',
        color: '#16a34a',
        subcategorias: [
            'Semillas y Plantas',
            'Fertilizantes',
            'Herramientas de Jardín',
            'Sistemas de Riego',
            'Macetas y Contenedores',
            'Productos Fitosanitarios',
            'Equipos Agrícolas',
            'Invernaderos',
            'Compostaje',
            'Césped y Pasto',
            'Flores y Ornamentales',
            'Huerta Orgánica'
        ]
    }
};

// Función para obtener todas las categorías
function obtenerTodasLasCategorias() {
    return Object.values(CATEGORIAS_DATA);
}

// Función para obtener categoría por ID
function obtenerCategoriaPorId(id) {
    return CATEGORIAS_DATA[id] || null;
}

// Función para obtener subcategorías de una categoría
function obtenerSubcategorias(categoriaId) {
    const categoria = CATEGORIAS_DATA[categoriaId];
    return categoria ? categoria.subcategorias : [];
}

// Función para buscar categorías por texto
function buscarCategorias(texto) {
    const textoBusqueda = texto.toLowerCase();
    return Object.values(CATEGORIAS_DATA).filter(categoria =>
        categoria.nombre.toLowerCase().includes(textoBusqueda) ||
        categoria.subcategorias.some(sub => sub.toLowerCase().includes(textoBusqueda))
    );
}

// Exportar para uso en otros archivos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CATEGORIAS_DATA,
        obtenerTodasLasCategorias,
        obtenerCategoriaPorId,
        obtenerSubcategorias,
        buscarCategorias
    };
}
