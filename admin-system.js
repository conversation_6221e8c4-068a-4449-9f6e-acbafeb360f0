/**
 * ADMIN PANEL - SISTEMA Y CONFIGURACIONES
 * Gestión de configuraciones, temas, atajos de teclado y funcionalidades del sistema
 */

// ==================== VARIABLES DEL SISTEMA ====================

let hasUnsavedChanges = false;
let autoSaveInterval;

// Analytics del sistema
const analytics = {
    pageViews: 0,
    productViews: {},
    categoryViews: {},
    searchQueries: [],
    userActions: []
};

// ==================== SISTEMA DE CONFIGURACIONES ====================

/**
 * Cargar configuraciones
 */
function loadSettings() {
    const savedSettings = loadFromStorage('admin_settings');
    if (savedSettings) {
        adminSettings = { ...adminSettings, ...savedSettings };
    }
    console.log('⚙️ Configuraciones cargadas:', adminSettings);
}

/**
 * Guardar configuraciones
 */
function saveSettings() {
    saveToStorage('admin_settings', adminSettings);
    showNotification('Configuraciones guardadas', 'success');
}

/**
 * Aplicar configuraciones
 */
function applySettings() {
    // Aplicar tema
    document.documentElement.setAttribute('data-theme', adminSettings.theme);

    // Aplicar modo compacto
    document.body.classList.toggle('compact-mode', adminSettings.compactMode);

    // Aplicar idioma
    applyLanguage(adminSettings.language);

    // Configurar auto-guardado
    if (adminSettings.autoSave) {
        initializeAutoSave();
    } else {
        clearInterval(autoSaveInterval);
    }

    console.log('✅ Configuraciones aplicadas');
}

/**
 * Aplicar idioma
 */
function applyLanguage(language) {
    // Aquí se implementaría la lógica de internacionalización
    document.documentElement.lang = language;
}

// ==================== SISTEMA DE TEMAS ====================

/**
 * Crear estilos de tema
 */
function createThemeStyles() {
    const themeStyles = document.createElement('style');
    themeStyles.id = 'theme-styles';
    themeStyles.textContent = `
        /* Tema Oscuro */
        [data-theme="dark"] {
            --text-primary: rgba(255,255,255,.9);
            --text-secondary: rgba(255,255,255,.6);
            background-color: #1a1a1a;
        }

        [data-theme="dark"] body {
            background-color: #1a1a1a;
            color: var(--text-primary);
        }

        [data-theme="dark"] .admin-sidebar {
            background-color: #2d2d2d;
            border-right: 1px solid #404040;
        }

        [data-theme="dark"] .admin-card,
        [data-theme="dark"] .product-card,
        [data-theme="dark"] .category-card,
        [data-theme="dark"] .stat-card,
        [data-theme="dark"] .admin-kpi-card {
            background-color: #2d2d2d;
            border: 1px solid #404040;
        }

        [data-theme="dark"] .admin-input,
        [data-theme="dark"] .admin-select,
        [data-theme="dark"] .admin-textarea {
            background-color: #404040;
            border-color: #555;
            color: var(--text-primary);
        }

        [data-theme="dark"] .ultra-modal {
            background-color: #2d2d2d;
            border: 1px solid #404040;
        }

        /* Tema Claro */
        [data-theme="light"] {
            --purple-primary: #8e24aa;
            --purple-light: #ba68c8;
            --purple-dark: #4a148c;
        }

        [data-theme="light"] body {
            background-color: #fafafa;
        }

        /* Modo Compacto */
        .compact-mode .admin-kpi-card {
            padding: 10px;
        }

        .compact-mode .admin-kpi-chart {
            height: 30px;
            margin-bottom: 10px;
        }

        .compact-mode .product-card {
            padding: 10px;
        }

        .compact-mode .product-image {
            height: 150px;
        }

        .compact-mode .admin-card-body {
            padding: 15px;
        }

        .compact-mode .stat-card {
            padding: 15px;
        }
    `;

    document.head.appendChild(themeStyles);
}

// ==================== SISTEMA DE ATAJOS DE TECLADO ====================

/**
 * Inicializar atajos de teclado
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl + N: Nuevo producto
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            openProductModal();
            showNotification('Atajo: Nuevo producto', 'info');
        }

        // Ctrl + S: Guardar cambios
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const activeModal = document.querySelector('.admin-modal-overlay.active, .ultra-modal-overlay.active');
            if (activeModal) {
                const saveBtn = activeModal.querySelector('.admin-btn-primary, .save-btn');
                if (saveBtn) {
                    saveBtn.click();
                }
            }
            showNotification('Atajo: Guardar', 'info');
        }

        // Escape: Cerrar modales
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.admin-modal-overlay.active, .ultra-modal-overlay.active');
            if (activeModal) {
                activeModal.classList.remove('active');
            }
        }

        // Ctrl + F: Búsqueda rápida
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('searchName');
            if (searchInput) {
                searchInput.focus();
                showNotification('Atajo: Búsqueda activada', 'info');
            }
        }

        // Ctrl + E: Exportar datos
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportData();
        }

        // Ctrl + H: Mostrar ayuda
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            showHelpModal();
        }

        // Ctrl + Shift + S: Abrir configuración
        if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            const settingsModal = document.getElementById('settingsModal');
            if (settingsModal) {
                settingsModal.classList.add('active');
            }
        }

        // Alt + 1-6: Cambiar pestañas de productos
        if (e.altKey && e.key >= '1' && e.key <= '6') {
            e.preventDefault();
            const tabs = document.querySelectorAll('.admin-tab');
            const tabIndex = parseInt(e.key) - 1;
            if (tabs[tabIndex]) {
                tabs[tabIndex].click();
                showNotification(`Atajo: Pestaña ${tabs[tabIndex].textContent}`, 'info');
            }
        }
    });

    console.log('⌨️ Atajos de teclado inicializados');
}

// ==================== SISTEMA DE AUTO-GUARDADO ====================

/**
 * Inicializar auto-guardado
 */
function initializeAutoSave() {
    if (adminSettings.autoSave) {
        autoSaveInterval = setInterval(() => {
            if (hasUnsavedChanges) {
                autoSaveData();
                hasUnsavedChanges = false;
            }
        }, 30000); // Auto-guardar cada 30 segundos
    }
}

/**
 * Auto-guardar datos
 */
function autoSaveData() {
    const autoSaveData = {
        categories: categories,
        settings: adminSettings,
        timestamp: new Date().toISOString()
    };

    saveToStorage('admin_autosave', autoSaveData);
    showAutoSaveIndicator();
}

/**
 * Mostrar indicador de guardado automático
 */
function showAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'autosave-indicator';
    indicator.innerHTML = '<i class="fas fa-save"></i> Guardado automático';
    indicator.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: var(--success-color);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 12px;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    document.body.appendChild(indicator);

    setTimeout(() => {
        indicator.style.opacity = '1';
    }, 100);

    setTimeout(() => {
        indicator.style.opacity = '0';
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 300);
    }, 2000);
}

/**
 * Marcar como cambiado
 */
function markAsChanged() {
    hasUnsavedChanges = true;

    // Mostrar indicador de cambios no guardados
    if (!document.querySelector('.unsaved-indicator')) {
        const indicator = document.createElement('div');
        indicator.className = 'unsaved-indicator';
        indicator.innerHTML = '<i class="fas fa-circle"></i>';
        indicator.style.cssText = `
            position: fixed;
            top: 15px;
            right: 100px;
            color: #ff9800;
            font-size: 12px;
            z-index: 9999;
        `;
        document.body.appendChild(indicator);
    }
}

/**
 * Marcar como guardado
 */
function markAsSaved() {
    hasUnsavedChanges = false;
    const indicator = document.querySelector('.unsaved-indicator');
    if (indicator) {
        indicator.remove();
    }
}

// ==================== SISTEMA DE ANALYTICS ====================

/**
 * Rastrear vista de página
 */
function trackPageView() {
    analytics.pageViews++;
    analytics.userActions.push({
        type: 'page_view',
        timestamp: new Date().toISOString(),
        page: 'admin_dashboard'
    });
}

/**
 * Rastrear vista de producto
 */
function trackProductView(productName) {
    analytics.productViews[productName] = (analytics.productViews[productName] || 0) + 1;
    analytics.userActions.push({
        type: 'product_view',
        timestamp: new Date().toISOString(),
        product: productName
    });
}

/**
 * Rastrear vista de categoría
 */
function trackCategoryView(categoryName) {
    analytics.categoryViews[categoryName] = (analytics.categoryViews[categoryName] || 0) + 1;
    analytics.userActions.push({
        type: 'category_view',
        timestamp: new Date().toISOString(),
        category: categoryName
    });
}

/**
 * Rastrear búsqueda
 */
function trackSearch(query) {
    analytics.searchQueries.push({
        query: query,
        timestamp: new Date().toISOString()
    });
    analytics.userActions.push({
        type: 'search',
        timestamp: new Date().toISOString(),
        query: query
    });
}

/**
 * Generar reporte de analytics
 */
function generateAnalyticsReport() {
    const report = {
        summary: {
            totalPageViews: analytics.pageViews,
            totalProductViews: Object.values(analytics.productViews).reduce((a, b) => a + b, 0),
            totalCategoryViews: Object.values(analytics.categoryViews).reduce((a, b) => a + b, 0),
            totalSearches: analytics.searchQueries.length
        },
        topProducts: Object.entries(analytics.productViews)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5),
        topCategories: Object.entries(analytics.categoryViews)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5),
        recentSearches: analytics.searchQueries.slice(-10),
        generatedAt: new Date().toISOString()
    };

    return report;
}

// ==================== SISTEMA DE BACKUP ====================

/**
 * Crear backup
 */
function createBackup() {
    const backupData = {
        categories: categories,
        settings: adminSettings,
        analytics: analytics,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    };

    saveToStorage('admin_backup', backupData);
    showNotification('Backup creado correctamente', 'success');
}

/**
 * Restaurar backup
 */
function restoreBackup() {
    try {
        const backupData = loadFromStorage('admin_backup');
        if (backupData) {
            if (backupData.categories) {
                categories = backupData.categories;
                loadCategories();
                updateStats();
                updateProductCategorySelect();
            }
            if (backupData.settings) {
                adminSettings = backupData.settings;
                applySettings();
            }
            showNotification('Backup restaurado correctamente', 'success');
        } else {
            showNotification('No se encontró ningún backup', 'warning');
        }
    } catch (error) {
        console.error('Error al restaurar backup:', error);
        showNotification('Error al restaurar el backup', 'error');
    }
}

/**
 * Auto-backup
 */
function autoBackup() {
    // Crear backup automático cada 5 minutos
    setInterval(() => {
        createBackup();
        console.log('Auto-backup created at:', new Date().toLocaleTimeString());
    }, 5 * 60 * 1000);
}

// ==================== FUNCIONES DE EXPORTACIÓN/IMPORTACIÓN ====================

/**
 * Exportar datos
 */
function exportData() {
    const exportData = {
        categories: categories,
        settings: adminSettings,
        analytics: analytics,
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    downloadFile(dataStr, `admin-data-${formatDate(new Date(), 'short')}.json`, 'application/json');

    showNotification('Datos exportados correctamente', 'success');
}

/**
 * Importar datos
 */
async function importData(file) {
    try {
        const fileContent = await readFileAsText(file);
        const importedData = JSON.parse(fileContent);

        if (importedData.categories) {
            categories = importedData.categories;
            loadCategories();
            updateStats();
            updateProductCategorySelect();
        }

        if (importedData.settings) {
            adminSettings = { ...adminSettings, ...importedData.settings };
            applySettings();
            saveSettings();
        }

        showNotification('Datos importados correctamente', 'success');
    } catch (error) {
        console.error('Error al importar datos:', error);
        showNotification('Error al importar datos', 'error');
    }
}

// ==================== OPTIMIZACIONES DE RENDIMIENTO ====================

/**
 * Optimizar imágenes
 */
function optimizeImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        // Lazy loading
        img.loading = 'lazy';

        // Optimización de carga
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });

        img.addEventListener('error', function() {
            this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbiBubyBkaXNwb25pYmxlPC90ZXh0Pjwvc3ZnPg==';
        });
    });
}

/**
 * Limpiar sistema
 */
function cleanup() {
    // Limpiar intervalos
    if (autoSaveInterval) {
        clearInterval(autoSaveInterval);
    }

    // Limpiar event listeners
    document.removeEventListener('keydown', initializeKeyboardShortcuts);

    console.log('🧹 Sistema limpiado');
}

// ==================== SISTEMA DE AYUDA ====================

/**
 * Crear sistema de ayuda
 */
function createHelpSystem() {
    const helpButton = document.createElement('button');
    helpButton.className = 'admin-help-btn';
    helpButton.innerHTML = '<i class="fas fa-question-circle"></i>';
    helpButton.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--purple-primary);
        color: white;
        border: none;
        font-size: 20px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        z-index: 1000;
        transition: all 0.3s ease;
    `;

    helpButton.addEventListener('mouseenter', () => {
        helpButton.style.transform = 'scale(1.1)';
        helpButton.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
    });

    helpButton.addEventListener('mouseleave', () => {
        helpButton.style.transform = 'scale(1)';
        helpButton.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
    });

    helpButton.addEventListener('click', showHelpModal);
    document.body.appendChild(helpButton);
}

/**
 * Mostrar modal de ayuda
 */
function showHelpModal() {
    const helpModal = document.createElement('div');
    helpModal.className = 'admin-modal-overlay active';
    helpModal.innerHTML = `
        <div class="admin-modal" style="max-width: 800px;">
            <div class="admin-modal-header">
                <h2 class="admin-modal-title">
                    <i class="fas fa-question-circle"></i> Centro de Ayuda
                </h2>
                <button class="admin-modal-close">&times;</button>
            </div>
            <div class="admin-modal-body">
                <div class="help-tabs">
                    <button class="help-tab active" data-help-tab="general">General</button>
                    <button class="help-tab" data-help-tab="products">Productos</button>
                    <button class="help-tab" data-help-tab="categories">Categorías</button>
                    <button class="help-tab" data-help-tab="shortcuts">Atajos</button>
                </div>

                <div class="help-content">
                    <div class="help-tab-content active" id="help-general">
                        <h3>Información General</h3>
                        <ul>
                            <li><strong>Dashboard:</strong> Vista principal con estadísticas y KPIs</li>
                            <li><strong>Navegación:</strong> Usa el menú lateral para acceder a diferentes secciones</li>
                            <li><strong>Búsqueda:</strong> Utiliza la búsqueda avanzada para filtrar productos</li>
                            <li><strong>Configuración:</strong> Personaliza el tema y preferencias</li>
                        </ul>
                    </div>

                    <div class="help-tab-content" id="help-products">
                        <h3>Gestión de Productos</h3>
                        <ul>
                            <li><strong>Agregar:</strong> Haz clic en el botón "+" para agregar nuevos productos</li>
                            <li><strong>Editar:</strong> Usa el ícono de lápiz en cada producto</li>
                            <li><strong>Eliminar:</strong> Usa el ícono de papelera (acción irreversible)</li>
                            <li><strong>Activar/Desactivar:</strong> Usa el toggle para cambiar el estado</li>
                            <li><strong>Ordenar:</strong> Arrastra y suelta para reordenar productos</li>
                        </ul>
                    </div>

                    <div class="help-tab-content" id="help-categories">
                        <h3>Gestión de Categorías</h3>
                        <ul>
                            <li><strong>Crear:</strong> Agrega nuevas categorías con subcategorías</li>
                            <li><strong>Iconos:</strong> Selecciona iconos representativos para cada categoría</li>
                            <li><strong>Subcategorías:</strong> Organiza productos en subcategorías específicas</li>
                            <li><strong>Estado:</strong> Activa o desactiva categorías según necesidad</li>
                        </ul>
                    </div>

                    <div class="help-tab-content" id="help-shortcuts">
                        <h3>Atajos de Teclado</h3>
                        <ul>
                            <li><strong>Ctrl + N:</strong> Nuevo producto</li>
                            <li><strong>Ctrl + S:</strong> Guardar cambios</li>
                            <li><strong>Escape:</strong> Cerrar modales</li>
                            <li><strong>Ctrl + F:</strong> Búsqueda rápida</li>
                            <li><strong>Ctrl + E:</strong> Exportar datos</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="admin-modal-footer">
                <button class="admin-btn admin-btn-primary" onclick="this.closest('.admin-modal-overlay').remove()">
                    Entendido
                </button>
            </div>
        </div>
    `;

    // Estilos para el modal de ayuda
    const helpStyles = document.createElement('style');
    helpStyles.textContent = `
        .help-tabs {
            display: flex;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .help-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-secondary);
            border-bottom: 2px solid transparent;
        }
        .help-tab.active {
            color: var(--purple-primary);
            border-bottom-color: var(--purple-primary);
        }
        .help-tab-content {
            display: none;
        }
        .help-tab-content.active {
            display: block;
        }
        .help-tab-content h3 {
            color: var(--purple-primary);
            margin-bottom: 15px;
        }
        .help-tab-content ul {
            list-style: none;
            padding: 0;
        }
        .help-tab-content li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        .help-tab-content li:last-child {
            border-bottom: none;
        }
    `;
    document.head.appendChild(helpStyles);

    document.body.appendChild(helpModal);

    // Event listeners para las pestañas de ayuda
    const helpTabs = helpModal.querySelectorAll('.help-tab');
    helpTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            helpTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            const tabContents = helpModal.querySelectorAll('.help-tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            const targetContent = helpModal.querySelector(`#help-${tab.dataset.helpTab}`);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });

    // Cerrar modal
    helpModal.querySelector('.admin-modal-close').addEventListener('click', () => {
        helpModal.remove();
    });

    helpModal.addEventListener('click', (e) => {
        if (e.target === helpModal) {
            helpModal.remove();
        }
    });
}

console.log('⚙️ Admin System Script Loaded Successfully');
