/* ==================== VARIABLES CSS ==================== */
:root {
    /* Colores principales */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --purple-primary: #6a1b9a;

    /* Colores de estado */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* Colores de texto */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --text-white: #ffffff;

    /* Colores de fondo */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-dark: #1f2937;
    --bg-sidebar: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Bordes y sombras */
    --border-color: #e5e7eb;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transiciones */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

    /* Tipografía */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
}

/* ==================== RESET Y BASE ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    display: flex;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ==================== LAYOUT PRINCIPAL ==================== */

/* Botón de menú móvil */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--primary-color);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: var(--border-radius);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

/* Sidebar */
.admin-sidebar {
    width: 280px;
    min-width: 280px;
    background: var(--bg-sidebar);
    color: white;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1000;
    box-shadow: var(--shadow-xl);
}

.sidebar-header {
    padding: 2rem 1.5rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.logo h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
}

.subtitle {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

/* Navegación del sidebar */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section h3 {
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: rgba(255, 255, 255, 0.6);
    padding: 0 1.5rem 0.75rem;
    margin-bottom: 0.5rem;
}

.nav-section ul {
    list-style: none;
}

.nav-section li {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    font-weight: 500;
    border-radius: 0;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-right: 3px solid var(--accent-color);
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
    font-size: var(--font-size-base);
}

.nav-link .badge {
    margin-left: auto;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: var(--font-size-xs);
    padding: 0.125rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
}

.nav-link .badge.urgent {
    background: var(--error-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.nav-indicator {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--accent-color);
    opacity: 0;
    transition: var(--transition);
}

.nav-link.active .nav-indicator {
    opacity: 1;
}

/* Footer del sidebar */
.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.user-info {
    flex: 1;
}

.user-info h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: white;
    margin-bottom: 0.125rem;
}

.user-info p {
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.7);
}

.logout-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* ==================== CONTENIDO PRINCIPAL ==================== */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: var(--bg-secondary);
}

/* Header principal */
.main-header {
    background: var(--bg-primary);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-left .page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.header-left .page-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 400;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    font-size: var(--font-size-sm);
    width: 300px;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.notification-btn,
.settings-btn {
    position: relative;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    width: 44px;
    height: 44px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.notification-btn:hover,
.settings-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.notification-count {
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--error-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* ==================== ÁREA DE CONTENIDO ==================== */
.content-area {
    flex: 1;
    padding: 2rem;
    max-width: 100%;
    overflow-x: hidden;
}

/* ==================== SECCIÓN KPI ==================== */
.kpi-section {
    margin-bottom: 3rem;
}

.kpi-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.kpi-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
}

.time-filter {
    display: flex;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 0.25rem;
    box-shadow: var(--shadow-sm);
}

.time-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    border-radius: calc(var(--border-radius) - 2px);
    transition: var(--transition);
}

.time-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.time-btn:hover:not(.active) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Grid de KPIs */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 1200px) {
    .kpi-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .kpi-grid {
        grid-template-columns: 1fr;
    }
}

.kpi-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.kpi-card.sales::before {
    background: linear-gradient(90deg, var(--success-color), #059669);
}

.kpi-card.orders::before {
    background: linear-gradient(90deg, var(--info-color), #2563eb);
}

.kpi-card.products::before {
    background: linear-gradient(90deg, var(--warning-color), #d97706);
}

.kpi-card.customers::before {
    background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
}

.kpi-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
}

.kpi-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.kpi-info {
    flex: 1;
    min-width: 0;
}

.sales .kpi-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.orders .kpi-icon {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.products .kpi-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.customers .kpi-icon {
    background: rgba(240, 147, 251, 0.1);
    color: var(--accent-color);
}

.visits .kpi-icon {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.time .kpi-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.pages .kpi-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.bounce .kpi-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.mobile .kpi-icon {
    background: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

.conversions .kpi-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.kpi-info h3 {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: none;
    letter-spacing: normal;
}

.kpi-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.kpi-change {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.kpi-change.positive {
    color: var(--success-color);
}

.kpi-change.negative {
    color: var(--error-color);
}

.kpi-change.neutral {
    color: var(--text-secondary);
}

.kpi-chart {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 80px;
    height: 40px;
    opacity: 0.6;
}

.kpi-chart svg {
    width: 100%;
    height: 100%;
}

/* Estilos específicos para KPIs sin gráficos */
.kpi-card.total-products,
.kpi-card.categories,
.kpi-card.products-offer,
.kpi-card.products-featured {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.kpi-card.total-products .kpi-value {
    color: var(--purple-primary);
}

.kpi-card.categories .kpi-value {
    color: var(--purple-primary);
}

.kpi-card.products-offer .kpi-value {
    color: var(--purple-primary);
}

.kpi-card.products-featured .kpi-value {
    color: var(--purple-primary);
}

/* ==================== GESTIÓN SIMPLE DE CATEGORÍAS ==================== */

/* Panel Principal */
.categories-main-panel {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

/* Secciones de Categorías y Subcategorías */
.categories-section,
.subcategories-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Dropdown Container */
.dropdown-container {
    position: relative;
    min-width: 200px;
    z-index: 1000;
}

.dropdown-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.dropdown-btn:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.dropdown-btn:disabled {
    background: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
    border-color: var(--border-color);
}

.dropdown-btn i {
    transition: transform 0.3s ease;
}

.dropdown-btn.active i {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: white !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
    z-index: 99999 !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    display: none !important;
}

.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 99999 !important;
    position: absolute !important;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.05);
}

.dropdown-item.disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.dropdown-item.disabled:hover {
    background: transparent;
}

.dropdown-item i {
    font-size: 1rem;
    color: var(--primary-color);
}

.dropdown-item span {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

/* Contenedor de Items Seleccionados */
.selected-items-container {
    min-height: 200px;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    background: var(--bg-secondary);
}

.selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

/* Bloques de Items Seleccionados */
.selected-item-block {
    background: white;
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: var(--font-size-sm);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 2px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.selected-item-block:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.selected-item-block i {
    font-size: 0.875rem;
}

.selected-item-block .remove-btn {
    background: rgba(239, 68, 68, 0.1);
    border: none;
    color: #ef4444;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    transition: var(--transition);
}

.selected-item-block .remove-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

.selected-item-block .remove-btn i {
    font-size: 0.75rem;
}

/* Subcategorías con color diferente */
.selected-item-block.subcategory {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

/* Estado vacío */
.selected-items-container.empty::before {
    content: "Los elementos seleccionados aparecerán aquí";
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 150px;
}

/* Sección de Guardar */
.save-section {
    text-align: center;
    padding: 2rem 0;
}

.save-btn {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
}

.save-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.save-btn:active {
    transform: translateY(0);
}

.save-btn i {
    font-size: 1.125rem;
}

/* Responsive */
@media (max-width: 768px) {
    .categories-main-panel {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .dropdown-container {
        min-width: auto;
    }
}

/* ==================== SECCIÓN DE PRODUCTOS ==================== */
.container-products {
    margin-bottom: 3rem;
}

.container-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.admin-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.admin-card-body {
    padding: 0;
}

.admin-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    padding: 0 1.5rem;
    overflow-x: auto;
}

.admin-tab {
    padding: 1rem 1.5rem;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    white-space: nowrap;
    position: relative;
}

.admin-tab:hover {
    color: var(--text-primary);
    background: rgba(102, 126, 234, 0.05);
}

.admin-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-primary);
}

.admin-tab-content {
    display: none;
    padding: 1.5rem;
}

.admin-tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 2rem;
    gap: 2rem;
}

.section-header .header-left h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.section-header .header-left p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-primary,
.btn-secondary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    border: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--text-secondary);
}

/* Controles de productos */
.products-controls {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.product-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.tab-btn:hover:not(.active) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    font-size: var(--font-size-xs);
    padding: 0.125rem 0.375rem;
    border-radius: 8px;
    font-weight: 600;
}

.tab-btn.active .tab-count {
    background: rgba(255, 255, 255, 0.3);
}

.products-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--text-secondary);
}

/* ==================== GRID DE PRODUCTOS ==================== */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
    margin: 0;
}

/* Estilos para las clases del admin.html */
.add-product-card {
    border: 2px dashed var(--border-color);
    background: var(--bg-secondary);
    transition: var(--transition);
}

.add-product-card:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.02);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: white;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.product-overlay-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.product-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    min-height: 320px;
    max-height: 340px;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.product-image {
    position: relative;
    height: 140px;
    overflow: hidden;
    margin: 0.25rem;
    border-radius: 4px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-details {
    padding: 0.5rem;
    height: calc(100% - 150px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-category {
    font-size: 8px;
    color: var(--text-secondary);
    margin-bottom: 0.125rem;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    font-weight: 600;
    line-height: 1;
}

.product-name {
    font-size: 10px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 2.4em;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 0.25rem;
    flex-wrap: wrap;
}

.current-price {
    font-size: 11px;
    font-weight: 700;
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
}

.original-price {
    font-size: 8px;
    color: var(--text-secondary);
    text-decoration: line-through;
}

.product-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
}

.product-badge {
    padding: 0.0625rem 0.25rem;
    border-radius: 6px;
    font-size: 7px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.badge-active {
    background: var(--primary-color);
    color: white;
}

.product-toggle {
    position: relative;
    display: inline-block;
    width: 32px;
    height: 18px;
}

.product-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.product-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-tertiary);
    transition: var(--transition);
    border-radius: 18px;
}

.product-toggle-slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 2px;
    bottom: 2px;
    background: white;
    transition: var(--transition);
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.product-toggle input:checked + .product-toggle-slider {
    background: var(--primary-color);
}

.product-toggle input:checked + .product-toggle-slider:before {
    transform: translateX(14px);
}

/* Tarjeta para agregar producto */
.product-card.add-product {
    background: var(--bg-primary);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    min-height: 400px;
}

.product-card.add-product:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.02);
}

.add-product-content {
    text-align: center;
}

.add-icon {
    width: 64px;
    height: 64px;
    background: var(--bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.product-card.add-product:hover .add-icon {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.add-product-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.add-product-content p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Tarjetas de productos */
.product-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge.featured {
    background: var(--primary-color);
    color: white;
}

.badge.discount {
    background: var(--error-color);
    color: white;
}

.badge.new {
    background: var(--success-color);
    color: white;
}

.product-actions {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transform: translateX(10px);
    transition: var(--transition);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: white;
    color: var(--text-primary);
    transform: scale(1.1);
}

.action-btn.edit:hover {
    color: var(--info-color);
}

.action-btn.duplicate:hover {
    color: var(--warning-color);
}

.action-btn.delete:hover {
    color: var(--error-color);
}

.product-info {
    padding: 1.5rem;
}

.product-category {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.original-price {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    text-decoration: line-through;
}

.product-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.stat i {
    font-size: 10px;
}

.product-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.stock-indicator {
    flex: 1;
}

.stock-indicator span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
    display: block;
}

.stock-bar {
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.stock-bar::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    border-radius: 2px;
    transition: var(--transition);
}

.stock-indicator.high .stock-bar::after {
    width: 90%;
    background: var(--success-color);
}

.stock-indicator.medium .stock-bar::after {
    width: 40%;
    background: var(--warning-color);
}

.stock-indicator.low .stock-bar::after {
    width: 15%;
    background: var(--error-color);
}

/* Toggle switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-tertiary);
    transition: var(--transition);
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: var(--transition);
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
    background: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* ==================== GESTIÓN DE CATEGORÍAS ==================== */
.container-categories {
    margin-bottom: 3rem;
}

.categories-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    min-height: 600px;
}

/* Panel de Categorías */
.categories-panel {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.subcategories-panel {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.add-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* Lista de Categorías */
.categories-list {
    padding: 1rem;
    max-height: 500px;
    overflow-y: auto;
}

.category-item {
    background: var(--bg-secondary);
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.category-item.active {
    background: rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.category-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.category-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.category-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.category-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.category-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.category-item:hover .category-actions {
    opacity: 1;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.action-btn.delete-btn:hover {
    background: var(--error-color);
}

/* Grid de Subcategorías */
.subcategories-grid {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    max-height: 500px;
    overflow-y: auto;
}

.subcategory-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    transition: var(--transition);
    position: relative;
}

.subcategory-card:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.subcategory-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.subcategory-icon {
    width: 36px;
    height: 36px;
    background: var(--secondary-color);
    color: white;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-base);
}

.subcategory-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.subcategory-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.product-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.subcategory-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.subcategory-card:hover .subcategory-actions {
    opacity: 1;
}

/* ==================== MODALES ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.active .modal-container {
    transform: scale(1);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
    max-height: 400px;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Selector de Iconos */
.icon-selector {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.icon-option {
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.icon-option:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.icon-option.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.modal-footer {
    background: var(--bg-secondary);
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* ==================== RESPONSIVE DESIGN ==================== */
@media (max-width: 1024px) {
    .content-area {
        padding: 1.5rem;
    }

    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .categories-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .subcategories-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .admin-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 1001;
        transition: left 0.3s ease;
    }

    .admin-sidebar.active {
        left: 0;
    }

    .main-content {
        width: 100%;
    }

    .main-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-right {
        justify-content: space-between;
    }

    .search-box input {
        width: 100%;
        max-width: 250px;
    }

    .content-area {
        padding: 1rem;
    }

    .kpi-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .time-filter {
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-actions {
        justify-content: center;
    }

    .products-controls {
        padding: 1rem;
    }

    .product-tabs {
        justify-content: center;
    }

    .products-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 0.75rem;
    }

    .product-card {
        min-height: 300px;
        max-height: 320px;
    }

    .product-image {
        height: 130px;
        margin: 0.25rem;
    }

    .product-details {
        height: calc(100% - 140px);
        padding: 0.375rem;
    }

    .product-name {
        font-size: 9px;
        height: 2.2em;
    }

    .current-price {
        font-size: 10px;
        padding: 0.125rem 0.25rem;
    }

    .product-category {
        font-size: 7px;
    }

    .product-badge {
        font-size: 6px;
        padding: 0.0625rem 0.25rem;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .subcategories-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .icon-selector {
        grid-template-columns: repeat(6, 1fr);
    }

    .modal-container {
        width: 95%;
        margin: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .panel-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .add-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .product-tabs {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: center;
    }

    .main-header .page-title {
        font-size: var(--font-size-xl);
    }

    .kpi-card {
        padding: 1rem;
    }

    .products-controls {
        padding: 0.75rem;
    }


}

/* ==================== ANIMACIONES ==================== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.kpi-card {
    animation: fadeInUp 0.6s ease forwards;
}

.kpi-card:nth-child(1) { animation-delay: 0.1s; }
.kpi-card:nth-child(2) { animation-delay: 0.2s; }
.kpi-card:nth-child(3) { animation-delay: 0.3s; }
.kpi-card:nth-child(4) { animation-delay: 0.4s; }

.product-card {
    animation: fadeInUp 0.6s ease forwards;
}

.summary-card {
    animation: slideInLeft 0.6s ease forwards;
}

.summary-card:nth-child(1) { animation-delay: 0.1s; }
.summary-card:nth-child(2) { animation-delay: 0.2s; }
.summary-card:nth-child(3) { animation-delay: 0.3s; }
