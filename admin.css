/* ==================== ADMIN PANEL STYLES ==================== */
/* Archivo CSS separado para el panel de administración */
/* Creado para mejorar la organización y mantenimiento del código */

/* ==================== VARIABLES CSS ==================== */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;

    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;

    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2c3e50;

    --border-color: #e9ecef;
    --border-radius: 12px;
    --border-radius-lg: 20px;

    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.2);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.25);

    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.5s ease;

    --purple-primary: #667eea;
    --purple-secondary: #764ba2;

    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ==================== RESET Y BASE ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
}

/* ==================== LAYOUT PRINCIPAL ==================== */
.admin-sidebar {
    width: 280px;
    min-width: 280px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    height: 100vh;
    overflow-y: auto;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 1000;
}

.admin-content {
    flex: 1;
    padding: 0;
    background: transparent;
    min-height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}

/* Header que va dentro del contenido principal */
.admin-header {
    background: white;
    padding: 20px 30px;
    border-radius: 0;
    box-shadow: var(--shadow-md);
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

/* Contenido principal que va después del header */
main.admin-content {
    padding: 30px;
    flex: 1;
}

/* ==================== SIDEBAR STYLES ==================== */
.admin-logo {
    padding: 30px 25px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.admin-logo h2 {
    font-size: 24px;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.admin-logo p {
    font-size: 12px;
    color: rgba(255,255,255,0.7);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.admin-menu {
    padding: 20px 0;
    list-style: none;
    margin: 0;
}

.admin-menu-section {
    padding: 15px 25px 10px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255,255,255,0.5);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 10px;
}

.admin-menu-item {
    margin-bottom: 5px;
}

.admin-menu-link {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    font-weight: 500;
}

.admin-menu-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(5px);
}

.admin-menu-link.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.admin-menu-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: white;
}

.admin-menu-icon {
    width: 20px;
    margin-right: 15px;
    text-align: center;
}

.admin-menu-badge {
    background: #e74c3c;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    font-weight: 600;
}

/* ==================== HEADER STYLES ==================== */
.admin-header {
    background: white;
    padding: 20px 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-header-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-header-title h1 {
    font-size: 28px;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
}

.admin-header-title .header-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.admin-header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-search {
    position: relative;
}

.admin-search input {
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    width: 300px;
    transition: var(--transition);
}

.admin-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.admin-search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.admin-user-menu {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.admin-user-menu:hover {
    background: var(--bg-secondary);
}

.admin-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

/* ==================== CONTENT STYLES ==================== */
.admin-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.admin-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.admin-section:hover {
    box-shadow: var(--shadow-lg);
}

.admin-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--bg-secondary);
}

.admin-section-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.admin-section-title i {
    color: var(--primary-color);
}

/* ==================== BUTTON STYLES ==================== */
.admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.admin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.admin-btn:hover::before {
    left: 100%;
}

.admin-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.admin-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.admin-btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.admin-btn-secondary:hover {
    background: var(--border-color);
    transform: translateY(-1px);
}

.admin-btn-success {
    background: linear-gradient(135deg, var(--success-color), #45a049);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.admin-btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.admin-btn-danger {
    background: linear-gradient(135deg, var(--error-color), #d32f2f);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.admin-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.admin-btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.admin-btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* ==================== FORM STYLES ==================== */
.admin-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.admin-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.admin-form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.admin-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-input,
.admin-textarea,
.admin-select {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: white;
    font-family: inherit;
}

.admin-input:focus,
.admin-textarea:focus,
.admin-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.admin-textarea {
    min-height: 100px;
    resize: vertical;
}

.admin-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.admin-input-icon {
    position: absolute;
    left: 15px;
    color: var(--text-secondary);
    z-index: 2;
}

.admin-input-group .admin-input {
    padding-left: 45px;
}

/* ==================== TABLE STYLES ==================== */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.admin-table th,
.admin-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.admin-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-table tr:hover {
    background: var(--bg-secondary);
}

.admin-table-actions {
    display: flex;
    gap: 8px;
}

.admin-table-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 12px;
}

.admin-table-btn-edit {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

.admin-table-btn-edit:hover {
    background: var(--info-color);
    color: white;
}

.admin-table-btn-delete {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

.admin-table-btn-delete:hover {
    background: var(--error-color);
    color: white;
}

/* ==================== CARD STYLES ==================== */
.admin-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.admin-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.admin-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.admin-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.admin-card-body {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ==================== MODAL STYLES ==================== */
.admin-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.6);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.admin-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.admin-modal {
    background: white;
    border-radius: var(--border-radius-lg);
    width: 600px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transform: translateY(20px) scale(0.95);
    transition: var(--transition);
}

.admin-modal-overlay.active .admin-modal {
    transform: translateY(0) scale(1);
}

.admin-modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.admin-modal-title {
    font-size: 22px;
    font-weight: 700;
    margin: 0;
}

.admin-modal-close {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    transition: var(--transition);
}

.admin-modal-close:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.admin-modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.admin-modal-footer {
    padding: 20px 30px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: var(--bg-secondary);
}

/* ==================== TABS STYLES ==================== */
.admin-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 25px;
}

.admin-tab {
    padding: 15px 25px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--text-secondary);
    transition: var(--transition);
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-tab:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.admin-tab.active {
    color: var(--primary-color);
}

.admin-tab.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.admin-tab-content {
    display: none;
}

.admin-tab-content.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

/* ==================== BADGE STYLES ==================== */
.admin-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-badge-primary {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.admin-badge-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.admin-badge-warning {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.admin-badge-danger {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

.admin-badge-info {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

/* ==================== ALERT STYLES ==================== */
.admin-alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
}

.admin-alert-success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.admin-alert-warning {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.admin-alert-danger {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
}

.admin-alert-info {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* ==================== LOADING STYLES ==================== */
.admin-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--text-secondary);
}

.admin-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== PROGRESS STYLES ==================== */
.admin-progress {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.admin-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* ==================== TOOLTIP STYLES ==================== */
.admin-tooltip {
    position: relative;
    cursor: help;
}

.admin-tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-dark);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.admin-tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--bg-dark);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.admin-tooltip:hover::before,
.admin-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* ==================== KPI STYLES ==================== */
.admin-kpi-container {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-md);
    margin-bottom: 30px;
}

.admin-kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.admin-kpi-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.admin-kpi-period {
    display: flex;
    gap: 8px;
}

.admin-kpi-period-btn {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    transition: var(--transition);
}

.admin-kpi-period-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.admin-kpi-period-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.admin-kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.admin-kpi-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.admin-kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.admin-kpi-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.admin-kpi-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.admin-kpi-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.admin-kpi-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
}

.admin-kpi-trend.positive {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.admin-kpi-trend.negative {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

.admin-kpi-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    font-weight: 500;
}

.admin-kpi-value {
    font-size: 32px;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: 10px;
}

.admin-kpi-description {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* ==================== CHART STYLES ==================== */
.admin-charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.admin-chart-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.admin-chart-card:hover {
    box-shadow: var(--shadow-md);
}

.admin-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.admin-chart-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.admin-chart-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.admin-chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.line-chart svg,
.bar-chart svg,
.donut-chart svg,
.bubble-chart svg {
    width: 100%;
    height: 100%;
}

.line-chart path {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 2;
    stroke-linecap: round;
}

.line-chart path:last-child {
    fill: rgba(102, 126, 234, 0.1);
    stroke: none;
}

.bar-chart rect {
    fill: var(--primary-color);
    transition: var(--transition);
}

.bar-chart rect:hover {
    fill: var(--secondary-color);
}

.donut-chart circle:first-child {
    fill: none;
    stroke: var(--border-color);
    stroke-width: 4;
}

.donut-chart circle:nth-child(2) {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 4;
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: center;
}

.donut-chart text {
    text-anchor: middle;
    dominant-baseline: middle;
    font-weight: 700;
    font-size: 14px;
    fill: var(--text-primary);
}

.bubble-chart circle {
    fill: var(--primary-color);
    opacity: 0.7;
    transition: var(--transition);
}

.bubble-chart circle:hover {
    opacity: 1;
    fill: var(--secondary-color);
}

/* ==================== PRODUCTS STYLES ==================== */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.product-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
}

.product-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-overlay-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: white;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.product-overlay-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.product-details {
    padding: 20px;
}

.product-category {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.product-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
    line-height: 1.3;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
}

.current-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.original-price {
    font-size: 14px;
    color: var(--text-secondary);
    text-decoration: line-through;
}

.product-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-active {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.badge-inactive {
    background: rgba(158, 158, 158, 0.1);
    color: #9e9e9e;
}

.product-toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.product-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.product-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.product-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.product-toggle input:checked + .product-toggle-slider {
    background-color: var(--primary-color);
}

.product-toggle input:checked + .product-toggle-slider:before {
    transform: translateX(20px);
}

.add-product-card {
    background: var(--bg-secondary);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.add-product-card:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.add-product-card i {
    font-size: 48px;
    margin-bottom: 15px;
}

.add-product-card span {
    font-size: 16px;
    font-weight: 600;
}

/* ==================== NUEVA SECCIÓN DE CATEGORÍAS ULTRA MODERNA ==================== */

.modern-categories-container {
    margin-bottom: 40px;
    position: relative;
}

/* Hero Section con Glassmorphism */
.categories-hero-section {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px;
    padding: 60px 40px;
    margin-bottom: 30px;
    overflow: hidden;
    min-height: 300px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.hero-text {
    flex: 1;
}

.hero-title {
    font-size: 48px;
    font-weight: 800;
    color: white;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 16px;
    text-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

.hero-title i {
    font-size: 40px;
    background: rgba(255,255,255,0.2);
    padding: 12px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.hero-subtitle {
    font-size: 18px;
    color: rgba(255,255,255,0.9);
    margin: 0 0 32px 0;
    line-height: 1.6;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 32px;
    margin-bottom: 32px;
}

.hero-stat {
    text-align: center;
}

.hero-stat .stat-value {
    display: block;
    font-size: 32px;
    font-weight: 800;
    color: white;
    line-height: 1;
    margin-bottom: 4px;
}

.hero-stat .stat-label {
    font-size: 14px;
    color: rgba(255,255,255,0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: flex-end;
}

/* Botones Neo-morphism */
.neo-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    border: none;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
}

.neo-btn-primary {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 8px 32px rgba(238, 90, 36, 0.3);
}

.neo-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(238, 90, 36, 0.4);
}

.neo-btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.neo-btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.neo-btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.neo-btn-outline:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.neo-btn-primary:hover .btn-glow {
    left: 100%;
}

/* Panel de Control Rápido */
.quick-controls-panel {
    background: white;
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 32px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.control-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    white-space: nowrap;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 4px;
}

.view-btn {
    padding: 8px 12px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    font-size: 16px;
}

.view-btn.active,
.view-btn:hover {
    background: white;
    color: var(--purple-primary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.modern-select {
    padding: 10px 16px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: white;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    min-width: 150px;
}

.modern-select:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-group {
    margin-left: auto;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 16px;
    color: var(--text-secondary);
    font-size: 16px;
    z-index: 2;
}

.search-input {
    padding: 12px 16px 12px 48px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: white;
    color: var(--text-primary);
    font-size: 14px;
    width: 280px;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-search {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
}

.search-input:not(:placeholder-shown) + .clear-search {
    opacity: 1;
    visibility: visible;
}

.clear-search:hover {
    background: #f8f9fa;
    color: var(--text-primary);
}

/* Área de Visualización de Categorías */
.categories-display-area {
    min-height: 400px;
    position: relative;
}

/* ==================== MODAL ULTRA MODERNO ==================== */

.ultra-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.ultra-modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.ultra-modal {
    background: white;
    border-radius: 24px;
    width: 900px;
    max-width: 95vw;
    max-height: 95vh;
    overflow: hidden;
    box-shadow: 0 32px 64px rgba(0,0,0,0.2);
    transform: translateY(40px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.ultra-modal-overlay.active .ultra-modal {
    transform: translateY(0) scale(1);
}

/* Header con Glassmorphism */
.ultra-modal-header {
    position: relative;
    padding: 32px;
    overflow: hidden;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.9;
}

.header-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.header-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 20px;
}

.modal-avatar {
    width: 64px;
    height: 64px;
    background: rgba(255,255,255,0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.modal-title-area {
    flex: 1;
}

.modal-title {
    font-size: 28px;
    font-weight: 800;
    color: white;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.modal-subtitle {
    font-size: 16px;
    color: rgba(255,255,255,0.9);
    margin: 0;
    line-height: 1.4;
}

.ultra-close-btn {
    width: 48px;
    height: 48px;
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 16px;
    color: white;
    cursor: pointer;
    font-size: 20px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.ultra-close-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.05);
}

/* Navegación por Pasos */
.modal-steps-nav {
    display: flex;
    justify-content: center;
    padding: 24px 32px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    gap: 32px;
}

.step {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: var(--transition);
    padding: 8px 16px;
    border-radius: 12px;
    position: relative;
}

.step.active {
    background: rgba(102, 126, 234, 0.1);
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #e9ecef;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    transition: var(--transition);
}

.step.active .step-number {
    background: var(--purple-primary);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.step-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 14px;
    transition: var(--transition);
}

.step.active .step-label {
    color: var(--purple-primary);
}

/* Contenido del Modal */
.ultra-modal-body {
    padding: 32px;
    max-height: 60vh;
    overflow-y: auto;
}

.ultra-form {
    position: relative;
}

.form-step {
    display: none;
    animation: fadeInUp 0.4s ease;
}

.form-step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 32px;
}

.step-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.step-header p {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.form-row {
    margin-bottom: 24px;
}

.input-group {
    position: relative;
}

.ultra-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 8px;
}

.input-wrapper,
.textarea-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    color: var(--text-secondary);
    font-size: 16px;
    z-index: 2;
}

.ultra-input,
.ultra-textarea {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    font-size: 16px;
    background: white;
    transition: var(--transition);
    position: relative;
}

.ultra-input:focus,
.ultra-textarea:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.ultra-textarea {
    padding-top: 16px;
    resize: vertical;
    min-height: 120px;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--purple-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.ultra-input:focus + .input-border,
.ultra-textarea:focus + .input-border {
    transform: scaleX(1);
}

.input-help {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
    font-style: italic;
}

/* Grid de Iconos */
.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 16px;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.icon-item:hover {
    border-color: var(--purple-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.icon-item.active {
    border-color: var(--purple-primary);
    background: rgba(102, 126, 234, 0.05);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.icon-item i {
    font-size: 24px;
    color: var(--purple-primary);
}

.icon-item span {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
}

/* Paleta de Colores */
.color-palette {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.color-item {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    border: 3px solid transparent;
    position: relative;
}

.color-item:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.color-item.active {
    border-color: white;
    box-shadow: 0 0 0 2px var(--purple-primary);
    transform: scale(1.1);
}

.color-item.active::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 800;
    font-size: 16px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Constructor de Subcategorías */
.subcategory-builder {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 24px;
    margin-top: 16px;
}

.add-subcategory-section {
    margin-bottom: 20px;
}

.add-sub-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: var(--purple-primary);
    border: none;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-sub-btn:hover {
    background: #5a67d8;
    transform: translateY(-50%) scale(1.05);
}

.subcategories-list {
    min-height: 120px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120px;
    color: var(--text-secondary);
    text-align: center;
}

.empty-state i {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-state p {
    font-weight: 600;
    margin: 0 0 4px 0;
}

.empty-state small {
    font-size: 12px;
    opacity: 0.8;
}

.subcategory-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    transition: var(--transition);
}

.subcategory-item:hover {
    border-color: var(--purple-primary);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.subcategory-name {
    font-weight: 600;
    color: var(--text-primary);
}

.remove-subcategory {
    width: 24px;
    height: 24px;
    background: #f44336;
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.remove-subcategory:hover {
    background: #d32f2f;
    transform: scale(1.1);
}

/* Tarjetas de Configuración */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 16px;
}

.config-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    overflow: hidden;
    transition: var(--transition);
}

.config-card:hover {
    border-color: var(--purple-primary);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.config-header {
    background: #f8f9fa;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #e9ecef;
}

.config-header i {
    font-size: 18px;
    color: var(--purple-primary);
}

.config-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.config-content {
    padding: 20px;
}

.toggle-group {
    margin-bottom: 16px;
}

.toggle-group:last-child {
    margin-bottom: 0;
}

.ultra-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.ultra-toggle input {
    display: none;
}

.toggle-slider {
    width: 48px;
    height: 24px;
    background: #e9ecef;
    border-radius: 24px;
    position: relative;
    transition: var(--transition);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ultra-toggle input:checked + .toggle-slider {
    background: var(--purple-primary);
}

.ultra-toggle input:checked + .toggle-slider::before {
    transform: translateX(24px);
}

.toggle-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

/* Footer del Modal */
.ultra-modal-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 24px 32px;
}

.footer-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.prev-btn {
    background: #6c757d;
    color: white;
}

.prev-btn:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-1px);
}

.prev-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.next-btn {
    background: var(--purple-primary);
    color: white;
}

.next-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.save-btn {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.step-indicator {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 14px;
}

.current-step {
    color: var(--purple-primary);
}

/* ==================== RESPONSIVE DESIGN ==================== */

/* Tablets */
@media (max-width: 1024px) {
    .admin-sidebar {
        width: 250px;
    }

    .admin-main {
        margin-left: 250px;
        padding: 20px;
    }

    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 32px;
    }

    .hero-actions {
        flex-direction: row;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .quick-controls-panel {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }

    .control-group {
        justify-content: space-between;
    }

    .search-group {
        margin-left: 0;
    }

    .ultra-modal {
        width: 95vw;
        max-height: 90vh;
    }

    .ultra-modal-header {
        padding: 24px;
    }

    .modal-title {
        font-size: 24px;
    }

    .modal-steps-nav {
        padding: 16px 24px;
        gap: 16px;
    }

    .step-label {
        display: none;
    }

    .ultra-modal-body {
        padding: 24px;
    }

    .icon-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 12px;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .admin-sidebar.open {
        transform: translateX(0);
    }

    .admin-main {
        margin-left: 0;
        padding: 15px;
    }

    .admin-header {
        padding: 15px 20px;
        margin-bottom: 20px;
    }

    .admin-header-title h1 {
        font-size: 24px;
    }

    .admin-search input {
        width: 200px;
    }

    .categories-hero-section {
        padding: 40px 24px;
    }

    .hero-title {
        font-size: 36px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 16px;
    }

    .hero-actions {
        width: 100%;
    }

    .neo-btn {
        width: 100%;
    }

    .search-input {
        width: 100%;
    }

    .ultra-modal {
        width: 100vw;
        height: 100vh;
        max-height: 100vh;
        border-radius: 0;
    }

    .ultra-modal-header {
        padding: 20px;
    }

    .modal-title {
        font-size: 20px;
    }

    .modal-subtitle {
        font-size: 14px;
    }

    .modal-steps-nav {
        padding: 12px 20px;
        gap: 8px;
    }

    .step {
        padding: 4px 8px;
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .ultra-modal-body {
        padding: 20px;
        max-height: calc(100vh - 300px);
    }

    .step-header h3 {
        font-size: 20px;
    }

    .icon-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }

    .icon-item {
        padding: 12px 8px;
    }

    .icon-item i {
        font-size: 20px;
    }

    .icon-item span {
        font-size: 10px;
    }

    .color-palette {
        gap: 8px;
    }

    .color-item {
        width: 40px;
        height: 40px;
    }

    .ultra-modal-footer {
        padding: 16px 20px;
    }

    .footer-navigation {
        flex-direction: column;
        gap: 12px;
    }

    .nav-btn {
        width: 100%;
        justify-content: center;
    }

    .step-indicator {
        order: -1;
    }

    .admin-kpi-grid {
        grid-template-columns: 1fr;
    }

    .admin-charts-grid {
        grid-template-columns: 1fr;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }
}

/* ==================== ANIMATIONS ==================== */

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.icon-item:hover {
    animation: pulse 0.3s ease;
}

.color-item:hover {
    animation: pulse 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.admin-card,
.admin-kpi-card,
.product-card {
    animation: slideInUp 0.6s ease;
}

.admin-menu-item {
    animation: slideInLeft 0.4s ease;
}

.admin-menu-item:nth-child(2) { animation-delay: 0.1s; }
.admin-menu-item:nth-child(3) { animation-delay: 0.2s; }
.admin-menu-item:nth-child(4) { animation-delay: 0.3s; }
.admin-menu-item:nth-child(5) { animation-delay: 0.4s; }

/* ==================== UTILITY CLASSES ==================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--border-radius); }
.rounded-lg { border-radius: var(--border-radius-lg); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.transition { transition: var(--transition); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }

/* ==================== RESPONSIVE LAYOUT ==================== */

@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        min-width: 100%;
        height: auto;
        position: fixed;
        top: 0;
        left: -100%;
        z-index: 9999;
        transition: left 0.3s ease;
    }

    .admin-sidebar.active {
        left: 0;
    }

    .admin-content {
        width: 100%;
        margin-left: 0;
    }

    .admin-header {
        padding: 15px 20px;
        position: relative;
    }

    .admin-header-title h1 {
        font-size: 20px;
    }

    .admin-search input {
        width: 200px;
    }

    main.admin-content {
        padding: 20px;
    }

    /* Botón de menú móvil */
    .admin-menu-toggle {
        display: block;
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 10000;
        background: var(--primary-color);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 8px;
        cursor: pointer;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }
}

@media (min-width: 769px) {
    .admin-menu-toggle {
        display: none;
    }
}

/* ==================== PRINT STYLES ==================== */

@media print {
    .admin-sidebar,
    .admin-header-actions,
    .admin-btn,
    .ultra-modal-overlay,
    .admin-menu-toggle {
        display: none !important;
    }

    body {
        flex-direction: column;
    }

    .admin-content {
        width: 100%;
        margin-left: 0;
        padding: 0;
    }

    main.admin-content {
        padding: 0;
    }

    .admin-section {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }

    .admin-kpi-card,
    .admin-chart-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
