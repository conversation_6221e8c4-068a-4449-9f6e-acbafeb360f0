<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - Tienda</title>
    <!-- Importación de fuentes -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Importación de iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Archivo CSS externo -->
    <link rel="stylesheet" href="admin.css">
    <!-- Estilos adicionales para funcionalidades separadas -->
    <link rel="stylesheet" href="admin-additional-styles.css">



</head>
<body>
    <!-- Botón de menú móvil -->
    <button class="admin-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="admin-logo">
            <h2>AdminPanel</h2>
            <p>Dashboard</p>
        </div>
        <nav class="admin-menu">
            <div class="admin-menu-section">Principal</div>
            <ul class="admin-menu">
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link active">
                        <i class="admin-menu-icon fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link">
                        <i class="admin-menu-icon fas fa-shopping-bag"></i>
                        <span>Productos</span>
                    </a>
                </li>
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link">
                        <i class="admin-menu-icon fas fa-tags"></i>
                        <span>Categorías</span>
                    </a>
                </li>
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link">
                        <i class="admin-menu-icon fas fa-percentage"></i>
                        <span>Ofertas</span>
                    </a>
                </li>
            </ul>
            <div class="admin-menu-section">Configuración</div>
            <ul class="admin-menu">
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link">
                        <i class="admin-menu-icon fas fa-store"></i>
                        <span>Información de Tienda</span>
                    </a>
                </li>
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link">
                        <i class="admin-menu-icon fas fa-user"></i>
                        <span>Perfil</span>
                    </a>
                </li>
                <li class="admin-menu-item">
                    <a href="#" class="admin-menu-link">
                        <i class="admin-menu-icon fas fa-sign-out-alt"></i>
                        <span>Cerrar Sesión</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Contenido principal -->
    <div class="admin-content">
        <!-- Header -->
        <header class="admin-header">
            <div class="admin-header-title">
                <div class="header-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h1>Panel de Administración</h1>
            </div>
            <div class="admin-header-actions">
                <div class="admin-search">
                    <input type="text" placeholder="Buscar...">
                    <i class="admin-search-icon fas fa-search"></i>
                </div>
                <div class="admin-user-menu">
                    <div class="admin-user-avatar">A</div>
                    <span>Admin</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </header>

        <!-- Contenido principal -->
        <main class="admin-content">
    <!-- Contenedor de KPIs -->
    <div class="container-kpi">
        <h2 class="container-title">Dashboard de Indicadores</h2>
        <div class="admin-kpi-dashboard">
            <div class="admin-kpi-header">
                <div class="admin-kpi-period">
                    <button class="admin-kpi-period-btn active">Hoy</button>
                    <button class="admin-kpi-period-btn">Semana</button>
                    <button class="admin-kpi-period-btn">Mes</button>
                    <button class="admin-kpi-period-btn">Año</button>
                </div>
            </div>
            <div class="admin-kpi-grid">
                <!-- KPI 1: Gráfico de línea -->
                <div class="admin-kpi-card">
                    <div class="admin-kpi-chart line-chart">
                        <svg width="100%" height="40" viewBox="0 0 100 40" preserveAspectRatio="none">
                            <path d="M0,35 L10,32 L20,28 L30,25 L40,22 L50,20 L60,18 L70,15 L80,12 L90,8 L100,5" stroke="rgba(106, 27, 154, 0.7)" stroke-width="2" fill="none"></path>
                            <path d="M0,35 L10,32 L20,28 L30,25 L40,22 L50,20 L60,18 L70,15 L80,12 L90,8 L100,5 L100,40 L0,40" fill="rgba(106, 27, 154, 0.1)"></path>
                        </svg>
                    </div>
                    <div class="admin-kpi-info">
                        <div class="admin-kpi-label">Visitas a la página</div>
                        <div class="admin-kpi-data">
                            <div class="admin-kpi-value">1,254</div>
                            <div class="admin-kpi-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>12.5%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- KPI 2: Gráfico de barras -->
                <div class="admin-kpi-card">
                    <div class="admin-kpi-chart bar-chart">
                        <svg width="100%" height="40" viewBox="0 0 100 40" preserveAspectRatio="none">
                            <rect x="5" y="30" width="6" height="10" fill="rgba(106, 27, 154, 0.3)"></rect>
                            <rect x="15" y="25" width="6" height="15" fill="rgba(106, 27, 154, 0.4)"></rect>
                            <rect x="25" y="20" width="6" height="20" fill="rgba(106, 27, 154, 0.5)"></rect>
                            <rect x="35" y="15" width="6" height="25" fill="rgba(106, 27, 154, 0.6)"></rect>
                            <rect x="45" y="10" width="6" height="30" fill="rgba(106, 27, 154, 0.7)"></rect>
                            <rect x="55" y="15" width="6" height="25" fill="rgba(106, 27, 154, 0.6)"></rect>
                            <rect x="65" y="5" width="6" height="35" fill="rgba(106, 27, 154, 0.8)"></rect>
                            <rect x="75" y="10" width="6" height="30" fill="rgba(106, 27, 154, 0.7)"></rect>
                            <rect x="85" y="5" width="6" height="35" fill="rgba(106, 27, 154, 0.8)"></rect>
                        </svg>
                    </div>
                    <div class="admin-kpi-info">
                        <div class="admin-kpi-label">Visitas desde móvil</div>
                        <div class="admin-kpi-data">
                            <div class="admin-kpi-value">68%</div>
                            <div class="admin-kpi-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>5.3%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- KPI 3: Gráfico de donut -->
                <div class="admin-kpi-card">
                    <div class="admin-kpi-chart donut-chart">
                        <svg width="40" height="40" viewBox="0 0 40 40">
                            <circle cx="20" cy="20" r="15" fill="none" stroke="rgba(106, 27, 154, 0.1)" stroke-width="5"></circle>
                            <circle cx="20" cy="20" r="15" fill="none" stroke="rgba(106, 27, 154, 0.7)" stroke-width="5" stroke-dasharray="70.5 23.5" stroke-dashoffset="0"></circle>
                            <text x="20" y="23" text-anchor="middle" font-size="10" font-weight="bold" fill="rgba(106, 27, 154, 0.7)">75%</text>
                        </svg>
                    </div>
                    <div class="admin-kpi-info">
                        <div class="admin-kpi-label">Tiempo promedio</div>
                        <div class="admin-kpi-data">
                            <div class="admin-kpi-value">3m 42s</div>
                            <div class="admin-kpi-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>0:45</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- KPI 6: Gráfico de burbujas -->
                <div class="admin-kpi-card">
                    <div class="admin-kpi-chart bubble-chart">
                        <svg width="100%" height="40" viewBox="0 0 100 40">
                            <circle cx="15" cy="20" r="5" fill="rgba(106, 27, 154, 0.3)"></circle>
                            <circle cx="30" cy="15" r="7" fill="rgba(106, 27, 154, 0.4)"></circle>
                            <circle cx="50" cy="25" r="10" fill="rgba(106, 27, 154, 0.5)"></circle>
                            <circle cx="70" cy="10" r="6" fill="rgba(106, 27, 154, 0.4)"></circle>
                            <circle cx="85" cy="20" r="8" fill="rgba(106, 27, 154, 0.6)"></circle>
                        </svg>
                    </div>
                    <div class="admin-kpi-info">
                        <div class="admin-kpi-label">Productos compartidos</div>
                        <div class="admin-kpi-data">
                            <div class="admin-kpi-value">42</div>
                            <div class="admin-kpi-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>15.7%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">Total Productos</div>
                <div class="stat-value">124</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>12% desde el mes pasado</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Categorías</div>
                <div class="stat-value">8</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>2 nuevas este mes</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Productos en Oferta</div>
                <div class="stat-value">32</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>5% desde el mes pasado</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Productos Destacados</div>
                <div class="stat-value">16</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>4 nuevos este mes</span>
                </div>
            </div>
        </div>
    </div>

<!-- Contenedor de productos -->
    <div class="container-products">
        <h2 class="container-title">Gestión de Productos</h2>
        <div class="admin-card">
            <div class="admin-card-body">
                <div class="admin-tabs">
                    <div class="admin-tab active" data-tab="destacados">Destacados</div>
                    <div class="admin-tab" data-tab="ofertas">Ofertas</div>
                    <div class="admin-tab" data-tab="novedades">Novedades</div>
                    <div class="admin-tab" data-tab="mas-vistos">Más Vistos</div>
                    <div class="admin-tab" data-tab="tendencias">Tendencias</div>
                    <div class="admin-tab" data-tab="liquidaciones">Liquidaciones</div>
                </div>

                <!-- Contenido de pestañas -->
                <div class="admin-tab-content active" id="destacados">
                    <div class="products-grid">
                        <!-- Tarjeta para agregar producto -->
                        <div class="product-card add-product-card">
                            <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                    <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                </button>
                            </div>
                            <div class="product-details" style="text-align: center;">
                                <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto destacado</div>
                            </div>
                        </div>

                        <!-- Producto 1 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Smartphone Premium">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Electrónica / Smartphones</div>
                                <h3 class="product-name">Smartphone Premium X12 Pro</h3>
                                <div class="product-price">
                                    <span class="current-price">$399.990</span>
                                    <span class="original-price">$469.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Destacado</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Producto 2 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Audífonos Inalámbricos">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Electrónica / Audio</div>
                                <h3 class="product-name">Audífonos Inalámbricos SoundPlus</h3>
                                <div class="product-price">
                                    <span class="current-price">$89.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Destacado</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Producto 3 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Smartwatch Fitness">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Electrónica / Wearables</div>
                                <h3 class="product-name">Smartwatch Fitness Tracker Pro</h3>
                                <div class="product-price">
                                    <span class="current-price">$79.990</span>
                                    <span class="original-price">$99.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Destacado</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="admin-tab-content" id="ofertas">
                    <div class="products-grid">
                        <!-- Tarjeta para agregar producto -->
                        <div class="product-card add-product-card">
                            <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                    <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                </button>
                            </div>
                            <div class="product-details" style="text-align: center;">
                                <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en oferta</div>
                            </div>
                        </div>

                        <!-- Producto 1 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Tablet Ultra">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Electrónica / Tablets</div>
                                <h3 class="product-name">Tablet Ultra 10.5" 128GB</h3>
                                <div class="product-price">
                                    <span class="current-price">$199.990</span>
                                    <span class="original-price">$269.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Oferta</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Producto 2 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Laptop Gaming">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Computación / Laptops</div>
                                <h3 class="product-name">Laptop Gaming Pro 15.6"</h3>
                                <div class="product-price">
                                    <span class="current-price">$899.990</span>
                                    <span class="original-price">$1.099.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Oferta</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Producto 3 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Smart TV">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Electrónica / Televisores</div>
                                <h3 class="product-name">Smart TV 4K 55" UltraHD</h3>
                                <div class="product-price">
                                    <span class="current-price">$499.990</span>
                                    <span class="original-price">$649.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Oferta</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="admin-tab-content" id="novedades">
                    <div class="products-grid">
                        <!-- Tarjeta para agregar producto -->
                        <div class="product-card add-product-card">
                            <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                    <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                </button>
                            </div>
                            <div class="product-details" style="text-align: center;">
                                <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto de novedad</div>
                            </div>
                        </div>

                        <!-- Producto 1 -->
                        <div class="product-card">
                            <div class="product-image">
                                <img src="https://via.placeholder.com/300x200" alt="Drone 4K">
                                <div class="product-overlay">
                                    <button class="product-overlay-btn edit-product">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="product-overlay-btn delete-product">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-details">
                                <div class="product-category">Electrónica / Drones</div>
                                <h3 class="product-name">Drone 4K Pro con Cámara Estabilizada</h3>
                                <div class="product-price">
                                    <span class="current-price">$349.990</span>
                                </div>
                                <div class="product-status">
                                    <span class="product-badge badge-active">Novedad</span>
                                    <label class="product-toggle">
                                        <input type="checkbox" checked>
                                        <span class="product-toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

        <!-- Producto 2 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Consola de Videojuegos">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Videojuegos</div>
                <h3 class="product-name">Consola de Videojuegos NextGen</h3>
                <div class="product-price">
                    <span class="current-price">$599.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Novedad</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 3 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Altavoz Inteligente">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Audio</div>
                <h3 class="product-name">Altavoz Inteligente con Asistente Virtual</h3>
                <div class="product-price">
                    <span class="current-price">$129.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Novedad</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div class="admin-tab-content" id="mas-vistos">
    <div class="products-grid">
        <!-- Tarjeta para agregar producto -->
        <div class="product-card add-product-card">
            <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                </button>
            </div>
            <div class="product-details" style="text-align: center;">
                <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto más visto</div>
            </div>
        </div>

        <!-- Producto 1 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Smartphone Premium">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Smartphones</div>
                <h3 class="product-name">Smartphone Premium X12 Pro</h3>
                <div class="product-price">
                    <span class="current-price">$399.990</span>
                    <span class="original-price">$469.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Más Visto</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 2 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Smart TV">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Televisores</div>
                <h3 class="product-name">Smart TV 4K 55" UltraHD</h3>
                <div class="product-price">
                    <span class="current-price">$499.990</span>
                    <span class="original-price">$649.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Más Visto</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 3 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Audífonos Inalámbricos">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Audio</div>
                <h3 class="product-name">Audífonos Inalámbricos SoundPlus</h3>
                <div class="product-price">
                    <span class="current-price">$89.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Más Visto</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div class="admin-tab-content" id="tendencias">
    <div class="products-grid">
        <!-- Tarjeta para agregar producto -->
        <div class="product-card add-product-card">
            <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                </button>
            </div>
            <div class="product-details" style="text-align: center;">
                <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en tendencia</div>
            </div>
        </div>

        <!-- Producto 1 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Drone 4K">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Drones</div>
                <h3 class="product-name">Drone 4K Pro con Cámara Estabilizada</h3>
                <div class="product-price">
                    <span class="current-price">$349.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Tendencia</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 2 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Smartwatch Fitness">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Wearables</div>
                <h3 class="product-name">Smartwatch Fitness Tracker Pro</h3>
                <div class="product-price">
                    <span class="current-price">$79.990</span>
                    <span class="original-price">$99.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Tendencia</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 3 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Consola de Videojuegos">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Videojuegos</div>
                <h3 class="product-name">Consola de Videojuegos NextGen</h3>
                <div class="product-price">
                    <span class="current-price">$599.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Tendencia</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div class="admin-tab-content" id="liquidaciones">
    <div class="products-grid">
        <!-- Tarjeta para agregar producto -->
        <div class="product-card add-product-card">
            <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                </button>
            </div>
            <div class="product-details" style="text-align: center;">
                <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en liquidación</div>
            </div>
        </div>

        <!-- Producto 1 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Laptop Gaming">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Computación / Laptops</div>
                <h3 class="product-name">Laptop Gaming Pro 15.6" (Modelo Anterior)</h3>
                <div class="product-price">
                    <span class="current-price">$699.990</span>
                    <span class="original-price">$1.099.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Liquidación</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 2 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Tablet Ultra">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Tablets</div>
                <h3 class="product-name">Tablet Ultra 9" 64GB (Última Unidad)</h3>
                <div class="product-price">
                    <span class="current-price">$149.990</span>
                    <span class="original-price">$229.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Liquidación</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Producto 3 -->
        <div class="product-card">
            <div class="product-image">
                <img src="https://via.placeholder.com/300x200" alt="Smart TV">
                <div class="product-overlay">
                    <button class="product-overlay-btn edit-product">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="product-overlay-btn delete-product">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="product-details">
                <div class="product-category">Electrónica / Televisores</div>
                <h3 class="product-name">Smart TV 4K 50" (Exhibición)</h3>
                <div class="product-price">
                    <span class="current-price">$399.990</span>
                    <span class="original-price">$599.990</span>
                </div>
                <div class="product-status">
                    <span class="product-badge badge-active">Liquidación</span>
                    <label class="product-toggle">
                        <input type="checkbox" checked>
                        <span class="product-toggle-slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
    </div>
    </div>

<!-- Nueva Sección de Gestión de Categorías -->
<div class="modern-categories-container">
    <!-- Header con diseño glassmorphism -->
    <div class="categories-hero-section">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>

        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    <i class="fas fa-sitemap"></i>
                    Centro de Categorías
                </h1>
                <p class="hero-subtitle">
                    Gestiona y organiza tu catálogo de productos con nuestro sistema inteligente de categorización
                </p>
                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="stat-value">5</span>
                        <span class="stat-label">Categorías Principales</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-value">24</span>
                        <span class="stat-label">Subcategorías</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-value">124</span>
                        <span class="stat-label">Productos Totales</span>
                    </div>
                </div>
            </div>

            <div class="hero-actions">
                <button class="neo-btn neo-btn-primary" id="newCategoryBtn">
                    <i class="fas fa-plus"></i>
                    <span>Nueva Categoría</span>
                    <div class="btn-glow"></div>
                </button>
                <button class="neo-btn neo-btn-secondary" id="importCategoriesBtn">
                    <i class="fas fa-upload"></i>
                    <span>Importar</span>
                </button>
                <button class="neo-btn neo-btn-outline" id="exportCategoriesBtn">
                    <i class="fas fa-download"></i>
                    <span>Exportar</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Panel de Control Rápido -->
    <div class="quick-controls-panel">
        <div class="control-group">
            <label class="control-label">Vista:</label>
            <div class="view-toggle">
                <button class="view-btn active" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
                <button class="view-btn" data-view="list">
                    <i class="fas fa-list"></i>
                </button>
                <button class="view-btn" data-view="tree">
                    <i class="fas fa-sitemap"></i>
                </button>
            </div>
        </div>

        <div class="control-group">
            <label class="control-label">Filtrar:</label>
            <select class="modern-select" id="categoryFilter">
                <option value="all">Todas las categorías</option>
                <option value="active">Solo activas</option>
                <option value="inactive">Solo inactivas</option>
                <option value="empty">Sin productos</option>
            </select>
        </div>

        <div class="control-group">
            <label class="control-label">Ordenar:</label>
            <select class="modern-select" id="categorySorter">
                <option value="name">Por nombre</option>
                <option value="products">Por productos</option>
                <option value="date">Por fecha</option>
                <option value="status">Por estado</option>
            </select>
        </div>

        <div class="control-group search-group">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-input" placeholder="Buscar categorías..." id="categorySearch">
                <button class="clear-search" id="clearSearch">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Grid de Categorías Moderno -->
    <div class="categories-display-area" id="categoriesDisplay">
        <!-- Las categorías se cargarán aquí dinámicamente -->
    </div>
</div>


</main>

<!-- Modal Ultra Moderno para Categorías -->
<div class="ultra-modal-overlay" id="categoryModal">
    <div class="ultra-modal">
        <!-- Header con efecto glassmorphism -->
        <div class="ultra-modal-header">
            <div class="header-background"></div>
            <div class="header-content">
                <div class="modal-avatar">
                    <i class="fas fa-sitemap"></i>
                </div>
                <div class="modal-title-area">
                    <h2 class="modal-title">Crear Nueva Categoría</h2>
                    <p class="modal-subtitle">Organiza tu inventario con categorías inteligentes</p>
                </div>
                <button class="ultra-close-btn" id="closeModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Navegación por pasos -->
        <div class="modal-steps-nav">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <span class="step-label">Información</span>
            </div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <span class="step-label">Diseño</span>
            </div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <span class="step-label">Subcategorías</span>
            </div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <span class="step-label">Configuración</span>
            </div>
        </div>

        <!-- Contenido del modal -->
        <div class="ultra-modal-body">
            <form id="newCategoryForm" class="ultra-form">

                <!-- Paso 1: Información Básica -->
                <div class="form-step active" data-step="1">
                    <div class="step-header">
                        <h3><i class="fas fa-info-circle"></i> Información Básica</h3>
                        <p>Define los datos principales de tu categoría</p>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label class="ultra-label">Nombre de la Categoría</label>
                            <div class="input-wrapper">
                                <i class="fas fa-tag input-icon"></i>
                                <input type="text" class="ultra-input" name="categoryName" placeholder="Ej: Tecnología y Electrónicos" required>
                                <div class="input-border"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label class="ultra-label">Descripción</label>
                            <div class="textarea-wrapper">
                                <i class="fas fa-align-left input-icon"></i>
                                <textarea class="ultra-textarea" name="categoryDescription" placeholder="Describe qué tipo de productos incluirá esta categoría..." rows="4"></textarea>
                                <div class="input-border"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label class="ultra-label">Slug URL</label>
                            <div class="input-wrapper">
                                <i class="fas fa-link input-icon"></i>
                                <input type="text" class="ultra-input" name="categorySlug" placeholder="tecnologia-electronicos" readonly>
                                <div class="input-border"></div>
                            </div>
                            <small class="input-help">Se genera automáticamente desde el nombre</small>
                        </div>
                    </div>
                </div>

                <!-- Paso 2: Diseño Visual -->
                <div class="form-step" data-step="2">
                    <div class="step-header">
                        <h3><i class="fas fa-palette"></i> Diseño Visual</h3>
                        <p>Personaliza la apariencia de tu categoría</p>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label class="ultra-label">Icono Principal</label>
                            <div class="icon-grid" id="iconGrid">
                                <div class="icon-item active" data-icon="fas fa-laptop">
                                    <i class="fas fa-laptop"></i>
                                    <span>Computación</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-mobile-alt">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>Móviles</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-tv">
                                    <i class="fas fa-tv"></i>
                                    <span>TV & Audio</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-gamepad">
                                    <i class="fas fa-gamepad"></i>
                                    <span>Gaming</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-camera">
                                    <i class="fas fa-camera"></i>
                                    <span>Fotografía</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-home">
                                    <i class="fas fa-home"></i>
                                    <span>Hogar</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-tshirt">
                                    <i class="fas fa-tshirt"></i>
                                    <span>Moda</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-car">
                                    <i class="fas fa-car"></i>
                                    <span>Automotriz</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-dumbbell">
                                    <i class="fas fa-dumbbell"></i>
                                    <span>Deportes</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-book">
                                    <i class="fas fa-book"></i>
                                    <span>Libros</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-utensils">
                                    <i class="fas fa-utensils"></i>
                                    <span>Cocina</span>
                                </div>
                                <div class="icon-item" data-icon="fas fa-baby">
                                    <i class="fas fa-baby"></i>
                                    <span>Bebés</span>
                                </div>
                            </div>
                            <input type="hidden" name="categoryIcon" value="fas fa-laptop">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="input-group">
                            <label class="ultra-label">Color de Tema</label>
                            <div class="color-palette">
                                <div class="color-item active" data-color="#667eea" style="background: linear-gradient(135deg, #667eea, #764ba2)"></div>
                                <div class="color-item" data-color="#f093fb" style="background: linear-gradient(135deg, #f093fb, #f5576c)"></div>
                                <div class="color-item" data-color="#4facfe" style="background: linear-gradient(135deg, #4facfe, #00f2fe)"></div>
                                <div class="color-item" data-color="#43e97b" style="background: linear-gradient(135deg, #43e97b, #38f9d7)"></div>
                                <div class="color-item" data-color="#fa709a" style="background: linear-gradient(135deg, #fa709a, #fee140)"></div>
                                <div class="color-item" data-color="#a8edea" style="background: linear-gradient(135deg, #a8edea, #fed6e3)"></div>
                                <div class="color-item" data-color="#ff9a9e" style="background: linear-gradient(135deg, #ff9a9e, #fecfef)"></div>
                                <div class="color-item" data-color="#ffecd2" style="background: linear-gradient(135deg, #ffecd2, #fcb69f)"></div>
                            </div>
                            <input type="hidden" name="categoryColor" value="#667eea">
                        </div>
                    </div>
                </div>

                <!-- Paso 3: Subcategorías -->
                <div class="form-step" data-step="3">
                    <div class="step-header">
                        <h3><i class="fas fa-sitemap"></i> Subcategorías</h3>
                        <p>Organiza productos en subcategorías específicas</p>
                    </div>

                    <div class="subcategory-builder">
                        <div class="add-subcategory-section">
                            <div class="input-group">
                                <div class="input-wrapper">
                                    <i class="fas fa-plus input-icon"></i>
                                    <input type="text" class="ultra-input" id="newSubcategoryInput" placeholder="Nombre de la subcategoría">
                                    <button type="button" class="add-sub-btn" id="addSubBtn">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="subcategories-list" id="subcategoriesList">
                            <div class="empty-state">
                                <i class="fas fa-folder-open"></i>
                                <p>No hay subcategorías aún</p>
                                <small>Agrega subcategorías para organizar mejor tus productos</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Paso 4: Configuración -->
                <div class="form-step" data-step="4">
                    <div class="step-header">
                        <h3><i class="fas fa-cogs"></i> Configuración</h3>
                        <p>Ajusta las opciones avanzadas de la categoría</p>
                    </div>

                    <div class="config-grid">
                        <div class="config-card">
                            <div class="config-header">
                                <i class="fas fa-eye"></i>
                                <h4>Visibilidad</h4>
                            </div>
                            <div class="config-content">
                                <div class="toggle-group">
                                    <label class="ultra-toggle">
                                        <input type="checkbox" name="categoryVisible" checked>
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-label">Categoría visible</span>
                                    </label>
                                </div>
                                <div class="toggle-group">
                                    <label class="ultra-toggle">
                                        <input type="checkbox" name="categoryFeatured">
                                        <span class="toggle-slider"></span>
                                        <span class="toggle-label">Categoría destacada</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <div class="config-header">
                                <i class="fas fa-sort"></i>
                                <h4>Ordenamiento</h4>
                            </div>
                            <div class="config-content">
                                <div class="input-group">
                                    <label class="ultra-label">Orden de visualización</label>
                                    <div class="input-wrapper">
                                        <input type="number" class="ultra-input" name="categoryOrder" value="0" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <div class="config-header">
                                <i class="fas fa-tags"></i>
                                <h4>SEO</h4>
                            </div>
                            <div class="config-content">
                                <div class="input-group">
                                    <label class="ultra-label">Meta descripción</label>
                                    <div class="textarea-wrapper">
                                        <textarea class="ultra-textarea" name="categoryMetaDescription" placeholder="Descripción para motores de búsqueda..." rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Footer con navegación -->
        <div class="ultra-modal-footer">
            <div class="footer-navigation">
                <button type="button" class="nav-btn prev-btn" id="prevStepBtn" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Anterior
                </button>

                <div class="step-indicator">
                    <span class="current-step">1</span> de <span class="total-steps">4</span>
                </div>

                <button type="button" class="nav-btn next-btn" id="nextStepBtn">
                    Siguiente
                    <i class="fas fa-chevron-right"></i>
                </button>

                <button type="button" class="nav-btn save-btn" id="saveCategoryBtn" style="display: none;">
                    <i class="fas fa-save"></i>
                    Crear Categoría
                </button>
            </div>
        </div>
    </div>
</div>



<!-- Modal para agregar/editar producto -->
<div class="admin-modal-overlay" id="productModal">
    <div class="admin-modal">
        <div class="admin-modal-header">
            <h3 class="admin-modal-title">Agregar Nuevo Producto</h3>
            <button class="admin-modal-close">&times;</button>
        </div>
        <div class="admin-modal-body">
            <form id="productForm">
                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Nombre del Producto *</label>
                            <input type="text" class="admin-form-input" name="productName" placeholder="Ej: Smartphone Premium X12 Pro" required>
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Categoría *</label>
                            <select class="admin-form-select" name="category" required>
                                <option value="">Seleccionar categoría</option>
                                <option value="electronica">Electrónica</option>
                                <option value="computacion">Computación</option>
                                <option value="hogar">Hogar</option>
                                <option value="deportes">Deportes</option>
                                <option value="moda">Moda</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Subcategoría *</label>
                            <select class="admin-form-select" name="subcategory" required>
                                <option value="">Seleccionar subcategoría</option>
                            </select>
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Sección de Destino *</label>
                            <select class="admin-form-select" name="targetSection" required>
                                <option value="">Seleccionar sección</option>
                                <option value="destacados">Destacados</option>
                                <option value="ofertas">Ofertas</option>
                                <option value="novedades">Novedades</option>
                                <option value="mas-vistos">Más Vistos</option>
                                <option value="tendencias">Tendencias</option>
                                <option value="liquidaciones">Liquidaciones</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-form-group">
                    <label class="admin-form-label">Reseña del Producto</label>
                    <textarea class="admin-form-textarea" name="productDescription" placeholder="Descripción detallada del producto..."></textarea>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Precio Antes (Opcional)</label>
                            <input type="number" class="admin-form-input" name="originalPrice" placeholder="0" min="0" step="0.01">
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Precio Actual *</label>
                            <input type="number" class="admin-form-input" name="currentPrice" placeholder="0" min="0" step="0.01" required>
                        </div>
                    </div>
                </div>

                <div class="admin-form-row">
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">% de Descuento</label>
                            <input type="number" class="admin-form-input" name="discountPercentage" placeholder="0" min="0" max="100" readonly>
                        </div>
                    </div>
                    <div class="admin-form-col">
                        <div class="admin-form-group">
                            <label class="admin-form-label">Estado</label>
                            <select class="admin-form-select" name="productStatus">
                                <option value="active">Activo</option>
                                <option value="inactive">Inactivo</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-form-group">
                    <label class="admin-form-label">Imagen del Producto (JPG)</label>
                    <div class="admin-form-file">
                        <div class="admin-form-file-preview" id="imagePreview">
                            <i class="fas fa-image" style="font-size: 48px; color: var(--text-secondary);"></i>
                            <p>Vista previa de la imagen</p>
                        </div>
                        <input type="file" class="admin-form-file-input" id="productImage" name="productImage" accept=".jpg,.jpeg">
                        <label for="productImage" class="admin-form-file-btn">
                            <i class="fas fa-upload"></i> Seleccionar imagen JPG
                        </label>
                    </div>
                </div>
            </form>
        </div>
        <div class="admin-modal-footer">
            <button type="button" class="admin-btn admin-btn-secondary" id="cancelProductBtn">Cancelar</button>
            <button type="button" class="admin-btn admin-btn-primary" id="saveProductBtn">
                <i class="fas fa-plus"></i> Agregar Producto
            </button>
        </div>
    </div>
</div>

<!-- Scripts externos del panel de administración -->
<!-- Archivo principal de administración -->
<script src="admin-main.js"></script>

<!-- Sistema de utilidades y funciones auxiliares -->
<script src="admin-utils.js"></script>

<!-- Sistema de configuraciones y funcionalidades del sistema -->
<script src="admin-system.js"></script>

<!-- Gestión de categorías con modal ultra moderno -->
<script src="admin-categories.js"></script>

<!-- Gestión de productos y funcionalidades adicionales -->
<script src="admin-products.js"></script>

<!-- Sistema de KPIs y gráficos -->
<script src="admin-kpis.js"></script>

<!-- Sistema de reportes -->
<script src="admin-reports.js"></script>

<!-- Sistema de animaciones y efectos visuales -->
<script src="admin-animations.js"></script>


































        </main>
    </div>
</body>
</html>
