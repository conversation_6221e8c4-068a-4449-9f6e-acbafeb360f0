/**
 * ADMINISTRADOR DE TIENDA - SCRIPTS PRINCIPALES
 * Sistema moderno de administración para tienda online
 */

// ==================== VARIABLES GLOBALES ====================
let currentSection = 'dashboard';
let currentTab = 'destacados';
let currentPeriod = 'today';

// Datos simulados
const mockData = {
    kpis: {
        today: {
            visits: { value: 1254, change: 12.5, trend: 'positive' },
            time: { value: '3m 42s', change: '0:45s', trend: 'positive' },
            pages: { value: 2.8, change: 0.3, trend: 'positive' },
            bounce: { value: 32, change: -5.2, trend: 'negative' },
            mobile: { value: 68, change: 3.1, trend: 'positive' },
            conversions: { value: 4.2, change: 0.8, trend: 'positive' }
        },
        week: {
            visits: { value: 8750, change: 18.2, trend: 'positive' },
            time: { value: '4m 15s', change: '1:20s', trend: 'positive' },
            pages: { value: 3.1, change: 0.5, trend: 'positive' },
            bounce: { value: 28, change: -8.1, trend: 'negative' },
            mobile: { value: 72, change: 4.2, trend: 'positive' },
            conversions: { value: 5.1, change: 1.2, trend: 'positive' }
        },
        month: {
            visits: { value: 35200, change: 15.8, trend: 'positive' },
            time: { value: '4m 32s', change: '1:45s', trend: 'positive' },
            pages: { value: 3.4, change: 0.7, trend: 'positive' },
            bounce: { value: 25, change: -12.3, trend: 'negative' },
            mobile: { value: 75, change: 6.8, trend: 'positive' },
            conversions: { value: 6.3, change: 2.1, trend: 'positive' }
        },
        year: {
            visits: { value: 425000, change: 24.5, trend: 'positive' },
            time: { value: '5m 18s', change: '2:30s', trend: 'positive' },
            pages: { value: 4.1, change: 1.2, trend: 'positive' },
            bounce: { value: 22, change: -18.5, trend: 'negative' },
            mobile: { value: 78, change: 12.4, trend: 'positive' },
            conversions: { value: 7.8, change: 3.5, trend: 'positive' }
        }
    }
};

// ==================== INICIALIZACIÓN ====================
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Administrador de Tienda iniciado');

    initializeApp();
    setupEventListeners();
    loadDashboardData();
    initializeAnimations();
});

/**
 * Inicializar aplicación
 */
function initializeApp() {
    // Configurar menú móvil
    setupMobileMenu();

    // Configurar navegación
    setupNavigation();

    // Configurar filtros de tiempo
    setupTimeFilters();

    // Configurar pestañas de productos
    setupProductTabs();

    // Mostrar notificación de bienvenida
    setTimeout(() => {
        showNotification('¡Bienvenido al panel de administración!', 'success');
    }, 1000);
}

/**
 * Configurar event listeners
 */
function setupEventListeners() {
    // Botones de acción
    const addProductBtn = document.getElementById('addProductBtn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', openProductModal);
    }

    // Botones de notificación y configuración
    const notificationBtn = document.querySelector('.notification-btn');
    const settingsBtn = document.querySelector('.settings-btn');

    if (notificationBtn) {
        notificationBtn.addEventListener('click', showNotifications);
    }

    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettings);
    }

    // Búsqueda
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // Toggles de productos
    document.addEventListener('change', function(e) {
        if (e.target.matches('.toggle-switch input')) {
            handleProductToggle(e.target);
        }
    });

    // Botones de acción de productos
    document.addEventListener('click', function(e) {
        if (e.target.closest('.action-btn.edit')) {
            editProduct(e.target.closest('.product-card'));
        } else if (e.target.closest('.action-btn.duplicate')) {
            duplicateProduct(e.target.closest('.product-card'));
        } else if (e.target.closest('.action-btn.delete')) {
            deleteProduct(e.target.closest('.product-card'));
        }
    });
}

/**
 * Configurar menú móvil
 */
function setupMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.admin-sidebar');

    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });

        // Cerrar al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !mobileToggle.contains(e.target) &&
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Cerrar con Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });
    }
}

/**
 * Configurar navegación del sidebar
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const section = this.dataset.section;
            if (section) {
                switchSection(section);

                // Actualizar estado activo
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');

                // Cerrar menú móvil si está abierto
                if (window.innerWidth <= 768) {
                    document.querySelector('.admin-sidebar').classList.remove('active');
                }
            }
        });
    });
}

/**
 * Configurar filtros de tiempo
 */
function setupTimeFilters() {
    const timeButtons = document.querySelectorAll('.time-btn');

    timeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            if (period) {
                currentPeriod = period;

                // Actualizar estado activo
                timeButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Actualizar KPIs
                updateKPIs(period);
            }
        });
    });
}

/**
 * Configurar pestañas de productos
 */
function setupProductTabs() {
    const tabButtons = document.querySelectorAll('.admin-tab');

    tabButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.dataset.tab;
            if (tab) {
                currentTab = tab;

                // Actualizar estado activo
                tabButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Mostrar contenido de la pestaña
                showTabContent(tab);
            }
        });
    });
}

/**
 * Cargar datos del dashboard
 */
function loadDashboardData() {
    updateKPIs(currentPeriod);
    loadRecentOrders();
    loadTopProducts();
    loadSystemAlerts();
}

/**
 * Actualizar KPIs
 */
function updateKPIs(period) {
    const data = mockData.kpis[period];
    if (!data) return;

    // Actualizar valores con animación (solo los que existen en mockData)
    updateKPIValue('.visits .kpi-value', data.visits.value);
    updateKPIValue('.mobile .kpi-value', data.mobile.value + '%');
    updateKPIValue('.time .kpi-value', data.time.value);

    // Verificar si existen las propiedades antes de usarlas
    if (data.bounce) {
        updateKPIValue('.shared .kpi-value', data.bounce.value);
        updateKPIChange('.shared .kpi-change', data.bounce.change, data.bounce.trend);
    } else {
        // Valores por defecto si no existen
        updateKPIValue('.shared .kpi-value', 42);
        updateKPIChange('.shared .kpi-change', 15.7, 'positive');
    }

    // Valores fijos para los KPIs que no están en mockData
    updateKPIValue('.total-products .kpi-value', 124);
    updateKPIValue('.categories .kpi-value', 8);
    updateKPIValue('.products-offer .kpi-value', 32);
    updateKPIValue('.products-featured .kpi-value', 16);

    // Actualizar cambios (solo los que existen)
    updateKPIChange('.visits .kpi-change', data.visits.change, data.visits.trend);
    updateKPIChange('.mobile .kpi-change', data.mobile.change, data.mobile.trend);
    updateKPIChange('.time .kpi-change', data.time.change, data.time.trend);

    // Cambios fijos para los otros KPIs
    updateKPIChange('.total-products .kpi-change', '12% desde el mes pasado', 'positive');
    updateKPIChange('.categories .kpi-change', '2 nuevas este mes', 'positive');
    updateKPIChange('.products-offer .kpi-change', '5% desde el mes pasado', 'negative');
    updateKPIChange('.products-featured .kpi-change', '4 nuevos este mes', 'positive');
}

/**
 * Animar valor numérico
 */
function animateValue(selector, endValue, isCurrency = false) {
    const element = document.querySelector(selector);
    if (!element) return;

    const startValue = 0;
    const duration = 1000;
    const startTime = performance.now();

    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const currentValue = Math.floor(startValue + (endValue - startValue) * progress);

        if (isCurrency) {
            element.textContent = formatCurrency(currentValue);
        } else {
            element.textContent = formatNumber(currentValue);
        }

        if (progress < 1) {
            requestAnimationFrame(updateValue);
        }
    }

    requestAnimationFrame(updateValue);
}

/**
 * Actualizar indicador de cambio KPI
 */
function updateKPIChange(selector, change, trend) {
    const element = document.querySelector(selector);
    if (!element) return;

    const icon = element.querySelector('i');
    const span = element.querySelector('span');

    // Actualizar clases
    element.className = `kpi-change ${trend}`;

    // Actualizar icono
    if (trend === 'positive') {
        icon.className = 'fas fa-arrow-up';
    } else if (trend === 'negative') {
        icon.className = 'fas fa-arrow-down';
    } else {
        icon.className = 'fas fa-minus';
    }

    // Actualizar texto - manejar tanto números como strings
    if (typeof change === 'string') {
        span.textContent = change;
    } else {
        const prefix = trend === 'positive' ? '+' : trend === 'negative' ? '-' : '';
        span.textContent = `${prefix}${Math.abs(change)}%`;
    }
}

/**
 * Cambiar sección
 */
function switchSection(section) {
    currentSection = section;

    // Actualizar título
    const pageTitle = document.querySelector('.page-title');
    const pageSubtitle = document.querySelector('.page-subtitle');

    if (pageTitle && pageSubtitle) {
        const sectionInfo = getSectionInfo(section);
        pageTitle.textContent = sectionInfo.title;
        pageSubtitle.textContent = sectionInfo.subtitle;
    }

    // Inicializar funcionalidades específicas de la sección
    if (section === 'categorias') {
        setTimeout(() => {
            console.log('Inicializando gestión simple de categorías...');
            initializeSimpleCategoriesManagement();

            // Prueba automática del dropdown
            setTimeout(() => {
                console.log('Ejecutando prueba automática del dropdown...');
                const dropdown = document.getElementById('categoryDropdownMenu');
                const btn = document.getElementById('categoryDropdownBtn');

                if (dropdown && btn) {
                    console.log('Elementos encontrados, cargando categorías...');
                    loadCategoryDropdown();

                    // Verificar que se cargaron las categorías
                    setTimeout(() => {
                        console.log('Contenido del dropdown:', dropdown.innerHTML.length, 'caracteres');
                        if (dropdown.innerHTML.length > 0) {
                            console.log('✅ Categorías cargadas correctamente');
                        } else {
                            console.error('❌ No se cargaron las categorías');
                        }
                    }, 100);
                } else {
                    console.error('❌ No se encontraron los elementos del dropdown');
                }
            }, 200);
        }, 100);
    }

    // Aquí se podría implementar la lógica para mostrar/ocultar secciones
    console.log(`Cambiando a sección: ${section}`);
}

/**
 * Obtener información de la sección
 */
function getSectionInfo(section) {
    const sections = {
        dashboard: { title: 'Dashboard', subtitle: 'Resumen general de tu tienda' },
        productos: { title: 'Productos', subtitle: 'Gestiona tu catálogo de productos' },
        categorias: { title: 'Categorías', subtitle: 'Organiza tus productos por categorías' },
        inventario: { title: 'Inventario', subtitle: 'Control de stock y almacén' },
        pedidos: { title: 'Pedidos', subtitle: 'Gestiona los pedidos de tus clientes' },
        clientes: { title: 'Clientes', subtitle: 'Administra tu base de clientes' },
        promociones: { title: 'Promociones', subtitle: 'Crea y gestiona ofertas especiales' },
        tienda: { title: 'Configuración', subtitle: 'Ajustes generales de la tienda' },
        reportes: { title: 'Reportes', subtitle: 'Análisis y estadísticas detalladas' }
    };

    return sections[section] || { title: 'Dashboard', subtitle: 'Panel de administración' };
}

/**
 * Obtener etiqueta del período
 */
function getPeriodLabel(period) {
    const labels = {
        today: 'Hoy',
        week: 'Esta semana',
        month: 'Este mes',
        year: 'Este año'
    };
    return labels[period] || 'Hoy';
}

/**
 * Obtener etiqueta de la pestaña
 */
function getTabLabel(tab) {
    const labels = {
        destacados: 'Destacados',
        ofertas: 'Ofertas',
        novedades: 'Novedades',
        'mas-vistos': 'Más Vistos',
        tendencias: 'Tendencias',
        liquidaciones: 'Liquidaciones'
    };
    return labels[tab] || 'Destacados';
}

// ==================== GESTIÓN DE PRODUCTOS ====================

/**
 * Actualizar valor de KPI
 */
function updateKPIValue(selector, value) {
    const element = document.querySelector(selector);
    if (element) {
        element.textContent = typeof value === 'number' ? formatNumber(value) : value;
    }
}

/**
 * Mostrar contenido de pestaña
 */
function showTabContent(tab) {
    // Ocultar todas las pestañas
    const allTabs = document.querySelectorAll('.admin-tab-content');
    allTabs.forEach(tabContent => {
        tabContent.classList.remove('active');
    });

    // Mostrar la pestaña seleccionada
    const selectedTab = document.getElementById(tab);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    console.log(`Mostrando productos de: ${tab}`);
}

/**
 * Abrir modal de producto
 */
function openProductModal() {
    showNotification('Abriendo formulario de nuevo producto...', 'info');

    // Aquí se implementaría la lógica del modal
    console.log('Abriendo modal de producto');
}

/**
 * Editar producto
 */
function editProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    showNotification(`Editando producto: ${productName}`, 'info');

    console.log('Editando producto:', productName);
}

/**
 * Duplicar producto
 */
function duplicateProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    showNotification(`Duplicando producto: ${productName}`, 'success');

    console.log('Duplicando producto:', productName);
}

/**
 * Eliminar producto
 */
function deleteProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;

    if (confirm(`¿Estás seguro de que quieres eliminar "${productName}"?`)) {
        // Animar eliminación
        productCard.style.transform = 'scale(0.8)';
        productCard.style.opacity = '0';

        setTimeout(() => {
            productCard.remove();
            showNotification(`Producto "${productName}" eliminado`, 'success');
        }, 300);
    }
}

/**
 * Manejar toggle de producto
 */
function handleProductToggle(toggle) {
    const productCard = toggle.closest('.product-card');
    const productName = productCard.querySelector('.product-name').textContent;
    const isActive = toggle.checked;

    const status = isActive ? 'activado' : 'desactivado';
    showNotification(`Producto "${productName}" ${status}`, 'info');

    // Animar cambio
    productCard.style.opacity = isActive ? '1' : '0.7';
}

/**
 * Manejar búsqueda
 */
function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    console.log('Buscando:', query);

    if (query.length > 2) {
        showNotification(`Buscando: "${query}"`, 'info');
    }
}

// ==================== DATOS SIMULADOS ====================

// Esta definición de mockData fue eliminada porque está duplicada arriba

// ==================== NOTIFICACIONES ====================

/**
 * Mostrar notificación
 */
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Agregar estilos si no existen
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                padding: 1rem 1.5rem;
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 1rem;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 400px;
                border-left: 4px solid;
            }
            .notification-success { border-left-color: #10b981; }
            .notification-error { border-left-color: #ef4444; }
            .notification-warning { border-left-color: #f59e0b; }
            .notification-info { border-left-color: #3b82f6; }
            .notification.show { transform: translateX(0); }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                flex: 1;
            }
            .notification-content i {
                font-size: 1.25rem;
            }
            .notification-success i { color: #10b981; }
            .notification-error i { color: #ef4444; }
            .notification-warning i { color: #f59e0b; }
            .notification-info i { color: #3b82f6; }
            .notification-close {
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 4px;
                transition: background 0.2s;
            }
            .notification-close:hover {
                background: #f3f4f6;
            }
        `;
        document.head.appendChild(styles);
    }

    // Agregar al DOM
    document.body.appendChild(notification);

    // Mostrar con animación
    setTimeout(() => notification.classList.add('show'), 100);

    // Configurar cierre
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => closeNotification(notification));

    // Auto-cerrar después de 5 segundos
    setTimeout(() => closeNotification(notification), 5000);
}

/**
 * Cerrar notificación
 */
function closeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Obtener icono de notificación
 */
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Mostrar notificaciones del sistema
 */
function showNotifications() {
    showNotification('Tienes 3 notificaciones nuevas', 'info');
}

/**
 * Abrir configuración
 */
function openSettings() {
    showNotification('Abriendo configuración...', 'info');
}

// ==================== UTILIDADES ====================

/**
 * Formatear moneda
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('es-CL', {
        style: 'currency',
        currency: 'CLP',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Formatear número
 */
function formatNumber(number) {
    return new Intl.NumberFormat('es-CL').format(number);
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Inicializar animaciones
 */
function initializeAnimations() {
    // Observador de intersección para animaciones
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, { threshold: 0.1 });

    // Observar elementos animados
    document.querySelectorAll('.kpi-card, .product-card, .summary-card').forEach(el => {
        observer.observe(el);
    });
}

// ==================== NUEVA GESTIÓN DE CATEGORÍAS ====================

// Datos completos de categorías por tipo de comercio
const commerceTypesData = {
    electronics: {
        name: 'Electrónicos',
        categories: {
            smartphones: {
                id: 'smartphones',
                name: 'Smartphones y Celulares',
                icon: 'fas fa-mobile-alt',
                subcategories: [
                    { id: 'android', name: 'Android', icon: 'fab fa-android' },
                    { id: 'iphone', name: 'iPhone', icon: 'fab fa-apple' },
                    { id: 'accesorios-cel', name: 'Accesorios', icon: 'fas fa-headphones' },
                    { id: 'fundas', name: 'Fundas y Protectores', icon: 'fas fa-shield-alt' },
                    { id: 'cargadores', name: 'Cargadores', icon: 'fas fa-plug' }
                ]
            },
            computadoras: {
                id: 'computadoras',
                name: 'Computadoras',
                icon: 'fas fa-desktop',
                subcategories: [
                    { id: 'laptops', name: 'Laptops', icon: 'fas fa-laptop' },
                    { id: 'desktop', name: 'PC Escritorio', icon: 'fas fa-desktop' },
                    { id: 'tablets', name: 'Tablets', icon: 'fas fa-tablet-alt' },
                    { id: 'componentes', name: 'Componentes', icon: 'fas fa-microchip' },
                    { id: 'perifericos', name: 'Periféricos', icon: 'fas fa-keyboard' }
                ]
            },
            audio_video: {
                id: 'audio_video',
                name: 'Audio y Video',
                icon: 'fas fa-headphones',
                subcategories: [
                    { id: 'audifonos', name: 'Audífonos', icon: 'fas fa-headphones' },
                    { id: 'parlantes', name: 'Parlantes', icon: 'fas fa-volume-up' },
                    { id: 'televisores', name: 'Televisores', icon: 'fas fa-tv' },
                    { id: 'camaras', name: 'Cámaras', icon: 'fas fa-camera' },
                    { id: 'streaming', name: 'Streaming', icon: 'fas fa-play-circle' }
                ]
            },
            gaming: {
                id: 'gaming',
                name: 'Gaming',
                icon: 'fas fa-gamepad',
                subcategories: [
                    { id: 'consolas', name: 'Consolas', icon: 'fas fa-gamepad' },
                    { id: 'juegos', name: 'Videojuegos', icon: 'fas fa-compact-disc' },
                    { id: 'accesorios-gaming', name: 'Accesorios Gaming', icon: 'fas fa-mouse' },
                    { id: 'pc-gaming', name: 'PC Gaming', icon: 'fas fa-desktop' },
                    { id: 'realidad-virtual', name: 'Realidad Virtual', icon: 'fas fa-vr-cardboard' }
                ]
            },
            electrodomesticos: {
                id: 'electrodomesticos',
                name: 'Electrodomésticos',
                icon: 'fas fa-blender',
                subcategories: [
                    { id: 'cocina-electro', name: 'Cocina', icon: 'fas fa-blender' },
                    { id: 'lavado', name: 'Lavado', icon: 'fas fa-tshirt' },
                    { id: 'climatizacion', name: 'Climatización', icon: 'fas fa-snowflake' },
                    { id: 'pequenos', name: 'Pequeños Electrodomésticos', icon: 'fas fa-coffee' },
                    { id: 'aspiradoras', name: 'Aspiradoras', icon: 'fas fa-broom' }
                ]
            }
        }
    },
    fashion: {
        name: 'Moda y Ropa',
        categories: {
            ropa_mujer: {
                id: 'ropa_mujer',
                name: 'Ropa Mujer',
                icon: 'fas fa-female',
                subcategories: [
                    { id: 'vestidos', name: 'Vestidos', icon: 'fas fa-tshirt' },
                    { id: 'blusas', name: 'Blusas y Camisas', icon: 'fas fa-tshirt' },
                    { id: 'pantalones-mujer', name: 'Pantalones', icon: 'fas fa-tshirt' },
                    { id: 'faldas', name: 'Faldas', icon: 'fas fa-tshirt' },
                    { id: 'ropa-interior-mujer', name: 'Ropa Interior', icon: 'fas fa-heart' },
                    { id: 'abrigos-mujer', name: 'Abrigos y Chaquetas', icon: 'fas fa-coat-arms' }
                ]
            },
            ropa_hombre: {
                id: 'ropa_hombre',
                name: 'Ropa Hombre',
                icon: 'fas fa-male',
                subcategories: [
                    { id: 'camisas-hombre', name: 'Camisas', icon: 'fas fa-tshirt' },
                    { id: 'pantalones-hombre', name: 'Pantalones', icon: 'fas fa-tshirt' },
                    { id: 'playeras', name: 'Playeras y Polos', icon: 'fas fa-tshirt' },
                    { id: 'trajes', name: 'Trajes', icon: 'fas fa-user-tie' },
                    { id: 'ropa-interior-hombre', name: 'Ropa Interior', icon: 'fas fa-tshirt' },
                    { id: 'abrigos-hombre', name: 'Abrigos y Chaquetas', icon: 'fas fa-coat-arms' }
                ]
            },
            calzado: {
                id: 'calzado',
                name: 'Calzado',
                icon: 'fas fa-shoe-prints',
                subcategories: [
                    { id: 'zapatos-mujer', name: 'Zapatos Mujer', icon: 'fas fa-shoe-prints' },
                    { id: 'zapatos-hombre', name: 'Zapatos Hombre', icon: 'fas fa-shoe-prints' },
                    { id: 'deportivos', name: 'Calzado Deportivo', icon: 'fas fa-running' },
                    { id: 'botas', name: 'Botas', icon: 'fas fa-shoe-prints' },
                    { id: 'sandalias', name: 'Sandalias', icon: 'fas fa-shoe-prints' }
                ]
            },
            accesorios: {
                id: 'accesorios',
                name: 'Accesorios',
                icon: 'fas fa-gem',
                subcategories: [
                    { id: 'bolsos', name: 'Bolsos y Carteras', icon: 'fas fa-shopping-bag' },
                    { id: 'joyeria', name: 'Joyería', icon: 'fas fa-gem' },
                    { id: 'relojes', name: 'Relojes', icon: 'fas fa-clock' },
                    { id: 'cinturones', name: 'Cinturones', icon: 'fas fa-circle' },
                    { id: 'sombreros', name: 'Sombreros y Gorras', icon: 'fas fa-hat-cowboy' },
                    { id: 'gafas', name: 'Gafas', icon: 'fas fa-glasses' }
                ]
            }
        }
    },
    food: {
        name: 'Alimentos',
        categories: {
            frescos: {
                id: 'frescos',
                name: 'Productos Frescos',
                icon: 'fas fa-apple-alt',
                subcategories: [
                    { id: 'frutas', name: 'Frutas', icon: 'fas fa-apple-alt' },
                    { id: 'verduras', name: 'Verduras', icon: 'fas fa-carrot' },
                    { id: 'carnes', name: 'Carnes', icon: 'fas fa-drumstick-bite' },
                    { id: 'pescados', name: 'Pescados y Mariscos', icon: 'fas fa-fish' },
                    { id: 'lacteos', name: 'Lácteos', icon: 'fas fa-cheese' },
                    { id: 'panaderia', name: 'Panadería', icon: 'fas fa-bread-slice' }
                ]
            },
            despensa: {
                id: 'despensa',
                name: 'Despensa',
                icon: 'fas fa-box',
                subcategories: [
                    { id: 'cereales', name: 'Cereales y Granos', icon: 'fas fa-seedling' },
                    { id: 'enlatados', name: 'Enlatados', icon: 'fas fa-box' },
                    { id: 'condimentos', name: 'Condimentos', icon: 'fas fa-pepper-hot' },
                    { id: 'aceites', name: 'Aceites y Vinagres', icon: 'fas fa-wine-bottle' },
                    { id: 'snacks', name: 'Snacks', icon: 'fas fa-cookie-bite' },
                    { id: 'dulces', name: 'Dulces y Chocolates', icon: 'fas fa-candy-cane' }
                ]
            },
            bebidas: {
                id: 'bebidas',
                name: 'Bebidas',
                icon: 'fas fa-wine-glass',
                subcategories: [
                    { id: 'refrescos', name: 'Refrescos', icon: 'fas fa-glass-whiskey' },
                    { id: 'jugos', name: 'Jugos', icon: 'fas fa-glass-whiskey' },
                    { id: 'agua', name: 'Agua', icon: 'fas fa-tint' },
                    { id: 'cafe-te', name: 'Café y Té', icon: 'fas fa-coffee' },
                    { id: 'alcoholicas', name: 'Bebidas Alcohólicas', icon: 'fas fa-wine-bottle' },
                    { id: 'energeticas', name: 'Bebidas Energéticas', icon: 'fas fa-bolt' }
                ]
            }
        }
    },
    health: {
        name: 'Salud y Belleza',
        categories: {
            cuidado_personal: {
                id: 'cuidado_personal',
                name: 'Cuidado Personal',
                icon: 'fas fa-spa',
                subcategories: [
                    { id: 'higiene', name: 'Higiene Personal', icon: 'fas fa-soap' },
                    { id: 'cuidado-piel', name: 'Cuidado de la Piel', icon: 'fas fa-hand-sparkles' },
                    { id: 'cuidado-cabello', name: 'Cuidado del Cabello', icon: 'fas fa-cut' },
                    { id: 'perfumes', name: 'Perfumes y Fragancias', icon: 'fas fa-spray-can' },
                    { id: 'afeitado', name: 'Afeitado', icon: 'fas fa-razor' }
                ]
            },
            maquillaje: {
                id: 'maquillaje',
                name: 'Maquillaje',
                icon: 'fas fa-palette',
                subcategories: [
                    { id: 'rostro', name: 'Maquillaje de Rostro', icon: 'fas fa-palette' },
                    { id: 'ojos', name: 'Maquillaje de Ojos', icon: 'fas fa-eye' },
                    { id: 'labios', name: 'Maquillaje de Labios', icon: 'fas fa-kiss' },
                    { id: 'unas', name: 'Cuidado de Uñas', icon: 'fas fa-hand-paper' },
                    { id: 'brochas', name: 'Brochas y Herramientas', icon: 'fas fa-paint-brush' }
                ]
            },
            salud: {
                id: 'salud',
                name: 'Salud y Bienestar',
                icon: 'fas fa-heartbeat',
                subcategories: [
                    { id: 'vitaminas', name: 'Vitaminas y Suplementos', icon: 'fas fa-pills' },
                    { id: 'medicamentos', name: 'Medicamentos', icon: 'fas fa-prescription-bottle' },
                    { id: 'primeros-auxilios', name: 'Primeros Auxilios', icon: 'fas fa-first-aid' },
                    { id: 'cuidado-dental', name: 'Cuidado Dental', icon: 'fas fa-tooth' },
                    { id: 'equipos-medicos', name: 'Equipos Médicos', icon: 'fas fa-stethoscope' }
                ]
            }
        }
    },
    home: {
        name: 'Hogar y Jardín',
        categories: {
            muebles: {
                id: 'muebles',
                name: 'Muebles',
                icon: 'fas fa-couch',
                subcategories: [
                    { id: 'sala', name: 'Muebles de Sala', icon: 'fas fa-couch' },
                    { id: 'dormitorio', name: 'Muebles de Dormitorio', icon: 'fas fa-bed' },
                    { id: 'comedor', name: 'Muebles de Comedor', icon: 'fas fa-utensils' },
                    { id: 'oficina', name: 'Muebles de Oficina', icon: 'fas fa-chair' },
                    { id: 'almacenamiento', name: 'Almacenamiento', icon: 'fas fa-archive' }
                ]
            },
            decoracion: {
                id: 'decoracion',
                name: 'Decoración',
                icon: 'fas fa-palette',
                subcategories: [
                    { id: 'cuadros', name: 'Cuadros y Arte', icon: 'fas fa-image' },
                    { id: 'plantas', name: 'Plantas Decorativas', icon: 'fas fa-leaf' },
                    { id: 'velas', name: 'Velas y Aromas', icon: 'fas fa-fire' },
                    { id: 'textiles', name: 'Textiles para Hogar', icon: 'fas fa-tshirt' },
                    { id: 'espejos', name: 'Espejos', icon: 'fas fa-mirror' }
                ]
            },
            jardin: {
                id: 'jardin',
                name: 'Jardín y Exterior',
                icon: 'fas fa-seedling',
                subcategories: [
                    { id: 'plantas-jardin', name: 'Plantas de Jardín', icon: 'fas fa-seedling' },
                    { id: 'herramientas-jardin', name: 'Herramientas de Jardín', icon: 'fas fa-tools' },
                    { id: 'muebles-exterior', name: 'Muebles de Exterior', icon: 'fas fa-chair' },
                    { id: 'iluminacion-exterior', name: 'Iluminación Exterior', icon: 'fas fa-lightbulb' },
                    { id: 'riego', name: 'Sistemas de Riego', icon: 'fas fa-tint' }
                ]
            },
            cocina: {
                id: 'cocina',
                name: 'Cocina y Comedor',
                icon: 'fas fa-utensils',
                subcategories: [
                    { id: 'utensilios', name: 'Utensilios de Cocina', icon: 'fas fa-utensils' },
                    { id: 'vajilla', name: 'Vajilla y Cristalería', icon: 'fas fa-wine-glass' },
                    { id: 'almacenaje-cocina', name: 'Almacenaje de Cocina', icon: 'fas fa-box' },
                    { id: 'textiles-cocina', name: 'Textiles de Cocina', icon: 'fas fa-tshirt' },
                    { id: 'pequenos-electrodomesticos', name: 'Pequeños Electrodomésticos', icon: 'fas fa-blender' }
                ]
            }
        }
    },
    sports: {
        name: 'Deportes',
        categories: {
            fitness: {
                id: 'fitness',
                name: 'Fitness y Gimnasio',
                icon: 'fas fa-dumbbell',
                subcategories: [
                    { id: 'pesas', name: 'Pesas y Mancuernas', icon: 'fas fa-dumbbell' },
                    { id: 'cardio', name: 'Equipos de Cardio', icon: 'fas fa-heartbeat' },
                    { id: 'yoga', name: 'Yoga y Pilates', icon: 'fas fa-spa' },
                    { id: 'accesorios-gym', name: 'Accesorios de Gimnasio', icon: 'fas fa-tools' },
                    { id: 'ropa-deportiva', name: 'Ropa Deportiva', icon: 'fas fa-tshirt' }
                ]
            },
            deportes_equipo: {
                id: 'deportes_equipo',
                name: 'Deportes de Equipo',
                icon: 'fas fa-futbol',
                subcategories: [
                    { id: 'futbol', name: 'Fútbol', icon: 'fas fa-futbol' },
                    { id: 'basketball', name: 'Basketball', icon: 'fas fa-basketball-ball' },
                    { id: 'volleyball', name: 'Volleyball', icon: 'fas fa-volleyball-ball' },
                    { id: 'baseball', name: 'Baseball', icon: 'fas fa-baseball-ball' },
                    { id: 'tenis', name: 'Tenis', icon: 'fas fa-table-tennis' }
                ]
            },
            deportes_acuaticos: {
                id: 'deportes_acuaticos',
                name: 'Deportes Acuáticos',
                icon: 'fas fa-swimmer',
                subcategories: [
                    { id: 'natacion', name: 'Natación', icon: 'fas fa-swimmer' },
                    { id: 'surf', name: 'Surf', icon: 'fas fa-water' },
                    { id: 'buceo', name: 'Buceo', icon: 'fas fa-mask' },
                    { id: 'kayak', name: 'Kayak y Canoa', icon: 'fas fa-ship' },
                    { id: 'pesca', name: 'Pesca', icon: 'fas fa-fish' }
                ]
            },
            deportes_extremos: {
                id: 'deportes_extremos',
                name: 'Deportes Extremos',
                icon: 'fas fa-mountain',
                subcategories: [
                    { id: 'escalada', name: 'Escalada', icon: 'fas fa-mountain' },
                    { id: 'ciclismo', name: 'Ciclismo', icon: 'fas fa-bicycle' },
                    { id: 'skateboard', name: 'Skateboard', icon: 'fas fa-skating' },
                    { id: 'camping', name: 'Camping y Senderismo', icon: 'fas fa-campground' },
                    { id: 'esqui', name: 'Esquí y Snowboard', icon: 'fas fa-skiing' }
                ]
            }
        }
    },
    automotive: {
        name: 'Automotriz',
        categories: {
            repuestos: {
                id: 'repuestos',
                name: 'Repuestos y Accesorios',
                icon: 'fas fa-cog',
                subcategories: [
                    { id: 'motor', name: 'Partes del Motor', icon: 'fas fa-cog' },
                    { id: 'frenos', name: 'Sistema de Frenos', icon: 'fas fa-stop-circle' },
                    { id: 'suspension', name: 'Suspensión', icon: 'fas fa-car' },
                    { id: 'electrico', name: 'Sistema Eléctrico', icon: 'fas fa-bolt' },
                    { id: 'carroceria', name: 'Carrocería', icon: 'fas fa-car-side' }
                ]
            },
            accesorios_auto: {
                id: 'accesorios_auto',
                name: 'Accesorios para Auto',
                icon: 'fas fa-car',
                subcategories: [
                    { id: 'interior-auto', name: 'Accesorios de Interior', icon: 'fas fa-car' },
                    { id: 'exterior-auto', name: 'Accesorios de Exterior', icon: 'fas fa-car-side' },
                    { id: 'audio-auto', name: 'Audio para Auto', icon: 'fas fa-music' },
                    { id: 'navegacion', name: 'GPS y Navegación', icon: 'fas fa-map' },
                    { id: 'seguridad-auto', name: 'Seguridad', icon: 'fas fa-shield-alt' }
                ]
            },
            llantas: {
                id: 'llantas',
                name: 'Llantas y Rines',
                icon: 'fas fa-circle',
                subcategories: [
                    { id: 'llantas-auto', name: 'Llantas para Auto', icon: 'fas fa-circle' },
                    { id: 'llantas-moto', name: 'Llantas para Moto', icon: 'fas fa-motorcycle' },
                    { id: 'rines', name: 'Rines', icon: 'fas fa-circle' },
                    { id: 'herramientas-llanta', name: 'Herramientas para Llantas', icon: 'fas fa-tools' }
                ]
            }
        }
    },
    books: {
        name: 'Libros y Medios',
        categories: {
            libros: {
                id: 'libros',
                name: 'Libros',
                icon: 'fas fa-book',
                subcategories: [
                    { id: 'ficcion', name: 'Ficción', icon: 'fas fa-book' },
                    { id: 'no-ficcion', name: 'No Ficción', icon: 'fas fa-book-open' },
                    { id: 'educativos', name: 'Libros Educativos', icon: 'fas fa-graduation-cap' },
                    { id: 'infantiles', name: 'Libros Infantiles', icon: 'fas fa-child' },
                    { id: 'comics', name: 'Comics y Manga', icon: 'fas fa-mask' }
                ]
            },
            medios_digitales: {
                id: 'medios_digitales',
                name: 'Medios Digitales',
                icon: 'fas fa-compact-disc',
                subcategories: [
                    { id: 'peliculas', name: 'Películas', icon: 'fas fa-film' },
                    { id: 'musica', name: 'Música', icon: 'fas fa-music' },
                    { id: 'software', name: 'Software', icon: 'fas fa-laptop-code' },
                    { id: 'ebooks', name: 'E-books', icon: 'fas fa-tablet-alt' },
                    { id: 'audiolibros', name: 'Audiolibros', icon: 'fas fa-headphones' }
                ]
            }
        }
    },
    toys: {
        name: 'Juguetes',
        categories: {
            juguetes_edad: {
                id: 'juguetes_edad',
                name: 'Juguetes por Edad',
                icon: 'fas fa-baby',
                subcategories: [
                    { id: 'bebes', name: 'Bebés (0-2 años)', icon: 'fas fa-baby' },
                    { id: 'preescolar', name: 'Preescolar (3-5 años)', icon: 'fas fa-child' },
                    { id: 'escolar', name: 'Escolar (6-12 años)', icon: 'fas fa-graduation-cap' },
                    { id: 'adolescentes', name: 'Adolescentes (13+ años)', icon: 'fas fa-user' }
                ]
            },
            juguetes_tipo: {
                id: 'juguetes_tipo',
                name: 'Tipos de Juguetes',
                icon: 'fas fa-gamepad',
                subcategories: [
                    { id: 'educativos-juguetes', name: 'Juguetes Educativos', icon: 'fas fa-brain' },
                    { id: 'construccion', name: 'Juegos de Construcción', icon: 'fas fa-cubes' },
                    { id: 'munecas', name: 'Muñecas y Figuras', icon: 'fas fa-female' },
                    { id: 'vehiculos-juguete', name: 'Vehículos de Juguete', icon: 'fas fa-car' },
                    { id: 'peluches', name: 'Peluches', icon: 'fas fa-heart' },
                    { id: 'juegos-mesa', name: 'Juegos de Mesa', icon: 'fas fa-chess' }
                ]
            }
        }
    },
    services: {
        name: 'Servicios',
        categories: {
            servicios_hogar: {
                id: 'servicios_hogar',
                name: 'Servicios para el Hogar',
                icon: 'fas fa-home',
                subcategories: [
                    { id: 'limpieza', name: 'Limpieza', icon: 'fas fa-broom' },
                    { id: 'plomeria', name: 'Plomería', icon: 'fas fa-wrench' },
                    { id: 'electricidad', name: 'Electricidad', icon: 'fas fa-bolt' },
                    { id: 'jardineria-servicio', name: 'Jardinería', icon: 'fas fa-seedling' },
                    { id: 'pintura', name: 'Pintura', icon: 'fas fa-paint-roller' }
                ]
            },
            servicios_profesionales: {
                id: 'servicios_profesionales',
                name: 'Servicios Profesionales',
                icon: 'fas fa-briefcase',
                subcategories: [
                    { id: 'consultoria', name: 'Consultoría', icon: 'fas fa-chart-line' },
                    { id: 'diseno', name: 'Diseño', icon: 'fas fa-palette' },
                    { id: 'marketing', name: 'Marketing', icon: 'fas fa-bullhorn' },
                    { id: 'legal', name: 'Servicios Legales', icon: 'fas fa-gavel' },
                    { id: 'contabilidad', name: 'Contabilidad', icon: 'fas fa-calculator' }
                ]
            },
            servicios_personales: {
                id: 'servicios_personales',
                name: 'Servicios Personales',
                icon: 'fas fa-user',
                subcategories: [
                    { id: 'belleza-servicio', name: 'Belleza y Estética', icon: 'fas fa-cut' },
                    { id: 'fitness-servicio', name: 'Fitness y Entrenamiento', icon: 'fas fa-dumbbell' },
                    { id: 'educacion', name: 'Educación y Tutorías', icon: 'fas fa-graduation-cap' },
                    { id: 'salud-servicio', name: 'Servicios de Salud', icon: 'fas fa-heartbeat' },
                    { id: 'transporte', name: 'Transporte', icon: 'fas fa-car' }
                ]
            }
        }
    }
};

// ==================== GESTIÓN SIMPLE DE CATEGORÍAS ====================

// Cargar datos desde el archivo externo
let availableCategories = [];
let subcategoriesByCategory = {};

// Inicializar datos cuando se carga la página
function inicializarDatosCategorias() {
    console.log('🔍 Verificando CATEGORIAS_DATA...');
    console.log('CATEGORIAS_DATA existe:', typeof CATEGORIAS_DATA !== 'undefined');

    if (typeof CATEGORIAS_DATA !== 'undefined') {
        console.log('📊 CATEGORIAS_DATA:', CATEGORIAS_DATA);
        console.log('📊 Número de categorías en CATEGORIAS_DATA:', Object.keys(CATEGORIAS_DATA).length);

        availableCategories = Object.values(CATEGORIAS_DATA).map(cat => ({
            id: cat.id,
            name: cat.nombre,
            icon: cat.icono,
            color: cat.color
        }));

        subcategoriesByCategory = {};
        Object.values(CATEGORIAS_DATA).forEach(cat => {
            subcategoriesByCategory[cat.id] = cat.subcategorias;
        });

        console.log('✅ Datos de categorías cargados:', availableCategories.length, 'categorías');
        console.log('📋 availableCategories:', availableCategories);
    } else {
        console.error('❌ CATEGORIAS_DATA no está definido');
        console.log('🔍 Variables globales disponibles:', Object.keys(window));
    }
}

// Variables globales
let selectedCategories = new Set();
let selectedSubcategories = new Set();
let currentSelectedCategory = null;

/**
 * Inicializar gestión simple de categorías - VERSIÓN SIMPLE
 */
function initializeSimpleCategoriesManagement() {
    console.log('🚀 Inicializando gestión SIMPLE de categorías...');

    // Cargar datos de categorías primero
    inicializarDatosCategorias();

    // Configurar contenedores vacíos
    updateSelectedCategoriesContainer();
    updateSelectedSubcategoriesContainer();

    // Configurar botón de categorías con onclick directo
    const categoryBtn = document.getElementById('categoryDropdownBtn');
    if (categoryBtn) {
        // Reemplazar completamente el onclick
        categoryBtn.setAttribute('onclick', '');
        categoryBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🖱️ Botón de categorías clickeado');
            toggleCategoryDropdown();
        };
        console.log('✅ Botón de categorías configurado');
    } else {
        console.error('❌ No se encontró el botón categoryDropdownBtn');
    }

    // Configurar botón de subcategorías
    const subcategoryBtn = document.getElementById('subcategoryDropdownBtn');
    if (subcategoryBtn) {
        subcategoryBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🖱️ Botón de subcategorías clickeado');
            toggleSubcategoryDropdown();
        };
        console.log('✅ Botón de subcategorías configurado');
    }

    console.log('🎉 Gestión simple de categorías inicializada correctamente');
}

/**
 * Cargar dropdown de categorías
 */
function loadCategoryDropdown() {
    console.log('loadCategoryDropdown llamada');

    const dropdown = document.getElementById('categoryDropdownMenu');
    if (!dropdown) {
        console.error('No se encontró categoryDropdownMenu');
        return;
    }

    console.log('Cargando', availableCategories.length, 'categorías');

    const dropdownHTML = availableCategories.map(category => `
        <div class="dropdown-item" onclick="selectCategory('${category.id}')">
            <i class="${category.icon}"></i>
            <span>${category.name}</span>
        </div>
    `).join('');

    dropdown.innerHTML = dropdownHTML;
    console.log('Dropdown cargado con HTML:', dropdownHTML.length, 'caracteres');
}

/**
 * Toggle dropdown de categorías - VERSIÓN SIMPLE
 */
function toggleCategoryDropdown() {
    console.log('🔄 toggleCategoryDropdown ejecutada');

    const dropdown = document.getElementById('categoryDropdownMenu');
    const btn = document.getElementById('categoryDropdownBtn');

    console.log('🔍 Elementos encontrados:', {
        dropdown: !!dropdown,
        btn: !!btn,
        dropdownId: dropdown?.id,
        btnId: btn?.id
    });

    if (!dropdown || !btn) {
        console.error('❌ Elementos no encontrados');
        console.log('🔍 Todos los elementos con ID categoryDropdown:',
            document.querySelectorAll('[id*="categoryDropdown"]'));
        return;
    }

    // Cargar categorías desde availableCategories
    if (dropdown.innerHTML.trim() === '') {
        console.log('📝 Cargando categorías...');
        // Asegurar que los datos estén cargados
        if (availableCategories.length === 0) {
            inicializarDatosCategorias();
        }

        if (availableCategories.length === 0) {
            dropdown.innerHTML = '<div class="dropdown-item disabled">No hay categorías disponibles</div>';
            console.log('⚠️ No hay categorías para mostrar');
        } else {
            loadCategoryDropdown();
            console.log('✅ Categorías cargadas en dropdown');
        }
    }

    // Toggle simple y directo
    if (dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
        dropdown.style.display = 'none';
        btn.classList.remove('active');
        console.log('🔒 Dropdown cerrado');
    } else {
        dropdown.classList.add('show');
        dropdown.style.display = 'block';
        btn.classList.add('active');
        console.log('🔓 Dropdown abierto - debería ser visible ahora');

        // Debug: verificar que el dropdown es visible
        console.log('Dropdown display:', dropdown.style.display);
        console.log('Dropdown classList:', dropdown.classList.toString());
        console.log('Dropdown innerHTML length:', dropdown.innerHTML.length);
    }
}

/**
 * Seleccionar categoría
 */
function selectCategory(categoryId) {
    if (selectedCategories.has(categoryId)) {
        showNotification('Esta categoría ya está seleccionada', 'warning');
        return;
    }

    const category = availableCategories.find(cat => cat.id === categoryId);
    if (!category) return;

    selectedCategories.add(categoryId);
    updateSelectedCategoriesContainer();
    updateSaveButtonVisibility();

    // Cerrar dropdown
    toggleCategoryDropdown();

    // Habilitar subcategorías si es la primera categoría
    if (selectedCategories.size === 1) {
        enableSubcategoryDropdown();
    }

    showNotification(`Categoría "${category.name}" agregada`, 'success');
}

/**
 * Remover categoría
 */
function removeCategory(categoryId) {
    selectedCategories.delete(categoryId);

    // Remover subcategorías relacionadas
    const subcategoriesToRemove = Array.from(selectedSubcategories).filter(sub =>
        subcategoriesByCategory[categoryId]?.includes(sub)
    );
    subcategoriesToRemove.forEach(sub => selectedSubcategories.delete(sub));

    updateSelectedCategoriesContainer();
    updateSelectedSubcategoriesContainer();
    updateSaveButtonVisibility();

    // Deshabilitar subcategorías si no hay categorías
    if (selectedCategories.size === 0) {
        disableSubcategoryDropdown();
    } else {
        loadSubcategoryDropdown();
    }

    const category = availableCategories.find(cat => cat.id === categoryId);
    if (category) {
        showNotification(`Categoría "${category.name}" eliminada`, 'success');
    }
}

/**
 * Actualizar contenedor de categorías seleccionadas
 */
function updateSelectedCategoriesContainer() {
    const container = document.getElementById('selectedCategoriesContainer');
    const containerWrapper = container?.parentElement;

    if (!container) return;

    if (selectedCategories.size === 0) {
        containerWrapper?.classList.add('empty');
        container.innerHTML = '';
        return;
    }

    containerWrapper?.classList.remove('empty');

    const selectedCategoriesData = Array.from(selectedCategories).map(id =>
        availableCategories.find(cat => cat.id === id)
    ).filter(Boolean);

    container.innerHTML = selectedCategoriesData.map(category => `
        <div class="selected-item-block" style="background-color: ${category.color}15; border-left: 4px solid ${category.color};">
            <i class="${category.icon}" style="color: ${category.color};"></i>
            <span style="color: ${category.color}; font-weight: 600;">${category.name}</span>
            <button class="remove-btn" onclick="removeCategory('${category.id}')" style="color: ${category.color};">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

/**
 * Habilitar dropdown de subcategorías
 */
function enableSubcategoryDropdown() {
    const btn = document.getElementById('subcategoryDropdownBtn');
    if (btn) {
        btn.disabled = false;
        loadSubcategoryDropdown();
    }
}

/**
 * Deshabilitar dropdown de subcategorías
 */
function disableSubcategoryDropdown() {
    const btn = document.getElementById('subcategoryDropdownBtn');
    const dropdown = document.getElementById('subcategoryDropdownMenu');
    if (btn && dropdown) {
        btn.disabled = true;
        dropdown.classList.remove('show');
        btn.classList.remove('active');
    }
}

/**
 * Cargar dropdown de subcategorías
 */
function loadSubcategoryDropdown() {
    const dropdown = document.getElementById('subcategoryDropdownMenu');
    if (!dropdown) return;

    // Obtener todas las subcategorías disponibles de las categorías seleccionadas
    const availableSubcategories = [];
    selectedCategories.forEach(categoryId => {
        const subs = subcategoriesByCategory[categoryId] || [];
        subs.forEach(sub => {
            if (!availableSubcategories.includes(sub)) {
                availableSubcategories.push(sub);
            }
        });
    });

    dropdown.innerHTML = availableSubcategories.map(subcategory => `
        <div class="dropdown-item" onclick="selectSubcategory('${subcategory}')">
            <i class="fas fa-tag"></i>
            <span>${subcategory}</span>
        </div>
    `).join('');
}

/**
 * Toggle dropdown de subcategorías
 */
function toggleSubcategoryDropdown() {
    const btn = document.getElementById('subcategoryDropdownBtn');
    if (btn?.disabled) return;

    const dropdown = document.getElementById('subcategoryDropdownMenu');
    if (!dropdown || !btn) return;

    dropdown.classList.toggle('show');
    btn.classList.toggle('active');

    // Cerrar otros dropdowns
    const categoryDropdown = document.getElementById('categoryDropdownMenu');
    const categoryBtn = document.getElementById('categoryDropdownBtn');
    if (categoryDropdown && categoryBtn) {
        categoryDropdown.classList.remove('show');
        categoryBtn.classList.remove('active');
    }

    // Cerrar dropdown al hacer clic fuera
    document.addEventListener('click', function closeDropdown(e) {
        if (!dropdown.contains(e.target) && !btn.contains(e.target)) {
            dropdown.classList.remove('show');
            btn.classList.remove('active');
            document.removeEventListener('click', closeDropdown);
        }
    });
}

/**
 * Seleccionar subcategoría
 */
function selectSubcategory(subcategory) {
    if (selectedSubcategories.has(subcategory)) {
        showNotification('Esta subcategoría ya está seleccionada', 'warning');
        return;
    }

    selectedSubcategories.add(subcategory);
    updateSelectedSubcategoriesContainer();
    updateSaveButtonVisibility();

    // Cerrar dropdown
    toggleSubcategoryDropdown();

    showNotification(`Subcategoría "${subcategory}" agregada`, 'success');
}

/**
 * Remover subcategoría
 */
function removeSubcategory(subcategory) {
    selectedSubcategories.delete(subcategory);
    updateSelectedSubcategoriesContainer();
    updateSaveButtonVisibility();

    showNotification(`Subcategoría "${subcategory}" eliminada`, 'success');
}

/**
 * Actualizar contenedor de subcategorías seleccionadas
 */
function updateSelectedSubcategoriesContainer() {
    const container = document.getElementById('selectedSubcategoriesContainer');
    const containerWrapper = container?.parentElement;

    if (!container) return;

    if (selectedSubcategories.size === 0) {
        containerWrapper?.classList.add('empty');
        container.innerHTML = '';
        return;
    }

    containerWrapper?.classList.remove('empty');

    container.innerHTML = Array.from(selectedSubcategories).map(subcategory => `
        <div class="selected-item-block subcategory" style="background-color: #6366f115; border-left: 4px solid #6366f1;">
            <i class="fas fa-tag" style="color: #6366f1;"></i>
            <span style="color: #6366f1; font-weight: 500;">${subcategory}</span>
            <button class="remove-btn" onclick="removeSubcategory('${subcategory}')" style="color: #6366f1;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

/**
 * Actualizar visibilidad del botón de guardar
 */
function updateSaveButtonVisibility() {
    const saveBtn = document.getElementById('saveCategoriesBtn');
    if (saveBtn) {
        if (selectedCategories.size > 0 || selectedSubcategories.size > 0) {
            saveBtn.style.display = 'inline-flex';
        } else {
            saveBtn.style.display = 'none';
        }
    }
}

/**
 * Guardar configuración simple de categorías
 */
function saveSimpleCategoriesConfiguration() {
    const configuration = {
        categories: Array.from(selectedCategories),
        subcategories: Array.from(selectedSubcategories),
        timestamp: new Date().toISOString()
    };

    console.log('Configuración de categorías guardada:', configuration);

    // Aquí puedes agregar la lógica para enviar al servidor
    showNotification(`Configuración guardada: ${configuration.categories.length} categorías, ${configuration.subcategories.length} subcategorías`, 'success');
}

/**
 * Crear tarjeta de categoría seleccionada
 */
function createSelectedCategoryCard(category) {
    const card = document.createElement('div');
    card.className = 'selected-category-card';
    card.dataset.categoryId = category.id;

    const subcategoriesCount = category.selectedSubcategories ? category.selectedSubcategories.length : 0;
    const totalSubcategories = category.subcategories ? category.subcategories.length : 0;

    card.innerHTML = `
        <div class="category-header">
            <div class="category-info">
                <i class="${category.icon}"></i>
                <span class="category-name">${category.name}</span>
            </div>
            <div class="category-actions">
                <button class="action-btn" onclick="manageCategorySubcategories('${category.id}')" title="Gestionar subcategorías">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="action-btn" onclick="removeSelectedCategory('${category.id}')" title="Eliminar categoría">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="subcategories-count">
            ${subcategoriesCount}/${totalSubcategories} subcategorías seleccionadas
        </div>
    `;

    // Event listener para gestionar subcategorías
    card.addEventListener('click', function(e) {
        if (!e.target.closest('.category-actions')) {
            manageCategorySubcategories(category.id);
        }
    });

    return card;
}

/**
 * Gestionar subcategorías de una categoría
 */
function manageCategorySubcategories(categoryId) {
    console.log('Gestionando subcategorías para categoría:', categoryId);

    const category = selectedCategories.find(cat => cat.id === categoryId);
    if (!category) {
        console.error('Categoría no encontrada:', categoryId);
        return;
    }

    currentCategoryForSubcategories = categoryId;
    showSubcategoriesPanel(category);
}

/**
 * Mostrar panel de subcategorías
 */
function showSubcategoriesPanel(category) {
    const subcategoriesPanel = document.getElementById('subcategoriesPanel');
    const selectedCategoryNameSpan = document.getElementById('selectedCategoryName');
    const subcategoriesGrid = document.getElementById('subcategoriesAvailableGrid');

    if (!subcategoriesPanel || !subcategoriesGrid) {
        console.error('Elementos del panel de subcategorías no encontrados');
        return;
    }

    // Actualizar título
    if (selectedCategoryNameSpan) {
        selectedCategoryNameSpan.textContent = category.name;
    }

    // Limpiar grid
    subcategoriesGrid.innerHTML = '';

    // Agregar subcategorías
    if (category.subcategories && category.subcategories.length > 0) {
        category.subcategories.forEach(subcategory => {
            const subcategoryCard = createAvailableSubcategoryCard(subcategory, category);
            subcategoriesGrid.appendChild(subcategoryCard);
        });
    } else {
        subcategoriesGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-tags"></i>
                <p>No hay subcategorías disponibles</p>
                <small>Esta categoría no tiene subcategorías predefinidas</small>
            </div>
        `;
    }

    // Mostrar panel
    subcategoriesPanel.style.display = 'block';

    // Scroll hacia el panel
    subcategoriesPanel.scrollIntoView({ behavior: 'smooth' });
}

/**
 * Ocultar panel de subcategorías
 */
function hideSubcategoriesPanel() {
    const subcategoriesPanel = document.getElementById('subcategoriesPanel');
    if (subcategoriesPanel) {
        subcategoriesPanel.style.display = 'none';
    }
    currentCategoryForSubcategories = null;
}

/**
 * Crear tarjeta de subcategoría disponible
 */
function createAvailableSubcategoryCard(subcategory, category) {
    const card = document.createElement('div');
    card.className = 'subcategory-available-card';
    card.dataset.subcategoryId = subcategory.id;

    // Verificar si ya está seleccionada
    const isSelected = category.selectedSubcategories &&
                      category.selectedSubcategories.some(sub => sub.id === subcategory.id);
    if (isSelected) {
        card.classList.add('selected');
    }

    card.innerHTML = `
        <i class="${subcategory.icon}"></i>
        <span>${subcategory.name}</span>
    `;

    // Event listener para seleccionar/deseleccionar
    card.addEventListener('click', function() {
        toggleSubcategorySelection(subcategory, category, card);
    });

    return card;
}

/**
 * Alternar selección de subcategoría
 */
function toggleSubcategorySelection(subcategory, category, cardElement) {
    // Inicializar array de subcategorías seleccionadas si no existe
    if (!category.selectedSubcategories) {
        category.selectedSubcategories = [];
    }

    const isSelected = category.selectedSubcategories.some(sub => sub.id === subcategory.id);

    if (isSelected) {
        // Deseleccionar
        category.selectedSubcategories = category.selectedSubcategories.filter(sub => sub.id !== subcategory.id);
        cardElement.classList.remove('selected');
        showNotification(`Subcategoría "${subcategory.name}" removida`, 'info');
    } else {
        // Seleccionar
        category.selectedSubcategories.push(subcategory);
        cardElement.classList.add('selected');
        showNotification(`Subcategoría "${subcategory.name}" agregada`, 'success');
    }

    // Actualizar display de categorías seleccionadas para reflejar el cambio
    updateSelectedCategoriesDisplay();
}

/**
 * Remover categoría seleccionada
 */
function removeSelectedCategory(categoryId) {
    console.log('Removiendo categoría:', categoryId);

    const category = selectedCategories.find(cat => cat.id === categoryId);
    if (!category) {
        console.error('Categoría no encontrada:', categoryId);
        return;
    }

    // Confirmar eliminación
    if (confirm(`¿Estás seguro de que quieres eliminar la categoría "${category.name}"?`)) {
        // Remover de la lista
        selectedCategories = selectedCategories.filter(cat => cat.id !== categoryId);

        // Actualizar displays
        updateSelectedCategoriesDisplay();

        // Si estamos viendo las subcategorías de esta categoría, ocultarlas
        if (currentCategoryForSubcategories === categoryId) {
            hideSubcategoriesPanel();
        }

        // Actualizar dropdown de categorías
        loadCategoryDropdown();

        showNotification(`Categoría "${category.name}" eliminada`, 'success');
    }
}

/**
 * Obtener resumen de categorías seleccionadas
 */
function getCategoriesSummary() {
    const summary = {
        totalCategories: selectedCategories.length,
        totalSubcategories: selectedCategories.reduce((total, cat) => {
            return total + (cat.selectedSubcategories ? cat.selectedSubcategories.length : 0);
        }, 0),
        commerceType: selectedCommerceType,
        categories: selectedCategories.map(cat => ({
            id: cat.id,
            name: cat.name,
            icon: cat.icon,
            subcategoriesCount: cat.selectedSubcategories ? cat.selectedSubcategories.length : 0,
            subcategories: cat.selectedSubcategories || []
        }))
    };

    console.log('Resumen de categorías:', summary);
    return summary;
}

/**
 * Guardar configuración de categorías
 */
function saveCategoriesConfiguration() {
    const summary = getCategoriesSummary();

    if (summary.totalCategories === 0) {
        showNotification('No hay categorías seleccionadas para guardar', 'warning');
        return;
    }

    // Aquí podrías enviar los datos al servidor
    console.log('Guardando configuración:', summary);

    // Simular guardado
    setTimeout(() => {
        showNotification(`Configuración guardada: ${summary.totalCategories} categorías, ${summary.totalSubcategories} subcategorías`, 'success');
    }, 500);
}

/**
 * Inicializar gestión de categorías (función original para compatibilidad)
 */
function initializeCategoryManagement() {
    console.log('Inicializando gestión de categorías...');

    // Verificar que existan los elementos necesarios
    const categoriesList = document.getElementById('categoriesList');
    const subcategoriesGrid = document.getElementById('subcategoriesGrid');

    if (!categoriesList) {
        console.error('No se encontró el elemento categoriesList');
        return;
    }

    if (!subcategoriesGrid) {
        console.error('No se encontró el elemento subcategoriesGrid');
        return;
    }

    // Renderizar categorías iniciales
    renderCategories();

    // Event listeners para modales
    setupCategoryModals();
    setupSubcategoryModals();

    console.log('Gestión de categorías inicializada correctamente');
}

/**
 * Renderizar lista de categorías
 */
function renderCategories() {
    console.log('Renderizando categorías...');

    const categoriesList = document.getElementById('categoriesList');
    if (!categoriesList) {
        console.error('No se encontró categoriesList');
        return;
    }

    categoriesList.innerHTML = '';

    Object.values(categoriesData).forEach(category => {
        const categoryItem = createCategoryItem(category);
        categoriesList.appendChild(categoryItem);
    });

    console.log('Categorías renderizadas:', Object.keys(categoriesData));

    // Seleccionar la categoría actual o la primera disponible
    if (Object.keys(categoriesData).length > 0) {
        const categoryToSelect = currentCategory && categoriesData[currentCategory]
            ? currentCategory
            : Object.keys(categoriesData)[0];

        console.log('Seleccionando categoría:', categoryToSelect);
        selectCategory(categoryToSelect);
    } else {
        // Si no hay categorías, mostrar mensaje vacío
        console.log('No hay categorías, mostrando mensaje vacío');
        showEmptySubcategories();
    }
}

/**
 * Crear elemento de categoría
 */
function createCategoryItem(category) {
    const item = document.createElement('div');
    item.className = 'category-item';
    item.dataset.category = category.id;

    const subcategoryCount = category.subcategories ? category.subcategories.length : 0;

    item.innerHTML = `
        <div class="category-content">
            <div class="category-icon">
                <i class="${category.icon}"></i>
            </div>
            <div class="category-info">
                <h4 class="category-name">${category.name}</h4>
                <span class="category-count">${subcategoryCount} subcategorías</span>
            </div>
        </div>
        <div class="category-actions">
            <button class="action-btn edit-btn" title="Editar" onclick="editCategory('${category.id}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" title="Eliminar" onclick="deleteCategory('${category.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    // Event listener para seleccionar categoría
    item.addEventListener('click', function(e) {
        // No activar si se hizo click en los botones de acción
        if (!e.target.closest('.category-actions')) {
            console.log('Seleccionando categoría:', category.id);
            selectCategory(category.id);
        }
    });

    return item;
}

/**
 * Seleccionar categoría
 */
function selectCategory(categoryId) {
    console.log('Función selectCategory llamada con:', categoryId);
    console.log('Datos de categorías disponibles:', Object.keys(categoriesData));

    if (!categoriesData[categoryId]) {
        console.error('Categoría no encontrada:', categoryId);
        return;
    }

    // Remover clase active de todas las categorías
    document.querySelectorAll('.category-item').forEach(item => {
        item.classList.remove('active');
    });

    // Agregar clase active a la categoría seleccionada
    const selectedItem = document.querySelector(`[data-category="${categoryId}"]`);
    if (selectedItem) {
        selectedItem.classList.add('active');
        currentCategory = categoryId;

        console.log('Categoría seleccionada:', categoryId);

        // Actualizar título de subcategorías
        const categoryName = categoriesData[categoryId].name;
        const selectedCategoryNameElement = document.getElementById('selectedCategoryName');
        if (selectedCategoryNameElement) {
            selectedCategoryNameElement.textContent = categoryName;
            console.log('Título actualizado a:', categoryName);
        }

        // Cargar subcategorías
        loadSubcategories(categoryId);
    } else {
        console.error('No se encontró el elemento DOM para la categoría:', categoryId);
    }
}

/**
 * Cargar subcategorías
 */
function loadSubcategories(categoryId) {
    console.log('Cargando subcategorías para:', categoryId);

    const subcategoriesGrid = document.getElementById('subcategoriesGrid');
    const categoryData = categoriesData[categoryId];

    if (!subcategoriesGrid) {
        console.error('No se encontró el grid de subcategorías');
        return;
    }

    console.log('Datos de la categoría:', categoryData);

    subcategoriesGrid.innerHTML = '';

    if (!categoryData || !categoryData.subcategories || categoryData.subcategories.length === 0) {
        console.log('No hay subcategorías, mostrando mensaje vacío');
        showEmptySubcategories();
        return;
    }

    console.log('Renderizando', categoryData.subcategories.length, 'subcategorías');

    categoryData.subcategories.forEach(subcategory => {
        const subcategoryCard = createSubcategoryCard(subcategory);
        subcategoriesGrid.appendChild(subcategoryCard);
    });
}

/**
 * Mostrar mensaje cuando no hay subcategorías
 */
function showEmptySubcategories() {
    const subcategoriesGrid = document.getElementById('subcategoriesGrid');
    if (!subcategoriesGrid) return;

    subcategoriesGrid.innerHTML = `
        <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: var(--text-secondary);">
            <i class="fas fa-tags" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
            <h3 style="margin: 0 0 0.5rem 0; font-weight: 500;">No hay subcategorías</h3>
            <p style="margin: 0; font-size: var(--font-size-sm);">
                ${currentCategory ? 'Agrega subcategorías para esta categoría' : 'Selecciona una categoría para ver sus subcategorías'}
            </p>
        </div>
    `;
}

/**
 * Crear tarjeta de subcategoría
 */
function createSubcategoryCard(subcategory) {
    const card = document.createElement('div');
    card.className = 'subcategory-card';
    card.innerHTML = `
        <div class="subcategory-header">
            <div class="subcategory-icon">
                <i class="${subcategory.icon}"></i>
            </div>
            <h4 class="subcategory-name">${subcategory.name}</h4>
        </div>
        <div class="subcategory-stats">
            <span class="product-count">${subcategory.products} productos</span>
            <span class="status-badge active">Activa</span>
        </div>
        <div class="subcategory-actions">
            <button class="action-btn edit-btn" title="Editar" onclick="editSubcategory('${subcategory.id}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" title="Eliminar" onclick="deleteSubcategory('${subcategory.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    return card;
}

/**
 * Configurar botones de categorías
 */
function setupCategoryButtons() {
    console.log('Configurando botones de categorías...');

    const addCategoryBtn = document.getElementById('addCategoryBtn');
    const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');

    console.log('Botones encontrados:', {
        addCategoryBtn: !!addCategoryBtn,
        addSubcategoryBtn: !!addSubcategoryBtn
    });

    // Configurar botón de agregar categoría
    if (addCategoryBtn) {
        // Remover listeners existentes
        addCategoryBtn.removeEventListener('click', handleCategoryButtonClick);
        // Agregar nuevo listener
        addCategoryBtn.addEventListener('click', handleCategoryButtonClick);
        console.log('Event listener agregado al botón de categoría');
    }

    // Configurar botón de agregar subcategoría
    if (addSubcategoryBtn) {
        // Remover listeners existentes
        addSubcategoryBtn.removeEventListener('click', handleSubcategoryButtonClick);
        // Agregar nuevo listener
        addSubcategoryBtn.addEventListener('click', handleSubcategoryButtonClick);
        console.log('Event listener agregado al botón de subcategoría');
    }
}

/**
 * Manejar click del botón de categoría
 */
function handleCategoryButtonClick(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('Botón de categoría clickeado - Abriendo modal');
    openCategoryModal();
}

/**
 * Manejar click del botón de subcategoría
 */
function handleSubcategoryButtonClick(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('Botón de subcategoría clickeado - Abriendo modal');
    openSubcategoryModal();
}

/**
 * Configurar modales de categorías
 */
function setupCategoryModals() {
    console.log('Iniciando configuración de modales de categorías...');

    const modal = document.getElementById('categoryModal');
    const openBtn = document.getElementById('addCategoryBtn');
    const closeBtn = document.getElementById('closeCategoryModal');
    const cancelBtn = document.getElementById('cancelCategoryBtn');
    const saveBtn = document.getElementById('saveCategoryBtn');

    console.log('Elementos encontrados:', {
        modal: !!modal,
        openBtn: !!openBtn,
        closeBtn: !!closeBtn,
        cancelBtn: !!cancelBtn,
        saveBtn: !!saveBtn
    });

    // Los event listeners de los botones se manejan en setupCategoryButtons()
    console.log('Configuración de modales de categorías - botones manejados por setupCategoryButtons');

    // Cerrar modal
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeCategoryModal();
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeCategoryModal();
        });
    }

    // Cerrar al hacer click fuera del modal
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeCategoryModal();
            }
        });
    }

    // Guardar categoría
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            saveCategoryData();
        });
    }

    // Selector de iconos
    setupIconSelector('categoryModal', 'selectedIcon');
}

/**
 * Configurar modales de subcategorías
 */
function setupSubcategoryModals() {
    console.log('Iniciando configuración de modales de subcategorías...');

    const modal = document.getElementById('subcategoryModal');
    const openBtn = document.getElementById('addSubcategoryBtn');
    const closeBtn = document.getElementById('closeSubcategoryModal');
    const cancelBtn = document.getElementById('cancelSubcategoryBtn');
    const saveBtn = document.getElementById('saveSubcategoryBtn');

    console.log('Elementos encontrados:', {
        modal: !!modal,
        openBtn: !!openBtn,
        closeBtn: !!closeBtn,
        cancelBtn: !!cancelBtn,
        saveBtn: !!saveBtn
    });

    // Los event listeners de los botones se manejan en setupCategoryButtons()
    console.log('Configuración de modales de subcategorías - botones manejados por setupCategoryButtons');

    // Cerrar modal
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeSubcategoryModal();
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeSubcategoryModal();
        });
    }

    // Cerrar al hacer click fuera del modal
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeSubcategoryModal();
            }
        });
    }

    // Guardar subcategoría
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            saveSubcategoryData();
        });
    }

    // Selector de iconos
    setupIconSelector('subcategoryModal', 'selectedSubIcon');
}

/**
 * Configurar selector de iconos
 */
function setupIconSelector(modalId, hiddenInputId) {
    const modal = document.getElementById(modalId);
    const hiddenInput = document.getElementById(hiddenInputId);

    modal?.querySelectorAll('.icon-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remover clase active de todas las opciones
            modal.querySelectorAll('.icon-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // Agregar clase active a la opción seleccionada
            this.classList.add('active');

            // Actualizar valor del input oculto
            if (hiddenInput) {
                hiddenInput.value = this.dataset.icon;
            }
        });
    });
}

/**
 * Abrir modal de categoría
 */
function openCategoryModal(categoryData = null) {
    console.log('openCategoryModal llamada');

    const modal = document.getElementById('categoryModal');
    const title = document.getElementById('categoryModalTitle');
    const nameInput = document.getElementById('categoryName');
    const descInput = document.getElementById('categoryDescription');
    const iconInput = document.getElementById('selectedIcon');

    console.log('Elementos del modal de categoría:', {
        modal: !!modal,
        title: !!title,
        nameInput: !!nameInput,
        descInput: !!descInput,
        iconInput: !!iconInput
    });

    if (!modal) {
        console.error('Modal de categoría no encontrado');
        showNotification('Error: Modal de categoría no encontrado', 'error');
        return;
    }

    // Resetear selector de iconos
    modal.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('active');
    });

    if (categoryData) {
        title.textContent = 'Editar Categoría';
        nameInput.value = categoryData.name || '';
        descInput.value = categoryData.description || '';

        // Seleccionar icono actual
        const currentIconOption = modal.querySelector(`[data-icon="${categoryData.icon}"]`);
        if (currentIconOption) {
            currentIconOption.classList.add('active');
            iconInput.value = categoryData.icon;
        }
    } else {
        title.textContent = 'Agregar Nueva Categoría';
        nameInput.value = '';
        descInput.value = '';

        // Seleccionar primer icono por defecto
        const firstIcon = modal.querySelector('.icon-option');
        if (firstIcon) {
            firstIcon.classList.add('active');
            iconInput.value = firstIcon.dataset.icon;
        }
    }

    modal.classList.add('active');
    console.log('Modal de categoría activado, clases:', modal.className);
}

/**
 * Cerrar modal de categoría
 */
function closeCategoryModal() {
    const modal = document.getElementById('categoryModal');
    modal.classList.remove('active');
}

/**
 * Abrir modal de subcategoría
 */
function openSubcategoryModal(subcategoryData = null) {
    console.log('openSubcategoryModal llamada');

    const modal = document.getElementById('subcategoryModal');
    const title = document.getElementById('subcategoryModalTitle');
    const nameInput = document.getElementById('subcategoryName');
    const descInput = document.getElementById('subcategoryDescription');
    const parentSelect = document.getElementById('parentCategory');
    const iconInput = document.getElementById('selectedSubIcon');

    console.log('Elementos del modal de subcategoría:', {
        modal: !!modal,
        title: !!title,
        nameInput: !!nameInput,
        descInput: !!descInput,
        parentSelect: !!parentSelect,
        iconInput: !!iconInput
    });

    if (!modal) {
        console.error('Modal de subcategoría no encontrado');
        showNotification('Error: Modal de subcategoría no encontrado', 'error');
        return;
    }

    // Actualizar opciones del select de categoría padre
    updateParentCategorySelect();

    // Establecer categoría padre actual
    if (parentSelect && currentCategory) {
        parentSelect.value = currentCategory;
    }

    // Resetear selector de iconos
    modal.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('active');
    });

    if (subcategoryData) {
        title.textContent = 'Editar Subcategoría';
        nameInput.value = subcategoryData.name || '';
        descInput.value = subcategoryData.description || '';

        // Seleccionar icono actual
        const currentIconOption = modal.querySelector(`[data-icon="${subcategoryData.icon}"]`);
        if (currentIconOption) {
            currentIconOption.classList.add('active');
            iconInput.value = subcategoryData.icon;
        }
    } else {
        title.textContent = 'Agregar Nueva Subcategoría';
        nameInput.value = '';
        descInput.value = '';

        // Seleccionar primer icono por defecto
        const firstIcon = modal.querySelector('.icon-option');
        if (firstIcon) {
            firstIcon.classList.add('active');
            iconInput.value = firstIcon.dataset.icon;
        }
    }

    modal.classList.add('active');
    console.log('Modal de subcategoría activado, clases:', modal.className);
}

/**
 * Cerrar modal de subcategoría
 */
function closeSubcategoryModal() {
    const modal = document.getElementById('subcategoryModal');
    modal.classList.remove('active');
}

/**
 * Guardar datos de categoría
 */
function saveCategoryData() {
    const nameInput = document.getElementById('categoryName');
    const descInput = document.getElementById('categoryDescription');
    const iconInput = document.getElementById('selectedIcon');

    const categoryData = {
        name: nameInput.value.trim(),
        description: descInput.value.trim(),
        icon: iconInput.value
    };

    if (!categoryData.name) {
        showNotification('Por favor ingresa un nombre para la categoría', 'warning');
        return;
    }

    // Verificar si es edición o creación
    if (editingCategoryId) {
        // Editar categoría existente
        if (categoriesData[editingCategoryId]) {
            categoriesData[editingCategoryId].name = categoryData.name;
            categoriesData[editingCategoryId].description = categoryData.description;
            categoriesData[editingCategoryId].icon = categoryData.icon;

            showNotification('Categoría actualizada exitosamente', 'success');
        }
        editingCategoryId = null;
    } else {
        // Crear nueva categoría
        const categoryId = generateCategoryId(categoryData.name);

        // Verificar que no exista ya una categoría con ese ID
        if (categoriesData[categoryId]) {
            showNotification('Ya existe una categoría con ese nombre', 'warning');
            return;
        }

        categoriesData[categoryId] = {
            id: categoryId,
            name: categoryData.name,
            description: categoryData.description,
            icon: categoryData.icon,
            subcategories: []
        };

        showNotification('Categoría creada exitosamente', 'success');

        // Seleccionar la nueva categoría
        currentCategory = categoryId;
    }

    // Actualizar la interfaz
    renderCategories();
    updateParentCategorySelect();
    closeCategoryModal();
}

/**
 * Guardar datos de subcategoría
 */
function saveSubcategoryData() {
    const nameInput = document.getElementById('subcategoryName');
    const descInput = document.getElementById('subcategoryDescription');
    const iconInput = document.getElementById('selectedSubIcon');
    const parentSelect = document.getElementById('parentCategory');

    const subcategoryData = {
        name: nameInput.value.trim(),
        description: descInput.value.trim(),
        icon: iconInput.value,
        parentCategory: parentSelect.value
    };

    if (!subcategoryData.name) {
        showNotification('Por favor ingresa un nombre para la subcategoría', 'warning');
        return;
    }

    if (!subcategoryData.parentCategory || !categoriesData[subcategoryData.parentCategory]) {
        showNotification('Por favor selecciona una categoría padre válida', 'warning');
        return;
    }

    const parentCategory = categoriesData[subcategoryData.parentCategory];

    // Verificar si es edición o creación
    if (editingSubcategoryId) {
        // Editar subcategoría existente
        const subcategoryIndex = parentCategory.subcategories.findIndex(sub => sub.id === editingSubcategoryId);
        if (subcategoryIndex !== -1) {
            parentCategory.subcategories[subcategoryIndex].name = subcategoryData.name;
            parentCategory.subcategories[subcategoryIndex].description = subcategoryData.description;
            parentCategory.subcategories[subcategoryIndex].icon = subcategoryData.icon;

            showNotification('Subcategoría actualizada exitosamente', 'success');
        }
        editingSubcategoryId = null;
    } else {
        // Crear nueva subcategoría
        const subcategoryId = generateSubcategoryId(subcategoryData.name);

        // Verificar que no exista ya una subcategoría con ese ID en la categoría padre
        const existingSubcategory = parentCategory.subcategories.find(sub => sub.id === subcategoryId);
        if (existingSubcategory) {
            showNotification('Ya existe una subcategoría con ese nombre en esta categoría', 'warning');
            return;
        }

        const newSubcategory = {
            id: subcategoryId,
            name: subcategoryData.name,
            description: subcategoryData.description,
            icon: subcategoryData.icon,
            products: 0
        };

        parentCategory.subcategories.push(newSubcategory);
        showNotification('Subcategoría creada exitosamente', 'success');
    }

    // Actualizar la interfaz
    renderCategories();
    loadSubcategories(subcategoryData.parentCategory);
    closeSubcategoryModal();
}

/**
 * Editar categoría
 */
function editCategory(categoryId) {
    const category = categoriesData[categoryId];
    if (!category) return;

    editingCategoryId = categoryId;
    openCategoryModal(category);
}

/**
 * Eliminar categoría
 */
function deleteCategory(categoryId) {
    const category = categoriesData[categoryId];
    if (!category) return;

    const subcategoryCount = category.subcategories ? category.subcategories.length : 0;
    let confirmMessage = `¿Estás seguro de que deseas eliminar la categoría "${category.name}"?`;

    if (subcategoryCount > 0) {
        confirmMessage += `\n\nEsta acción también eliminará ${subcategoryCount} subcategoría(s).`;
    }

    if (confirm(confirmMessage)) {
        delete categoriesData[categoryId];

        // Si era la categoría actual, seleccionar otra
        if (currentCategory === categoryId) {
            const remainingCategories = Object.keys(categoriesData);
            currentCategory = remainingCategories.length > 0 ? remainingCategories[0] : null;
        }

        renderCategories();
        updateParentCategorySelect();
        showNotification('Categoría eliminada exitosamente', 'success');
    }
}

/**
 * Editar subcategoría
 */
function editSubcategory(subcategoryId) {
    // Buscar la subcategoría en la categoría actual
    const category = categoriesData[currentCategory];
    if (!category || !category.subcategories) return;

    const subcategory = category.subcategories.find(sub => sub.id === subcategoryId);
    if (!subcategory) return;

    editingSubcategoryId = subcategoryId;
    openSubcategoryModal(subcategory);
}

/**
 * Eliminar subcategoría
 */
function deleteSubcategory(subcategoryId) {
    const category = categoriesData[currentCategory];
    if (!category || !category.subcategories) return;

    const subcategoryIndex = category.subcategories.findIndex(sub => sub.id === subcategoryId);
    if (subcategoryIndex === -1) return;

    const subcategory = category.subcategories[subcategoryIndex];

    if (confirm(`¿Estás seguro de que deseas eliminar la subcategoría "${subcategory.name}"?`)) {
        category.subcategories.splice(subcategoryIndex, 1);

        renderCategories();
        loadSubcategories(currentCategory);
        showNotification('Subcategoría eliminada exitosamente', 'success');
    }
}

/**
 * Generar ID para categoría
 */
function generateCategoryId(name) {
    return name.toLowerCase()
        .replace(/[áàäâ]/g, 'a')
        .replace(/[éèëê]/g, 'e')
        .replace(/[íìïî]/g, 'i')
        .replace(/[óòöô]/g, 'o')
        .replace(/[úùüû]/g, 'u')
        .replace(/ñ/g, 'n')
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
}

/**
 * Generar ID para subcategoría
 */
function generateSubcategoryId(name) {
    return generateCategoryId(name);
}

/**
 * Actualizar select de categoría padre
 */
function updateParentCategorySelect() {
    const parentSelect = document.getElementById('parentCategory');
    if (!parentSelect) return;

    parentSelect.innerHTML = '';

    Object.values(categoriesData).forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        parentSelect.appendChild(option);
    });

    // Seleccionar la categoría actual por defecto
    if (currentCategory && categoriesData[currentCategory]) {
        parentSelect.value = currentCategory;
    }
}

// ==================== INICIALIZACIÓN ACTUALIZADA ====================

/**
 * Inicialización del dashboard
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard administrativo cargado');

    // Esperar un poco más para asegurar que todo esté renderizado
    setTimeout(() => {
        // Inicializar componentes
        initializeTabs();
        initializeKPIs();
        initializeProductManagement();
        initializeCategoryManagement();

        console.log('Todos los componentes inicializados correctamente');

        // Verificar que los botones existan
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');

        console.log('Verificación de botones:', {
            addCategoryBtn: !!addCategoryBtn,
            addSubcategoryBtn: !!addSubcategoryBtn
        });

        // Si los botones no existen, intentar configurar los modales de nuevo
        if (!addCategoryBtn || !addSubcategoryBtn) {
            console.warn('Botones no encontrados, reintentando configuración...');
            setTimeout(() => {
                setupCategoryModals();
                setupSubcategoryModals();
            }, 1000);
        }
    }, 100);
});

// Configuración adicional cuando la ventana esté completamente cargada
window.addEventListener('load', function() {
    console.log('Ventana completamente cargada, configurando modales...');

    // Esperar un poco más y reconfigurar
    setTimeout(() => {
        setupCategoryModals();
        setupSubcategoryModals();

        // Verificar nuevamente
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');

        console.log('Verificación final de botones:', {
            addCategoryBtn: !!addCategoryBtn,
            addSubcategoryBtn: !!addSubcategoryBtn
        });

        // Si aún no funcionan, agregar event listeners directamente
        if (addCategoryBtn && !addCategoryBtn.hasAttribute('data-listener-added')) {
            addCategoryBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Event listener directo - Abriendo modal de categoría');
                openCategoryModal();
            });
            addCategoryBtn.setAttribute('data-listener-added', 'true');
            console.log('Event listener directo agregado al botón de categoría');
        }

        if (addSubcategoryBtn && !addSubcategoryBtn.hasAttribute('data-listener-added')) {
            addSubcategoryBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Event listener directo - Abriendo modal de subcategoría');
                openSubcategoryModal();
            });
            addSubcategoryBtn.setAttribute('data-listener-added', 'true');
            console.log('Event listener directo agregado al botón de subcategoría');
        }
    }, 500);
});

// ==================== FUNCIONES GLOBALES ====================
// Hacer funciones disponibles globalmente para los onclick en HTML
window.selectCategory = selectCategory;
window.removeCategory = removeCategory;
window.selectSubcategory = selectSubcategory;
window.removeSubcategory = removeSubcategory;
window.toggleCategoryDropdown = toggleCategoryDropdown;
window.toggleSubcategoryDropdown = toggleSubcategoryDropdown;
window.saveSimpleCategoriesConfiguration = saveSimpleCategoriesConfiguration;
window.initializeSimpleCategoriesManagement = initializeSimpleCategoriesManagement;

// Función de prueba para debugging
window.testDropdown = function() {
    console.log('=== PRUEBA DE DROPDOWN ===');

    const dropdown = document.getElementById('categoryDropdownMenu');
    const btn = document.getElementById('categoryDropdownBtn');

    console.log('Elementos:', {
        dropdown: dropdown,
        btn: btn,
        dropdownExists: !!dropdown,
        btnExists: !!btn
    });

    if (dropdown) {
        console.log('Dropdown innerHTML:', dropdown.innerHTML);
        console.log('Dropdown classes:', dropdown.className);
        console.log('Dropdown style display:', dropdown.style.display);
        console.log('Dropdown computed display:', window.getComputedStyle(dropdown).display);

        // Forzar mostrar dropdown
        dropdown.style.display = 'block';
        dropdown.style.position = 'absolute';
        dropdown.style.top = '100%';
        dropdown.style.left = '0';
        dropdown.style.right = '0';
        dropdown.style.background = 'white';
        dropdown.style.border = '2px solid #ccc';
        dropdown.style.zIndex = '99999';
        dropdown.classList.add('show');

        console.log('Dropdown forzado a mostrar');
    }

    if (btn) {
        btn.classList.add('active');
        console.log('Botón marcado como activo');
    }
};

// Función de emergencia para cargar y mostrar dropdown
window.fixDropdown = function() {
    console.log('🔧 REPARANDO DROPDOWN...');

    const dropdown = document.getElementById('categoryDropdownMenu');
    const btn = document.getElementById('categoryDropdownBtn');

    if (!dropdown || !btn) {
        console.error('❌ No se encontraron los elementos');
        return;
    }

    console.log('Elementos encontrados:', { dropdown, btn });

    // 1. Cargar categorías desde el archivo de datos
    inicializarDatosCategorias();

    if (availableCategories.length > 0) {
        loadCategoryDropdown();
        console.log('✅ Categorías cargadas desde archivo de datos');
    } else {
        dropdown.innerHTML = `
            <div class="dropdown-item disabled" style="padding: 0.75rem 1rem; display: flex; align-items: center; gap: 0.75rem; color: #6b7280;">
                <i class="fas fa-info-circle" style="color: #6b7280;"></i>
                <span>No hay categorías disponibles</span>
            </div>
        `;
    }

    // 2. Configurar estilos del dropdown
    dropdown.style.position = 'absolute';
    dropdown.style.top = '100%';
    dropdown.style.left = '0';
    dropdown.style.right = '0';
    dropdown.style.background = 'white';
    dropdown.style.border = '2px solid #e5e7eb';
    dropdown.style.borderRadius = '8px';
    dropdown.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
    dropdown.style.zIndex = '99999';
    dropdown.style.maxHeight = '300px';
    dropdown.style.overflowY = 'auto';

    // 3. Configurar botón
    btn.onclick = function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🖱️ Botón clickeado - fixDropdown');

        if (dropdown.classList.contains('show')) {
            dropdown.classList.remove('show');
            btn.classList.remove('active');
            console.log('🔒 Dropdown cerrado');
        } else {
            // Forzar z-index alto antes de mostrar
            dropdown.style.zIndex = '99999';
            dropdown.classList.add('show');
            btn.classList.add('active');
            console.log('🔓 Dropdown abierto');
        }
    };

    console.log('🎉 Dropdown reparado completamente! Haz clic en "Seleccionar"');
};

// Función súper simple para forzar visualización
window.forceShowDropdown = function() {
    console.log('🚀 FORZANDO VISUALIZACIÓN DEL DROPDOWN...');

    const dropdown = document.getElementById('categoryDropdownMenu');
    const btn = document.getElementById('categoryDropdownBtn');

    if (!dropdown || !btn) {
        console.error('❌ Elementos no encontrados');
        return;
    }

    // Cargar categorías desde el archivo de datos
    inicializarDatosCategorias();

    if (availableCategories.length > 0) {
        const categoriesHTML = availableCategories.map(category => `
            <div class="dropdown-item" onclick="selectCategory('${category.id}')" style="padding: 0.75rem 1rem; cursor: pointer; display: flex; align-items: center; gap: 0.75rem; border-bottom: 1px solid #e5e7eb; background: white;">
                <i class="${category.icon}" style="color: ${category.color};"></i>
                <span>${category.name}</span>
            </div>
        `).join('');
        dropdown.innerHTML = categoriesHTML;
        console.log('✅ Categorías cargadas:', availableCategories.length);
    } else {
        dropdown.innerHTML = `
            <div style="padding: 0.75rem 1rem; display: flex; align-items: center; gap: 0.75rem; background: white; color: #6b7280;">
                <i class="fas fa-info-circle" style="color: #6b7280;"></i>
                <span>No hay categorías disponibles</span>
            </div>
        `;
    }

    // Forzar estilos completamente
    dropdown.style.cssText = `
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        background: white !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
        z-index: 999999 !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    `;

    dropdown.classList.add('show');
    btn.classList.add('active');

    console.log('✅ Dropdown forzado a mostrar');
    console.log('Dropdown rect:', dropdown.getBoundingClientRect());
};

// Función de diagnóstico completo
window.diagnosticarCategorias = function() {
    console.log('🔧 === DIAGNÓSTICO COMPLETO DE CATEGORÍAS ===');

    // 1. Verificar archivo de datos
    console.log('1️⃣ Verificando archivo de datos...');
    console.log('CATEGORIAS_DATA existe:', typeof CATEGORIAS_DATA !== 'undefined');
    if (typeof CATEGORIAS_DATA !== 'undefined') {
        console.log('Número de categorías:', Object.keys(CATEGORIAS_DATA).length);
        console.log('Primera categoría:', Object.values(CATEGORIAS_DATA)[0]);
    }

    // 2. Verificar elementos HTML
    console.log('2️⃣ Verificando elementos HTML...');
    const dropdown = document.getElementById('categoryDropdownMenu');
    const btn = document.getElementById('categoryDropdownBtn');
    console.log('Dropdown existe:', !!dropdown);
    console.log('Botón existe:', !!btn);

    // 3. Verificar variables JavaScript
    console.log('3️⃣ Verificando variables JavaScript...');
    console.log('availableCategories length:', availableCategories.length);
    console.log('availableCategories:', availableCategories);

    // 4. Intentar cargar datos
    console.log('4️⃣ Intentando cargar datos...');
    inicializarDatosCategorias();

    // 5. Intentar cargar dropdown
    console.log('5️⃣ Intentando cargar dropdown...');
    if (dropdown) {
        loadCategoryDropdown();
        console.log('Contenido del dropdown:', dropdown.innerHTML.substring(0, 200) + '...');
    }

    // 6. Mostrar dropdown
    console.log('6️⃣ Intentando mostrar dropdown...');
    if (dropdown && btn) {
        dropdown.style.display = 'block';
        dropdown.style.position = 'absolute';
        dropdown.style.top = '100%';
        dropdown.style.left = '0';
        dropdown.style.right = '0';
        dropdown.style.background = 'white';
        dropdown.style.border = '2px solid #e5e7eb';
        dropdown.style.zIndex = '99999';
        console.log('Dropdown forzado a mostrar');
    }

    console.log('🎯 === FIN DEL DIAGNÓSTICO ===');
};

// Funciones alternativas simples para los modales
// Esta función ya existe más arriba, eliminando duplicado

// Esta función también ya existe más arriba, eliminando duplicado

// Función de prueba para verificar que todo funciona
window.testModals = function() {
    console.log('Probando modales...');
    console.log('openCategoryModal disponible:', typeof window.openCategoryModal);
    console.log('openSubcategoryModal disponible:', typeof window.openSubcategoryModal);

    // Probar modal de categoría
    try {
        window.openCategoryModal();
        console.log('Modal de categoría abierto exitosamente');
    } catch (error) {
        console.error('Error al abrir modal de categoría:', error);
    }
};

// ==================== EXPORT PARA DEBUGGING ====================
window.AdminApp = {
    switchSection,
    updateKPIs,
    showNotification,
    formatCurrency,
    formatNumber,
    currentSection,
    currentTab,
    currentPeriod,
    selectCategory,
    loadSubcategories,
    initializeCategoryManagement,
    categoriesData,
    currentCategory,
    renderCategories
};

// Agregar al final de administrador-scripts.js

// Variables globales para categorías
let selectedCategories = [];
let selectedSubcategories = [];
let categoriesData = {};

// Función para alternar dropdown de categorías
function toggleCategoryDropdown() {
    const dropdown = document.getElementById('categoryDropdownMenu');
    const btn = document.getElementById('categoryDropdownBtn');
    
    dropdown.classList.toggle('show');
    btn.classList.toggle('active');
    
    // Cerrar el dropdown de subcategorías si está abierto
    const subDropdown = document.getElementById('subcategoryDropdownMenu');
    const subBtn = document.getElementById('subcategoryDropdownBtn');
    subDropdown.classList.remove('show');
    subBtn.classList.remove('active');
    
    // Cargar categorías si no están cargadas
    if (!dropdown.hasChildNodes() || dropdown.children.length === 0) {
        loadCategories();
    }
}

// Función para alternar dropdown de subcategorías
function toggleSubcategoryDropdown() {
    const dropdown = document.getElementById('subcategoryDropdownMenu');
    const btn = document.getElementById('subcategoryDropdownBtn');
    
    // Solo permitir si hay categorías seleccionadas
    if (selectedCategories.length === 0) {
        alert('Primero debes seleccionar al menos una categoría');
        return;
    }
    
    dropdown.classList.toggle('show');
    btn.classList.toggle('active');
    
    // Cerrar el dropdown de categorías si está abierto
    const catDropdown = document.getElementById('categoryDropdownMenu');
    const catBtn = document.getElementById('categoryDropdownBtn');
    catDropdown.classList.remove('show');
    catBtn.classList.remove('active');
    
    // Cargar subcategorías basadas en categorías seleccionadas
    loadSubcategories();
}

// Función para cargar categorías
function loadCategories() {
    const dropdown = document.getElementById('categoryDropdownMenu');
    
    // Datos de ejemplo (deberías reemplazar con tus datos reales)
    const categories = [
        { id: 'electronica', name: 'Electrónica', icon: 'fas fa-laptop' },
        { id: 'moda', name: 'Moda y Ropa', icon: 'fas fa-tshirt' },
        { id: 'hogar', name: 'Hogar y Jardín', icon: 'fas fa-home' },
        { id: 'deportes', name: 'Deportes', icon: 'fas fa-dumbbell' },
        { id: 'libros', name: 'Libros', icon: 'fas fa-book' },
        { id: 'automotriz', name: 'Automotriz', icon: 'fas fa-car' }
    ];
    
    dropdown.innerHTML = '';
    
    categories.forEach(category => {
        const item = document.createElement('div');
        item.className = 'dropdown-item';
        item.innerHTML = `
            <i class="${category.icon}"></i>
            <span>${category.name}</span>
        `;
        item.onclick = () => selectCategory(category);
        dropdown.appendChild(item);
    });
}

// Función para cargar subcategorías
function loadSubcategories() {
    const dropdown = document.getElementById('subcategoryDropdownMenu');
    
    // Datos de ejemplo de subcategorías
    const subcategories = {
        'electronica': [
            { id: 'smartphones', name: 'Smartphones', icon: 'fas fa-mobile-alt' },
            { id: 'laptops', name: 'Laptops', icon: 'fas fa-laptop' },
            { id: 'televisores', name: 'Televisores', icon: 'fas fa-tv' },
            { id: 'audio', name: 'Audio', icon: 'fas fa-headphones' }
        ],
        'moda': [
            { id: 'ropa-hombre', name: 'Ropa Hombre', icon: 'fas fa-tshirt' },
            { id: 'ropa-mujer', name: 'Ropa Mujer', icon: 'fas fa-female' },
            { id: 'calzado', name: 'Calzado', icon: 'fas fa-shoe-prints' },
            { id: 'accesorios', name: 'Accesorios', icon: 'fas fa-ring' }
        ],
        'hogar': [
            { id: 'muebles', name: 'Muebles', icon: 'fas fa-couch' },
            { id: 'decoracion', name: 'Decoración', icon: 'fas fa-palette' },
            { id: 'jardin', name: 'Jardín', icon: 'fas fa-seedling' },
            { id: 'cocina', name: 'Cocina', icon: 'fas fa-utensils' }
        ],
        'deportes': [
            { id: 'fitness', name: 'Fitness', icon: 'fas fa-dumbbell' },
            { id: 'futbol', name: 'Fútbol', icon: 'fas fa-futbol' },
            { id: 'running', name: 'Running', icon: 'fas fa-running' },
            { id: 'natacion', name: 'Natación', icon: 'fas fa-swimmer' }
        ]
    };
    
    dropdown.innerHTML = '';
    
    // Obtener subcategorías de todas las categorías seleccionadas
    let availableSubcategories = [];
    selectedCategories.forEach(categoryId => {
        if (subcategories[categoryId]) {
            availableSubcategories = [...availableSubcategories, ...subcategories[categoryId]];
        }
    });
    
    availableSubcategories.forEach(subcategory => {
        const item = document.createElement('div');
        item.className = 'dropdown-item';
        item.innerHTML = `
            <i class="${subcategory.icon}"></i>
            <span>${subcategory.name}</span>
        `;
        item.onclick = () => selectSubcategory(subcategory);
        dropdown.appendChild(item);
    });
}

// Función para seleccionar categoría
function selectCategory(category) {
    if (!selectedCategories.includes(category.id)) {
        selectedCategories.push(category.id);
        addCategoryBlock(category);
        updateSubcategoryDropdown();
        showSaveButton();
    }
    
    // Cerrar dropdown
    document.getElementById('categoryDropdownMenu').classList.remove('show');
    document.getElementById('categoryDropdownBtn').classList.remove('active');
}

// Función para seleccionar subcategoría
function selectSubcategory(subcategory) {
    if (!selectedSubcategories.includes(subcategory.id)) {
        selectedSubcategories.push(subcategory.id);
        addSubcategoryBlock(subcategory);
        showSaveButton();
    }
    
    // Cerrar dropdown
    document.getElementById('subcategoryDropdownMenu').classList.remove('show');
    document.getElementById('subcategoryDropdownBtn').classList.remove('active');
}

// Función para agregar bloque de categoría seleccionada
function addCategoryBlock(category) {
    const container = document.getElementById('selectedCategoriesContainer');
    
    const block = document.createElement('div');
    block.className = 'selected-item-block';
    block.setAttribute('data-id', category.id);
    block.innerHTML = `
        <i class="${category.icon}"></i>
        <span>${category.name}</span>
        <button class="remove-item-btn" onclick="removeCategory('${category.id}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    container.appendChild(block);
}

// Función para agregar bloque de subcategoría seleccionada
function addSubcategoryBlock(subcategory) {
    const container = document.getElementById('selectedSubcategoriesContainer');
    
    const block = document.createElement('div');
    block.className = 'selected-item-block';
    block.setAttribute('data-id', subcategory.id);
    block.innerHTML = `
        <i class="${subcategory.icon}"></i>
        <span>${subcategory.name}</span>
        <button class="remove-item-btn" onclick="removeSubcategory('${subcategory.id}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    container.appendChild(block);
}

// Función para remover categoría
function removeCategory(categoryId) {
    selectedCategories = selectedCategories.filter(id => id !== categoryId);
    document.querySelector(`[data-id="${categoryId}"]`).remove();
    updateSubcategoryDropdown();
    
    if (selectedCategories.length === 0 && selectedSubcategories.length === 0) {
        hideSaveButton();
    }
}

// Función para remover subcategoría
function removeSubcategory(subcategoryId) {
    selectedSubcategories = selectedSubcategories.filter(id => id !== subcategoryId);
    document.querySelector(`#selectedSubcategoriesContainer [data-id="${subcategoryId}"]`).remove();
    
    if (selectedCategories.length === 0 && selectedSubcategories.length === 0) {
        hideSaveButton();
    }
}

// Función para actualizar dropdown de subcategorías
function updateSubcategoryDropdown() {
    const btn = document.getElementById('subcategoryDropdownBtn');
    
    if (selectedCategories.length > 0) {
        btn.disabled = false;
        btn.classList.remove('disabled');
    } else {
        btn.disabled = true;
        btn.classList.add('disabled');
        // Limpiar subcategorías seleccionadas
        selectedSubcategories = [];
        document.getElementById('selectedSubcategoriesContainer').innerHTML = '';
    }
}

// Función para mostrar botón de guardar
function showSaveButton() {
    document.getElementById('saveCategoriesBtn').style.display = 'block';
}

// Función para ocultar botón de guardar
function hideSaveButton() {
    document.getElementById('saveCategoriesBtn').style.display = 'none';
}

// Función para guardar configuración
function saveSimpleCategoriesConfiguration() {
    if (selectedCategories.length === 0 && selectedSubcategories.length === 0) {
        alert('No hay categorías o subcategorías seleccionadas para guardar');
        return;
    }
    
    const configuration = {
        categories: selectedCategories,
        subcategories: selectedSubcategories,
        timestamp: new Date().toISOString()
    };
    
    console.log('Configuración guardada:', configuration);
    
    // Aquí puedes agregar la lógica para enviar al servidor
    // Por ejemplo: enviar via AJAX o fetch
    
    alert('Configuración guardada exitosamente');
    hideSaveButton();
}

// Cerrar dropdowns al hacer clic fuera
document.addEventListener('click', function(event) {
    const categoryDropdown = document.getElementById('categoryDropdownMenu');
    const categoryBtn = document.getElementById('categoryDropdownBtn');
    const subcategoryDropdown = document.getElementById('subcategoryDropdownMenu');
    const subcategoryBtn = document.getElementById('subcategoryDropdownBtn');
    
    if (!categoryBtn.contains(event.target) && !categoryDropdown.contains(event.target)) {
        categoryDropdown.classList.remove('show');
        categoryBtn.classList.remove('active');
    }
    
    if (!subcategoryBtn.contains(event.target) && !subcategoryDropdown.contains(event.target)) {
        subcategoryDropdown.classList.remove('show');
        subcategoryBtn.classList.remove('active');
    }
});
