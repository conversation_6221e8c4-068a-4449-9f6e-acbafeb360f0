/**
 * ADMINISTRADOR DE TIENDA - SCRIPTS PRINCIPALES
 * Sistema moderno de administración para tienda online
 */

// ==================== VARIABLES GLOBALES ====================
let currentSection = 'dashboard';
let currentTab = 'destacados';
let currentPeriod = 'today';

// Datos simulados
const mockData = {
    kpis: {
        today: {
            visits: { value: 1254, change: 12.5, trend: 'positive' },
            time: { value: '3m 42s', change: '0:45s', trend: 'positive' },
            pages: { value: 2.8, change: 0.3, trend: 'positive' },
            bounce: { value: 32, change: -5.2, trend: 'negative' },
            mobile: { value: 68, change: 3.1, trend: 'positive' },
            conversions: { value: 4.2, change: 0.8, trend: 'positive' }
        },
        week: {
            visits: { value: 8750, change: 18.2, trend: 'positive' },
            time: { value: '4m 15s', change: '1:20s', trend: 'positive' },
            pages: { value: 3.1, change: 0.5, trend: 'positive' },
            bounce: { value: 28, change: -8.1, trend: 'negative' },
            mobile: { value: 72, change: 4.2, trend: 'positive' },
            conversions: { value: 5.1, change: 1.2, trend: 'positive' }
        },
        month: {
            visits: { value: 35200, change: 15.8, trend: 'positive' },
            time: { value: '4m 32s', change: '1:45s', trend: 'positive' },
            pages: { value: 3.4, change: 0.7, trend: 'positive' },
            bounce: { value: 25, change: -12.3, trend: 'negative' },
            mobile: { value: 75, change: 6.8, trend: 'positive' },
            conversions: { value: 6.3, change: 2.1, trend: 'positive' }
        },
        year: {
            visits: { value: 425000, change: 24.5, trend: 'positive' },
            time: { value: '5m 18s', change: '2:30s', trend: 'positive' },
            pages: { value: 4.1, change: 1.2, trend: 'positive' },
            bounce: { value: 22, change: -18.5, trend: 'negative' },
            mobile: { value: 78, change: 12.4, trend: 'positive' },
            conversions: { value: 7.8, change: 3.5, trend: 'positive' }
        }
    }
};

// ==================== INICIALIZACIÓN ====================
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Administrador de Tienda iniciado');

    initializeApp();
    setupEventListeners();
    loadDashboardData();
    initializeAnimations();
});

/**
 * Inicializar aplicación
 */
function initializeApp() {
    // Configurar menú móvil
    setupMobileMenu();

    // Configurar navegación
    setupNavigation();

    // Configurar filtros de tiempo
    setupTimeFilters();

    // Configurar pestañas de productos
    setupProductTabs();

    // Mostrar notificación de bienvenida
    setTimeout(() => {
        showNotification('¡Bienvenido al panel de administración!', 'success');
    }, 1000);
}

/**
 * Configurar event listeners
 */
function setupEventListeners() {
    // Botones de acción
    const addProductBtn = document.getElementById('addProductBtn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', openProductModal);
    }

    // Botones de notificación y configuración
    const notificationBtn = document.querySelector('.notification-btn');
    const settingsBtn = document.querySelector('.settings-btn');

    if (notificationBtn) {
        notificationBtn.addEventListener('click', showNotifications);
    }

    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettings);
    }

    // Búsqueda
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // Toggles de productos
    document.addEventListener('change', function(e) {
        if (e.target.matches('.toggle-switch input')) {
            handleProductToggle(e.target);
        }
    });

    // Botones de acción de productos
    document.addEventListener('click', function(e) {
        if (e.target.closest('.action-btn.edit')) {
            editProduct(e.target.closest('.product-card'));
        } else if (e.target.closest('.action-btn.duplicate')) {
            duplicateProduct(e.target.closest('.product-card'));
        } else if (e.target.closest('.action-btn.delete')) {
            deleteProduct(e.target.closest('.product-card'));
        }
    });
}

/**
 * Configurar menú móvil
 */
function setupMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.admin-sidebar');

    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });

        // Cerrar al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !mobileToggle.contains(e.target) &&
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Cerrar con Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });
    }
}

/**
 * Configurar navegación del sidebar
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const section = this.dataset.section;
            if (section) {
                switchSection(section);

                // Actualizar estado activo
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');

                // Cerrar menú móvil si está abierto
                if (window.innerWidth <= 768) {
                    document.querySelector('.admin-sidebar').classList.remove('active');
                }
            }
        });
    });
}

/**
 * Configurar filtros de tiempo
 */
function setupTimeFilters() {
    const timeButtons = document.querySelectorAll('.time-btn');

    timeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            if (period) {
                currentPeriod = period;

                // Actualizar estado activo
                timeButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Actualizar KPIs
                updateKPIs(period);

                showNotification(`Datos actualizados para: ${getPeriodLabel(period)}`, 'info');
            }
        });
    });
}

/**
 * Configurar pestañas de productos
 */
function setupProductTabs() {
    const tabButtons = document.querySelectorAll('.admin-tab');

    tabButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.dataset.tab;
            if (tab) {
                currentTab = tab;

                // Actualizar estado activo
                tabButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Mostrar contenido de la pestaña
                showTabContent(tab);

                showNotification(`Mostrando productos: ${getTabLabel(tab)}`, 'info');
            }
        });
    });
}

/**
 * Cargar datos del dashboard
 */
function loadDashboardData() {
    updateKPIs(currentPeriod);
    loadRecentOrders();
    loadTopProducts();
    loadSystemAlerts();
}

/**
 * Actualizar KPIs
 */
function updateKPIs(period) {
    const data = mockData.kpis[period];
    if (!data) return;

    // Actualizar valores con animación
    updateKPIValue('.visits .kpi-value', data.visits.value);
    updateKPIValue('.time .kpi-value', data.time.value);
    updateKPIValue('.pages .kpi-value', data.pages.value);
    updateKPIValue('.bounce .kpi-value', data.bounce.value + '%');
    updateKPIValue('.mobile .kpi-value', data.mobile.value + '%');
    updateKPIValue('.conversions .kpi-value', data.conversions.value + '%');

    // Actualizar cambios
    updateKPIChange('.visits .kpi-change', data.visits.change, data.visits.trend);
    updateKPIChange('.time .kpi-change', data.time.change, data.time.trend);
    updateKPIChange('.pages .kpi-change', data.pages.change, data.pages.trend);
    updateKPIChange('.bounce .kpi-change', data.bounce.change, data.bounce.trend);
    updateKPIChange('.mobile .kpi-change', data.mobile.change, data.mobile.trend);
    updateKPIChange('.conversions .kpi-change', data.conversions.change, data.conversions.trend);
}

/**
 * Animar valor numérico
 */
function animateValue(selector, endValue, isCurrency = false) {
    const element = document.querySelector(selector);
    if (!element) return;

    const startValue = 0;
    const duration = 1000;
    const startTime = performance.now();

    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const currentValue = Math.floor(startValue + (endValue - startValue) * progress);

        if (isCurrency) {
            element.textContent = formatCurrency(currentValue);
        } else {
            element.textContent = formatNumber(currentValue);
        }

        if (progress < 1) {
            requestAnimationFrame(updateValue);
        }
    }

    requestAnimationFrame(updateValue);
}

/**
 * Actualizar indicador de cambio KPI
 */
function updateKPIChange(selector, change, trend) {
    const element = document.querySelector(selector);
    if (!element) return;

    const icon = element.querySelector('i');
    const span = element.querySelector('span');

    // Actualizar clases
    element.className = `kpi-change ${trend}`;

    // Actualizar icono
    if (trend === 'positive') {
        icon.className = 'fas fa-arrow-up';
    } else if (trend === 'negative') {
        icon.className = 'fas fa-arrow-down';
    } else {
        icon.className = 'fas fa-minus';
    }

    // Actualizar texto
    const prefix = trend === 'positive' ? '+' : trend === 'negative' ? '-' : '';
    span.textContent = `${prefix}${Math.abs(change)}% vs mes anterior`;
}

/**
 * Cambiar sección
 */
function switchSection(section) {
    currentSection = section;

    // Actualizar título
    const pageTitle = document.querySelector('.page-title');
    const pageSubtitle = document.querySelector('.page-subtitle');

    if (pageTitle && pageSubtitle) {
        const sectionInfo = getSectionInfo(section);
        pageTitle.textContent = sectionInfo.title;
        pageSubtitle.textContent = sectionInfo.subtitle;
    }

    // Aquí se podría implementar la lógica para mostrar/ocultar secciones
    console.log(`Cambiando a sección: ${section}`);
}

/**
 * Obtener información de la sección
 */
function getSectionInfo(section) {
    const sections = {
        dashboard: { title: 'Dashboard', subtitle: 'Resumen general de tu tienda' },
        productos: { title: 'Productos', subtitle: 'Gestiona tu catálogo de productos' },
        categorias: { title: 'Categorías', subtitle: 'Organiza tus productos por categorías' },
        inventario: { title: 'Inventario', subtitle: 'Control de stock y almacén' },
        pedidos: { title: 'Pedidos', subtitle: 'Gestiona los pedidos de tus clientes' },
        clientes: { title: 'Clientes', subtitle: 'Administra tu base de clientes' },
        promociones: { title: 'Promociones', subtitle: 'Crea y gestiona ofertas especiales' },
        tienda: { title: 'Configuración', subtitle: 'Ajustes generales de la tienda' },
        reportes: { title: 'Reportes', subtitle: 'Análisis y estadísticas detalladas' }
    };

    return sections[section] || { title: 'Dashboard', subtitle: 'Panel de administración' };
}

/**
 * Obtener etiqueta del período
 */
function getPeriodLabel(period) {
    const labels = {
        today: 'Hoy',
        week: 'Esta semana',
        month: 'Este mes',
        year: 'Este año'
    };
    return labels[period] || 'Hoy';
}

/**
 * Obtener etiqueta de la pestaña
 */
function getTabLabel(tab) {
    const labels = {
        destacados: 'Destacados',
        ofertas: 'Ofertas',
        novedades: 'Novedades',
        'mas-vistos': 'Más Vistos',
        tendencias: 'Tendencias',
        liquidaciones: 'Liquidaciones'
    };
    return labels[tab] || 'Destacados';
}

// ==================== GESTIÓN DE PRODUCTOS ====================

/**
 * Actualizar valor de KPI
 */
function updateKPIValue(selector, value) {
    const element = document.querySelector(selector);
    if (element) {
        element.textContent = typeof value === 'number' ? formatNumber(value) : value;
    }
}

/**
 * Mostrar contenido de pestaña
 */
function showTabContent(tab) {
    // Ocultar todas las pestañas
    const allTabs = document.querySelectorAll('.admin-tab-content');
    allTabs.forEach(tabContent => {
        tabContent.classList.remove('active');
    });

    // Mostrar la pestaña seleccionada
    const selectedTab = document.getElementById(tab);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    console.log(`Mostrando productos de: ${tab}`);
}

/**
 * Abrir modal de producto
 */
function openProductModal() {
    showNotification('Abriendo formulario de nuevo producto...', 'info');

    // Aquí se implementaría la lógica del modal
    console.log('Abriendo modal de producto');
}

/**
 * Editar producto
 */
function editProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    showNotification(`Editando producto: ${productName}`, 'info');

    console.log('Editando producto:', productName);
}

/**
 * Duplicar producto
 */
function duplicateProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    showNotification(`Duplicando producto: ${productName}`, 'success');

    console.log('Duplicando producto:', productName);
}

/**
 * Eliminar producto
 */
function deleteProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;

    if (confirm(`¿Estás seguro de que quieres eliminar "${productName}"?`)) {
        // Animar eliminación
        productCard.style.transform = 'scale(0.8)';
        productCard.style.opacity = '0';

        setTimeout(() => {
            productCard.remove();
            showNotification(`Producto "${productName}" eliminado`, 'success');
        }, 300);
    }
}

/**
 * Manejar toggle de producto
 */
function handleProductToggle(toggle) {
    const productCard = toggle.closest('.product-card');
    const productName = productCard.querySelector('.product-name').textContent;
    const isActive = toggle.checked;

    const status = isActive ? 'activado' : 'desactivado';
    showNotification(`Producto "${productName}" ${status}`, 'info');

    // Animar cambio
    productCard.style.opacity = isActive ? '1' : '0.7';
}

/**
 * Manejar búsqueda
 */
function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    console.log('Buscando:', query);

    if (query.length > 2) {
        showNotification(`Buscando: "${query}"`, 'info');
    }
}

// ==================== DATOS SIMULADOS ====================

/**
 * Cargar pedidos recientes
 */
function loadRecentOrders() {
    console.log('Cargando pedidos recientes...');
    // Los datos ya están en el HTML, aquí se podría implementar carga dinámica
}

/**
 * Cargar productos más vendidos
 */
function loadTopProducts() {
    console.log('Cargando productos más vendidos...');
    // Los datos ya están en el HTML, aquí se podría implementar carga dinámica
}

/**
 * Cargar alertas del sistema
 */
function loadSystemAlerts() {
    console.log('Cargando alertas del sistema...');
    // Los datos ya están en el HTML, aquí se podría implementar carga dinámica
}

// ==================== NOTIFICACIONES ====================

/**
 * Mostrar notificación
 */
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Agregar estilos si no existen
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                padding: 1rem 1.5rem;
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 1rem;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 400px;
                border-left: 4px solid;
            }
            .notification-success { border-left-color: #10b981; }
            .notification-error { border-left-color: #ef4444; }
            .notification-warning { border-left-color: #f59e0b; }
            .notification-info { border-left-color: #3b82f6; }
            .notification.show { transform: translateX(0); }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                flex: 1;
            }
            .notification-content i {
                font-size: 1.25rem;
            }
            .notification-success i { color: #10b981; }
            .notification-error i { color: #ef4444; }
            .notification-warning i { color: #f59e0b; }
            .notification-info i { color: #3b82f6; }
            .notification-close {
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 4px;
                transition: background 0.2s;
            }
            .notification-close:hover {
                background: #f3f4f6;
            }
        `;
        document.head.appendChild(styles);
    }

    // Agregar al DOM
    document.body.appendChild(notification);

    // Mostrar con animación
    setTimeout(() => notification.classList.add('show'), 100);

    // Configurar cierre
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => closeNotification(notification));

    // Auto-cerrar después de 5 segundos
    setTimeout(() => closeNotification(notification), 5000);
}

/**
 * Cerrar notificación
 */
function closeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Obtener icono de notificación
 */
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Mostrar notificaciones del sistema
 */
function showNotifications() {
    showNotification('Tienes 3 notificaciones nuevas', 'info');
}

/**
 * Abrir configuración
 */
function openSettings() {
    showNotification('Abriendo configuración...', 'info');
}

// ==================== UTILIDADES ====================

/**
 * Formatear moneda
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('es-CL', {
        style: 'currency',
        currency: 'CLP',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Formatear número
 */
function formatNumber(number) {
    return new Intl.NumberFormat('es-CL').format(number);
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Inicializar animaciones
 */
function initializeAnimations() {
    // Observador de intersección para animaciones
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, { threshold: 0.1 });

    // Observar elementos animados
    document.querySelectorAll('.kpi-card, .product-card, .summary-card').forEach(el => {
        observer.observe(el);
    });
}

// ==================== EXPORT PARA DEBUGGING ====================
window.AdminApp = {
    switchSection,
    updateKPIs,
    showNotification,
    formatCurrency,
    formatNumber,
    currentSection,
    currentTab,
    currentPeriod
};
