/**
 * ADMINISTRADOR DE TIENDA - SCRIPTS PRINCIPALES
 * Sistema moderno de administración para tienda online
 */

// ==================== VARIABLES GLOBALES ====================
let currentSection = 'dashboard';
let currentTab = 'destacados';
let currentPeriod = 'today';

// Datos simulados
const mockData = {
    kpis: {
        today: {
            visits: { value: 1254, change: 12.5, trend: 'positive' },
            time: { value: '3m 42s', change: '0:45s', trend: 'positive' },
            pages: { value: 2.8, change: 0.3, trend: 'positive' },
            bounce: { value: 32, change: -5.2, trend: 'negative' },
            mobile: { value: 68, change: 3.1, trend: 'positive' },
            conversions: { value: 4.2, change: 0.8, trend: 'positive' }
        },
        week: {
            visits: { value: 8750, change: 18.2, trend: 'positive' },
            time: { value: '4m 15s', change: '1:20s', trend: 'positive' },
            pages: { value: 3.1, change: 0.5, trend: 'positive' },
            bounce: { value: 28, change: -8.1, trend: 'negative' },
            mobile: { value: 72, change: 4.2, trend: 'positive' },
            conversions: { value: 5.1, change: 1.2, trend: 'positive' }
        },
        month: {
            visits: { value: 35200, change: 15.8, trend: 'positive' },
            time: { value: '4m 32s', change: '1:45s', trend: 'positive' },
            pages: { value: 3.4, change: 0.7, trend: 'positive' },
            bounce: { value: 25, change: -12.3, trend: 'negative' },
            mobile: { value: 75, change: 6.8, trend: 'positive' },
            conversions: { value: 6.3, change: 2.1, trend: 'positive' }
        },
        year: {
            visits: { value: 425000, change: 24.5, trend: 'positive' },
            time: { value: '5m 18s', change: '2:30s', trend: 'positive' },
            pages: { value: 4.1, change: 1.2, trend: 'positive' },
            bounce: { value: 22, change: -18.5, trend: 'negative' },
            mobile: { value: 78, change: 12.4, trend: 'positive' },
            conversions: { value: 7.8, change: 3.5, trend: 'positive' }
        }
    }
};

// ==================== INICIALIZACIÓN ====================
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Administrador de Tienda iniciado');

    initializeApp();
    setupEventListeners();
    loadDashboardData();
    initializeAnimations();
});

/**
 * Inicializar aplicación
 */
function initializeApp() {
    // Configurar menú móvil
    setupMobileMenu();

    // Configurar navegación
    setupNavigation();

    // Configurar filtros de tiempo
    setupTimeFilters();

    // Configurar pestañas de productos
    setupProductTabs();

    // Mostrar notificación de bienvenida
    setTimeout(() => {
        showNotification('¡Bienvenido al panel de administración!', 'success');
    }, 1000);
}

/**
 * Configurar event listeners
 */
function setupEventListeners() {
    // Botones de acción
    const addProductBtn = document.getElementById('addProductBtn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', openProductModal);
    }

    // Botones de notificación y configuración
    const notificationBtn = document.querySelector('.notification-btn');
    const settingsBtn = document.querySelector('.settings-btn');

    if (notificationBtn) {
        notificationBtn.addEventListener('click', showNotifications);
    }

    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettings);
    }

    // Búsqueda
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // Toggles de productos
    document.addEventListener('change', function(e) {
        if (e.target.matches('.toggle-switch input')) {
            handleProductToggle(e.target);
        }
    });

    // Botones de acción de productos
    document.addEventListener('click', function(e) {
        if (e.target.closest('.action-btn.edit')) {
            editProduct(e.target.closest('.product-card'));
        } else if (e.target.closest('.action-btn.duplicate')) {
            duplicateProduct(e.target.closest('.product-card'));
        } else if (e.target.closest('.action-btn.delete')) {
            deleteProduct(e.target.closest('.product-card'));
        }
    });
}

/**
 * Configurar menú móvil
 */
function setupMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.admin-sidebar');

    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });

        // Cerrar al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !mobileToggle.contains(e.target) &&
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Cerrar con Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });
    }
}

/**
 * Configurar navegación del sidebar
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const section = this.dataset.section;
            if (section) {
                switchSection(section);

                // Actualizar estado activo
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');

                // Cerrar menú móvil si está abierto
                if (window.innerWidth <= 768) {
                    document.querySelector('.admin-sidebar').classList.remove('active');
                }
            }
        });
    });
}

/**
 * Configurar filtros de tiempo
 */
function setupTimeFilters() {
    const timeButtons = document.querySelectorAll('.time-btn');

    timeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            if (period) {
                currentPeriod = period;

                // Actualizar estado activo
                timeButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Actualizar KPIs
                updateKPIs(period);
            }
        });
    });
}

/**
 * Configurar pestañas de productos
 */
function setupProductTabs() {
    const tabButtons = document.querySelectorAll('.admin-tab');

    tabButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.dataset.tab;
            if (tab) {
                currentTab = tab;

                // Actualizar estado activo
                tabButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Mostrar contenido de la pestaña
                showTabContent(tab);
            }
        });
    });
}

/**
 * Cargar datos del dashboard
 */
function loadDashboardData() {
    updateKPIs(currentPeriod);
    loadRecentOrders();
    loadTopProducts();
    loadSystemAlerts();
}

/**
 * Actualizar KPIs
 */
function updateKPIs(period) {
    const data = mockData.kpis[period];
    if (!data) return;

    // Actualizar valores con animación
    updateKPIValue('.visits .kpi-value', data.visits.value);
    updateKPIValue('.mobile .kpi-value', data.mobile.value + '%');
    updateKPIValue('.time .kpi-value', data.time.value);
    updateKPIValue('.shared .kpi-value', data.shared.value);
    updateKPIValue('.total-products .kpi-value', data.totalProducts.value);
    updateKPIValue('.categories .kpi-value', data.categories.value);
    updateKPIValue('.products-offer .kpi-value', data.productsOffer.value);
    updateKPIValue('.products-featured .kpi-value', data.productsFeatured.value);

    // Actualizar cambios
    updateKPIChange('.visits .kpi-change', data.visits.change, data.visits.trend);
    updateKPIChange('.mobile .kpi-change', data.mobile.change, data.mobile.trend);
    updateKPIChange('.time .kpi-change', data.time.change, data.time.trend);
    updateKPIChange('.shared .kpi-change', data.shared.change, data.shared.trend);
    updateKPIChange('.total-products .kpi-change', data.totalProducts.change, data.totalProducts.trend);
    updateKPIChange('.categories .kpi-change', data.categories.change, data.categories.trend);
    updateKPIChange('.products-offer .kpi-change', data.productsOffer.change, data.productsOffer.trend);
    updateKPIChange('.products-featured .kpi-change', data.productsFeatured.change, data.productsFeatured.trend);
}

/**
 * Animar valor numérico
 */
function animateValue(selector, endValue, isCurrency = false) {
    const element = document.querySelector(selector);
    if (!element) return;

    const startValue = 0;
    const duration = 1000;
    const startTime = performance.now();

    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const currentValue = Math.floor(startValue + (endValue - startValue) * progress);

        if (isCurrency) {
            element.textContent = formatCurrency(currentValue);
        } else {
            element.textContent = formatNumber(currentValue);
        }

        if (progress < 1) {
            requestAnimationFrame(updateValue);
        }
    }

    requestAnimationFrame(updateValue);
}

/**
 * Actualizar indicador de cambio KPI
 */
function updateKPIChange(selector, change, trend) {
    const element = document.querySelector(selector);
    if (!element) return;

    const icon = element.querySelector('i');
    const span = element.querySelector('span');

    // Actualizar clases
    element.className = `kpi-change ${trend}`;

    // Actualizar icono
    if (trend === 'positive') {
        icon.className = 'fas fa-arrow-up';
    } else if (trend === 'negative') {
        icon.className = 'fas fa-arrow-down';
    } else {
        icon.className = 'fas fa-minus';
    }

    // Actualizar texto - manejar tanto números como strings
    if (typeof change === 'string') {
        span.textContent = change;
    } else {
        const prefix = trend === 'positive' ? '+' : trend === 'negative' ? '-' : '';
        span.textContent = `${prefix}${Math.abs(change)}%`;
    }
}

/**
 * Cambiar sección
 */
function switchSection(section) {
    currentSection = section;

    // Actualizar título
    const pageTitle = document.querySelector('.page-title');
    const pageSubtitle = document.querySelector('.page-subtitle');

    if (pageTitle && pageSubtitle) {
        const sectionInfo = getSectionInfo(section);
        pageTitle.textContent = sectionInfo.title;
        pageSubtitle.textContent = sectionInfo.subtitle;
    }

    // Reconfigurar modales cuando se accede a la sección de categorías
    if (section === 'categorias') {
        setTimeout(() => {
            console.log('Reconfigurando modales para sección de categorías...');
            initializeCategoryManagement();
            setupCategoryButtons();
        }, 100);
    }

    // Aquí se podría implementar la lógica para mostrar/ocultar secciones
    console.log(`Cambiando a sección: ${section}`);
}

/**
 * Obtener información de la sección
 */
function getSectionInfo(section) {
    const sections = {
        dashboard: { title: 'Dashboard', subtitle: 'Resumen general de tu tienda' },
        productos: { title: 'Productos', subtitle: 'Gestiona tu catálogo de productos' },
        categorias: { title: 'Categorías', subtitle: 'Organiza tus productos por categorías' },
        inventario: { title: 'Inventario', subtitle: 'Control de stock y almacén' },
        pedidos: { title: 'Pedidos', subtitle: 'Gestiona los pedidos de tus clientes' },
        clientes: { title: 'Clientes', subtitle: 'Administra tu base de clientes' },
        promociones: { title: 'Promociones', subtitle: 'Crea y gestiona ofertas especiales' },
        tienda: { title: 'Configuración', subtitle: 'Ajustes generales de la tienda' },
        reportes: { title: 'Reportes', subtitle: 'Análisis y estadísticas detalladas' }
    };

    return sections[section] || { title: 'Dashboard', subtitle: 'Panel de administración' };
}

/**
 * Obtener etiqueta del período
 */
function getPeriodLabel(period) {
    const labels = {
        today: 'Hoy',
        week: 'Esta semana',
        month: 'Este mes',
        year: 'Este año'
    };
    return labels[period] || 'Hoy';
}

/**
 * Obtener etiqueta de la pestaña
 */
function getTabLabel(tab) {
    const labels = {
        destacados: 'Destacados',
        ofertas: 'Ofertas',
        novedades: 'Novedades',
        'mas-vistos': 'Más Vistos',
        tendencias: 'Tendencias',
        liquidaciones: 'Liquidaciones'
    };
    return labels[tab] || 'Destacados';
}

// ==================== GESTIÓN DE PRODUCTOS ====================

/**
 * Actualizar valor de KPI
 */
function updateKPIValue(selector, value) {
    const element = document.querySelector(selector);
    if (element) {
        element.textContent = typeof value === 'number' ? formatNumber(value) : value;
    }
}

/**
 * Mostrar contenido de pestaña
 */
function showTabContent(tab) {
    // Ocultar todas las pestañas
    const allTabs = document.querySelectorAll('.admin-tab-content');
    allTabs.forEach(tabContent => {
        tabContent.classList.remove('active');
    });

    // Mostrar la pestaña seleccionada
    const selectedTab = document.getElementById(tab);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    console.log(`Mostrando productos de: ${tab}`);
}

/**
 * Abrir modal de producto
 */
function openProductModal() {
    showNotification('Abriendo formulario de nuevo producto...', 'info');

    // Aquí se implementaría la lógica del modal
    console.log('Abriendo modal de producto');
}

/**
 * Editar producto
 */
function editProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    showNotification(`Editando producto: ${productName}`, 'info');

    console.log('Editando producto:', productName);
}

/**
 * Duplicar producto
 */
function duplicateProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    showNotification(`Duplicando producto: ${productName}`, 'success');

    console.log('Duplicando producto:', productName);
}

/**
 * Eliminar producto
 */
function deleteProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;

    if (confirm(`¿Estás seguro de que quieres eliminar "${productName}"?`)) {
        // Animar eliminación
        productCard.style.transform = 'scale(0.8)';
        productCard.style.opacity = '0';

        setTimeout(() => {
            productCard.remove();
            showNotification(`Producto "${productName}" eliminado`, 'success');
        }, 300);
    }
}

/**
 * Manejar toggle de producto
 */
function handleProductToggle(toggle) {
    const productCard = toggle.closest('.product-card');
    const productName = productCard.querySelector('.product-name').textContent;
    const isActive = toggle.checked;

    const status = isActive ? 'activado' : 'desactivado';
    showNotification(`Producto "${productName}" ${status}`, 'info');

    // Animar cambio
    productCard.style.opacity = isActive ? '1' : '0.7';
}

/**
 * Manejar búsqueda
 */
function handleSearch(event) {
    const query = event.target.value.toLowerCase();
    console.log('Buscando:', query);

    if (query.length > 2) {
        showNotification(`Buscando: "${query}"`, 'info');
    }
}

// ==================== DATOS SIMULADOS ====================

const mockData = {
    kpis: {
        today: {
            visits: { value: 1254, change: 12.5, trend: 'positive' },
            mobile: { value: 68, change: 5.3, trend: 'positive' },
            time: { value: '3m 42s', change: '0:45', trend: 'positive' },
            shared: { value: 42, change: 15.7, trend: 'positive' },
            totalProducts: { value: 124, change: '12% desde el mes pasado', trend: 'positive' },
            categories: { value: 8, change: '2 nuevas este mes', trend: 'positive' },
            productsOffer: { value: 32, change: '5% desde el mes pasado', trend: 'negative' },
            productsFeatured: { value: 16, change: '4 nuevos este mes', trend: 'positive' }
        },
        week: {
            visits: { value: 8750, change: 8.3, trend: 'positive' },
            mobile: { value: 72, change: 4.2, trend: 'positive' },
            time: { value: '4m 15s', change: '1:20', trend: 'positive' },
            shared: { value: 298, change: 18.5, trend: 'positive' },
            totalProducts: { value: 124, change: '8% desde la semana pasada', trend: 'positive' },
            categories: { value: 8, change: '1 nueva esta semana', trend: 'positive' },
            productsOffer: { value: 35, change: '3% desde la semana pasada', trend: 'positive' },
            productsFeatured: { value: 18, change: '2 nuevos esta semana', trend: 'positive' }
        },
        month: {
            visits: { value: 35420, change: 15.7, trend: 'positive' },
            mobile: { value: 70, change: 2.8, trend: 'positive' },
            time: { value: '3m 58s', change: '0:22', trend: 'positive' },
            shared: { value: 1250, change: 22.3, trend: 'positive' },
            totalProducts: { value: 124, change: '12% desde el mes pasado', trend: 'positive' },
            categories: { value: 8, change: '2 nuevas este mes', trend: 'positive' },
            productsOffer: { value: 32, change: '5% desde el mes pasado', trend: 'negative' },
            productsFeatured: { value: 16, change: '4 nuevos este mes', trend: 'positive' }
        },
        year: {
            visits: { value: 425040, change: 22.1, trend: 'positive' },
            mobile: { value: 75, change: 5.3, trend: 'positive' },
            time: { value: '4m 05s', change: '0:18', trend: 'positive' },
            shared: { value: 15680, change: 28.7, trend: 'positive' },
            totalProducts: { value: 124, change: '45% desde el año pasado', trend: 'positive' },
            categories: { value: 8, change: '3 nuevas este año', trend: 'positive' },
            productsOffer: { value: 28, change: '12% desde el año pasado', trend: 'negative' },
            productsFeatured: { value: 20, change: '8 nuevos este año', trend: 'positive' }
        }
    }
};

// ==================== NOTIFICACIONES ====================

/**
 * Mostrar notificación
 */
function showNotification(message, type = 'info') {
    // Crear elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Agregar estilos si no existen
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 12px;
                padding: 1rem 1.5rem;
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 1rem;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 400px;
                border-left: 4px solid;
            }
            .notification-success { border-left-color: #10b981; }
            .notification-error { border-left-color: #ef4444; }
            .notification-warning { border-left-color: #f59e0b; }
            .notification-info { border-left-color: #3b82f6; }
            .notification.show { transform: translateX(0); }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                flex: 1;
            }
            .notification-content i {
                font-size: 1.25rem;
            }
            .notification-success i { color: #10b981; }
            .notification-error i { color: #ef4444; }
            .notification-warning i { color: #f59e0b; }
            .notification-info i { color: #3b82f6; }
            .notification-close {
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 4px;
                transition: background 0.2s;
            }
            .notification-close:hover {
                background: #f3f4f6;
            }
        `;
        document.head.appendChild(styles);
    }

    // Agregar al DOM
    document.body.appendChild(notification);

    // Mostrar con animación
    setTimeout(() => notification.classList.add('show'), 100);

    // Configurar cierre
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => closeNotification(notification));

    // Auto-cerrar después de 5 segundos
    setTimeout(() => closeNotification(notification), 5000);
}

/**
 * Cerrar notificación
 */
function closeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Obtener icono de notificación
 */
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Mostrar notificaciones del sistema
 */
function showNotifications() {
    showNotification('Tienes 3 notificaciones nuevas', 'info');
}

/**
 * Abrir configuración
 */
function openSettings() {
    showNotification('Abriendo configuración...', 'info');
}

// ==================== UTILIDADES ====================

/**
 * Formatear moneda
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('es-CL', {
        style: 'currency',
        currency: 'CLP',
        minimumFractionDigits: 0
    }).format(amount);
}

/**
 * Formatear número
 */
function formatNumber(number) {
    return new Intl.NumberFormat('es-CL').format(number);
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Inicializar animaciones
 */
function initializeAnimations() {
    // Observador de intersección para animaciones
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, { threshold: 0.1 });

    // Observar elementos animados
    document.querySelectorAll('.kpi-card, .product-card, .summary-card').forEach(el => {
        observer.observe(el);
    });
}

// ==================== GESTIÓN DE CATEGORÍAS ====================

// Datos dinámicos para categorías y subcategorías
let categoriesData = {
    electronica: {
        id: 'electronica',
        name: 'Electrónica',
        icon: 'fas fa-laptop',
        description: 'Productos electrónicos y tecnológicos',
        subcategories: [
            { id: 'smartphones', name: 'Smartphones', icon: 'fas fa-mobile-alt', products: 24, description: 'Teléfonos inteligentes' },
            { id: 'computadoras', name: 'Computadoras', icon: 'fas fa-laptop', products: 18, description: 'Laptops y PCs' },
            { id: 'tv-audio', name: 'TV y Audio', icon: 'fas fa-tv', products: 15, description: 'Televisores y equipos de audio' },
            { id: 'gaming', name: 'Gaming', icon: 'fas fa-gamepad', products: 12, description: 'Consolas y videojuegos' },
            { id: 'audio', name: 'Audio', icon: 'fas fa-headphones', products: 9, description: 'Audífonos y parlantes' },
            { id: 'fotografia', name: 'Fotografía', icon: 'fas fa-camera', products: 7, description: 'Cámaras y accesorios' }
        ]
    },
    moda: {
        id: 'moda',
        name: 'Moda y Ropa',
        icon: 'fas fa-tshirt',
        description: 'Ropa y accesorios de moda',
        subcategories: [
            { id: 'hombre', name: 'Ropa Hombre', icon: 'fas fa-male', products: 45, description: 'Ropa masculina' },
            { id: 'mujer', name: 'Ropa Mujer', icon: 'fas fa-female', products: 52, description: 'Ropa femenina' },
            { id: 'calzado', name: 'Calzado', icon: 'fas fa-shoe-prints', products: 28, description: 'Zapatos y zapatillas' },
            { id: 'accesorios', name: 'Accesorios', icon: 'fas fa-ring', products: 19, description: 'Joyas y accesorios' }
        ]
    },
    hogar: {
        id: 'hogar',
        name: 'Hogar y Jardín',
        icon: 'fas fa-home',
        description: 'Productos para el hogar y jardín',
        subcategories: [
            { id: 'muebles', name: 'Muebles', icon: 'fas fa-couch', products: 34, description: 'Muebles para el hogar' },
            { id: 'decoracion', name: 'Decoración', icon: 'fas fa-palette', products: 22, description: 'Artículos decorativos' },
            { id: 'jardin', name: 'Jardín', icon: 'fas fa-seedling', products: 16, description: 'Plantas y jardinería' },
            { id: 'cocina', name: 'Cocina', icon: 'fas fa-utensils', products: 25, description: 'Utensilios de cocina' }
        ]
    },
    deportes: {
        id: 'deportes',
        name: 'Deportes',
        icon: 'fas fa-dumbbell',
        description: 'Artículos deportivos y fitness',
        subcategories: [
            { id: 'fitness', name: 'Fitness', icon: 'fas fa-dumbbell', products: 18, description: 'Equipos de gimnasio' },
            { id: 'futbol', name: 'Fútbol', icon: 'fas fa-futbol', products: 15, description: 'Equipamiento de fútbol' },
            { id: 'running', name: 'Running', icon: 'fas fa-running', products: 12, description: 'Ropa y accesorios para correr' },
            { id: 'natacion', name: 'Natación', icon: 'fas fa-swimmer', products: 8, description: 'Equipos de natación' }
        ]
    }
};

let currentCategory = 'electronica';
let editingCategoryId = null;
let editingSubcategoryId = null;

/**
 * Inicializar gestión de categorías
 */
function initializeCategoryManagement() {
    console.log('Inicializando gestión de categorías...');

    // Verificar que existan los elementos necesarios
    const categoriesList = document.getElementById('categoriesList');
    const subcategoriesGrid = document.getElementById('subcategoriesGrid');

    if (!categoriesList) {
        console.error('No se encontró el elemento categoriesList');
        return;
    }

    if (!subcategoriesGrid) {
        console.error('No se encontró el elemento subcategoriesGrid');
        return;
    }

    // Renderizar categorías iniciales
    renderCategories();

    // Event listeners para modales
    setupCategoryModals();
    setupSubcategoryModals();

    console.log('Gestión de categorías inicializada correctamente');
}

/**
 * Renderizar lista de categorías
 */
function renderCategories() {
    console.log('Renderizando categorías...');

    const categoriesList = document.getElementById('categoriesList');
    if (!categoriesList) {
        console.error('No se encontró categoriesList');
        return;
    }

    categoriesList.innerHTML = '';

    Object.values(categoriesData).forEach(category => {
        const categoryItem = createCategoryItem(category);
        categoriesList.appendChild(categoryItem);
    });

    console.log('Categorías renderizadas:', Object.keys(categoriesData));

    // Seleccionar la categoría actual o la primera disponible
    if (Object.keys(categoriesData).length > 0) {
        const categoryToSelect = currentCategory && categoriesData[currentCategory]
            ? currentCategory
            : Object.keys(categoriesData)[0];

        console.log('Seleccionando categoría:', categoryToSelect);
        selectCategory(categoryToSelect);
    } else {
        // Si no hay categorías, mostrar mensaje vacío
        console.log('No hay categorías, mostrando mensaje vacío');
        showEmptySubcategories();
    }
}

/**
 * Crear elemento de categoría
 */
function createCategoryItem(category) {
    const item = document.createElement('div');
    item.className = 'category-item';
    item.dataset.category = category.id;

    const subcategoryCount = category.subcategories ? category.subcategories.length : 0;

    item.innerHTML = `
        <div class="category-content">
            <div class="category-icon">
                <i class="${category.icon}"></i>
            </div>
            <div class="category-info">
                <h4 class="category-name">${category.name}</h4>
                <span class="category-count">${subcategoryCount} subcategorías</span>
            </div>
        </div>
        <div class="category-actions">
            <button class="action-btn edit-btn" title="Editar" onclick="editCategory('${category.id}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" title="Eliminar" onclick="deleteCategory('${category.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    // Event listener para seleccionar categoría
    item.addEventListener('click', function(e) {
        // No activar si se hizo click en los botones de acción
        if (!e.target.closest('.category-actions')) {
            console.log('Seleccionando categoría:', category.id);
            selectCategory(category.id);
        }
    });

    return item;
}

/**
 * Seleccionar categoría
 */
function selectCategory(categoryId) {
    console.log('Función selectCategory llamada con:', categoryId);
    console.log('Datos de categorías disponibles:', Object.keys(categoriesData));

    if (!categoriesData[categoryId]) {
        console.error('Categoría no encontrada:', categoryId);
        return;
    }

    // Remover clase active de todas las categorías
    document.querySelectorAll('.category-item').forEach(item => {
        item.classList.remove('active');
    });

    // Agregar clase active a la categoría seleccionada
    const selectedItem = document.querySelector(`[data-category="${categoryId}"]`);
    if (selectedItem) {
        selectedItem.classList.add('active');
        currentCategory = categoryId;

        console.log('Categoría seleccionada:', categoryId);

        // Actualizar título de subcategorías
        const categoryName = categoriesData[categoryId].name;
        const selectedCategoryNameElement = document.getElementById('selectedCategoryName');
        if (selectedCategoryNameElement) {
            selectedCategoryNameElement.textContent = categoryName;
            console.log('Título actualizado a:', categoryName);
        }

        // Cargar subcategorías
        loadSubcategories(categoryId);
    } else {
        console.error('No se encontró el elemento DOM para la categoría:', categoryId);
    }
}

/**
 * Cargar subcategorías
 */
function loadSubcategories(categoryId) {
    console.log('Cargando subcategorías para:', categoryId);

    const subcategoriesGrid = document.getElementById('subcategoriesGrid');
    const categoryData = categoriesData[categoryId];

    if (!subcategoriesGrid) {
        console.error('No se encontró el grid de subcategorías');
        return;
    }

    console.log('Datos de la categoría:', categoryData);

    subcategoriesGrid.innerHTML = '';

    if (!categoryData || !categoryData.subcategories || categoryData.subcategories.length === 0) {
        console.log('No hay subcategorías, mostrando mensaje vacío');
        showEmptySubcategories();
        return;
    }

    console.log('Renderizando', categoryData.subcategories.length, 'subcategorías');

    categoryData.subcategories.forEach(subcategory => {
        const subcategoryCard = createSubcategoryCard(subcategory);
        subcategoriesGrid.appendChild(subcategoryCard);
    });
}

/**
 * Mostrar mensaje cuando no hay subcategorías
 */
function showEmptySubcategories() {
    const subcategoriesGrid = document.getElementById('subcategoriesGrid');
    if (!subcategoriesGrid) return;

    subcategoriesGrid.innerHTML = `
        <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: var(--text-secondary);">
            <i class="fas fa-tags" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
            <h3 style="margin: 0 0 0.5rem 0; font-weight: 500;">No hay subcategorías</h3>
            <p style="margin: 0; font-size: var(--font-size-sm);">
                ${currentCategory ? 'Agrega subcategorías para esta categoría' : 'Selecciona una categoría para ver sus subcategorías'}
            </p>
        </div>
    `;
}

/**
 * Crear tarjeta de subcategoría
 */
function createSubcategoryCard(subcategory) {
    const card = document.createElement('div');
    card.className = 'subcategory-card';
    card.innerHTML = `
        <div class="subcategory-header">
            <div class="subcategory-icon">
                <i class="${subcategory.icon}"></i>
            </div>
            <h4 class="subcategory-name">${subcategory.name}</h4>
        </div>
        <div class="subcategory-stats">
            <span class="product-count">${subcategory.products} productos</span>
            <span class="status-badge active">Activa</span>
        </div>
        <div class="subcategory-actions">
            <button class="action-btn edit-btn" title="Editar" onclick="editSubcategory('${subcategory.id}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" title="Eliminar" onclick="deleteSubcategory('${subcategory.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    return card;
}

/**
 * Configurar botones de categorías
 */
function setupCategoryButtons() {
    console.log('Configurando botones de categorías...');

    const addCategoryBtn = document.getElementById('addCategoryBtn');
    const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');

    console.log('Botones encontrados:', {
        addCategoryBtn: !!addCategoryBtn,
        addSubcategoryBtn: !!addSubcategoryBtn
    });

    // Configurar botón de agregar categoría
    if (addCategoryBtn) {
        // Remover listeners existentes
        addCategoryBtn.removeEventListener('click', handleCategoryButtonClick);
        // Agregar nuevo listener
        addCategoryBtn.addEventListener('click', handleCategoryButtonClick);
        console.log('Event listener agregado al botón de categoría');
    }

    // Configurar botón de agregar subcategoría
    if (addSubcategoryBtn) {
        // Remover listeners existentes
        addSubcategoryBtn.removeEventListener('click', handleSubcategoryButtonClick);
        // Agregar nuevo listener
        addSubcategoryBtn.addEventListener('click', handleSubcategoryButtonClick);
        console.log('Event listener agregado al botón de subcategoría');
    }
}

/**
 * Manejar click del botón de categoría
 */
function handleCategoryButtonClick(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('Botón de categoría clickeado - Abriendo modal');
    openCategoryModal();
}

/**
 * Manejar click del botón de subcategoría
 */
function handleSubcategoryButtonClick(e) {
    e.preventDefault();
    e.stopPropagation();
    console.log('Botón de subcategoría clickeado - Abriendo modal');
    openSubcategoryModal();
}

/**
 * Configurar modales de categorías
 */
function setupCategoryModals() {
    console.log('Iniciando configuración de modales de categorías...');

    const modal = document.getElementById('categoryModal');
    const openBtn = document.getElementById('addCategoryBtn');
    const closeBtn = document.getElementById('closeCategoryModal');
    const cancelBtn = document.getElementById('cancelCategoryBtn');
    const saveBtn = document.getElementById('saveCategoryBtn');

    console.log('Elementos encontrados:', {
        modal: !!modal,
        openBtn: !!openBtn,
        closeBtn: !!closeBtn,
        cancelBtn: !!cancelBtn,
        saveBtn: !!saveBtn
    });

    // Los event listeners de los botones se manejan en setupCategoryButtons()
    console.log('Configuración de modales de categorías - botones manejados por setupCategoryButtons');

    // Cerrar modal
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeCategoryModal();
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeCategoryModal();
        });
    }

    // Cerrar al hacer click fuera del modal
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeCategoryModal();
            }
        });
    }

    // Guardar categoría
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            saveCategoryData();
        });
    }

    // Selector de iconos
    setupIconSelector('categoryModal', 'selectedIcon');
}

/**
 * Configurar modales de subcategorías
 */
function setupSubcategoryModals() {
    console.log('Iniciando configuración de modales de subcategorías...');

    const modal = document.getElementById('subcategoryModal');
    const openBtn = document.getElementById('addSubcategoryBtn');
    const closeBtn = document.getElementById('closeSubcategoryModal');
    const cancelBtn = document.getElementById('cancelSubcategoryBtn');
    const saveBtn = document.getElementById('saveSubcategoryBtn');

    console.log('Elementos encontrados:', {
        modal: !!modal,
        openBtn: !!openBtn,
        closeBtn: !!closeBtn,
        cancelBtn: !!cancelBtn,
        saveBtn: !!saveBtn
    });

    // Los event listeners de los botones se manejan en setupCategoryButtons()
    console.log('Configuración de modales de subcategorías - botones manejados por setupCategoryButtons');

    // Cerrar modal
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeSubcategoryModal();
        });
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeSubcategoryModal();
        });
    }

    // Cerrar al hacer click fuera del modal
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeSubcategoryModal();
            }
        });
    }

    // Guardar subcategoría
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            saveSubcategoryData();
        });
    }

    // Selector de iconos
    setupIconSelector('subcategoryModal', 'selectedSubIcon');
}

/**
 * Configurar selector de iconos
 */
function setupIconSelector(modalId, hiddenInputId) {
    const modal = document.getElementById(modalId);
    const hiddenInput = document.getElementById(hiddenInputId);

    modal?.querySelectorAll('.icon-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remover clase active de todas las opciones
            modal.querySelectorAll('.icon-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // Agregar clase active a la opción seleccionada
            this.classList.add('active');

            // Actualizar valor del input oculto
            if (hiddenInput) {
                hiddenInput.value = this.dataset.icon;
            }
        });
    });
}

/**
 * Abrir modal de categoría
 */
function openCategoryModal(categoryData = null) {
    console.log('openCategoryModal llamada');

    const modal = document.getElementById('categoryModal');
    const title = document.getElementById('categoryModalTitle');
    const nameInput = document.getElementById('categoryName');
    const descInput = document.getElementById('categoryDescription');
    const iconInput = document.getElementById('selectedIcon');

    console.log('Elementos del modal de categoría:', {
        modal: !!modal,
        title: !!title,
        nameInput: !!nameInput,
        descInput: !!descInput,
        iconInput: !!iconInput
    });

    if (!modal) {
        console.error('Modal de categoría no encontrado');
        showNotification('Error: Modal de categoría no encontrado', 'error');
        return;
    }

    // Resetear selector de iconos
    modal.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('active');
    });

    if (categoryData) {
        title.textContent = 'Editar Categoría';
        nameInput.value = categoryData.name || '';
        descInput.value = categoryData.description || '';

        // Seleccionar icono actual
        const currentIconOption = modal.querySelector(`[data-icon="${categoryData.icon}"]`);
        if (currentIconOption) {
            currentIconOption.classList.add('active');
            iconInput.value = categoryData.icon;
        }
    } else {
        title.textContent = 'Agregar Nueva Categoría';
        nameInput.value = '';
        descInput.value = '';

        // Seleccionar primer icono por defecto
        const firstIcon = modal.querySelector('.icon-option');
        if (firstIcon) {
            firstIcon.classList.add('active');
            iconInput.value = firstIcon.dataset.icon;
        }
    }

    modal.classList.add('active');
    console.log('Modal de categoría activado, clases:', modal.className);
}

/**
 * Cerrar modal de categoría
 */
function closeCategoryModal() {
    const modal = document.getElementById('categoryModal');
    modal.classList.remove('active');
}

/**
 * Abrir modal de subcategoría
 */
function openSubcategoryModal(subcategoryData = null) {
    console.log('openSubcategoryModal llamada');

    const modal = document.getElementById('subcategoryModal');
    const title = document.getElementById('subcategoryModalTitle');
    const nameInput = document.getElementById('subcategoryName');
    const descInput = document.getElementById('subcategoryDescription');
    const parentSelect = document.getElementById('parentCategory');
    const iconInput = document.getElementById('selectedSubIcon');

    console.log('Elementos del modal de subcategoría:', {
        modal: !!modal,
        title: !!title,
        nameInput: !!nameInput,
        descInput: !!descInput,
        parentSelect: !!parentSelect,
        iconInput: !!iconInput
    });

    if (!modal) {
        console.error('Modal de subcategoría no encontrado');
        showNotification('Error: Modal de subcategoría no encontrado', 'error');
        return;
    }

    // Actualizar opciones del select de categoría padre
    updateParentCategorySelect();

    // Establecer categoría padre actual
    if (parentSelect && currentCategory) {
        parentSelect.value = currentCategory;
    }

    // Resetear selector de iconos
    modal.querySelectorAll('.icon-option').forEach(option => {
        option.classList.remove('active');
    });

    if (subcategoryData) {
        title.textContent = 'Editar Subcategoría';
        nameInput.value = subcategoryData.name || '';
        descInput.value = subcategoryData.description || '';

        // Seleccionar icono actual
        const currentIconOption = modal.querySelector(`[data-icon="${subcategoryData.icon}"]`);
        if (currentIconOption) {
            currentIconOption.classList.add('active');
            iconInput.value = subcategoryData.icon;
        }
    } else {
        title.textContent = 'Agregar Nueva Subcategoría';
        nameInput.value = '';
        descInput.value = '';

        // Seleccionar primer icono por defecto
        const firstIcon = modal.querySelector('.icon-option');
        if (firstIcon) {
            firstIcon.classList.add('active');
            iconInput.value = firstIcon.dataset.icon;
        }
    }

    modal.classList.add('active');
    console.log('Modal de subcategoría activado, clases:', modal.className);
}

/**
 * Cerrar modal de subcategoría
 */
function closeSubcategoryModal() {
    const modal = document.getElementById('subcategoryModal');
    modal.classList.remove('active');
}

/**
 * Guardar datos de categoría
 */
function saveCategoryData() {
    const nameInput = document.getElementById('categoryName');
    const descInput = document.getElementById('categoryDescription');
    const iconInput = document.getElementById('selectedIcon');

    const categoryData = {
        name: nameInput.value.trim(),
        description: descInput.value.trim(),
        icon: iconInput.value
    };

    if (!categoryData.name) {
        showNotification('Por favor ingresa un nombre para la categoría', 'warning');
        return;
    }

    // Verificar si es edición o creación
    if (editingCategoryId) {
        // Editar categoría existente
        if (categoriesData[editingCategoryId]) {
            categoriesData[editingCategoryId].name = categoryData.name;
            categoriesData[editingCategoryId].description = categoryData.description;
            categoriesData[editingCategoryId].icon = categoryData.icon;

            showNotification('Categoría actualizada exitosamente', 'success');
        }
        editingCategoryId = null;
    } else {
        // Crear nueva categoría
        const categoryId = generateCategoryId(categoryData.name);

        // Verificar que no exista ya una categoría con ese ID
        if (categoriesData[categoryId]) {
            showNotification('Ya existe una categoría con ese nombre', 'warning');
            return;
        }

        categoriesData[categoryId] = {
            id: categoryId,
            name: categoryData.name,
            description: categoryData.description,
            icon: categoryData.icon,
            subcategories: []
        };

        showNotification('Categoría creada exitosamente', 'success');

        // Seleccionar la nueva categoría
        currentCategory = categoryId;
    }

    // Actualizar la interfaz
    renderCategories();
    updateParentCategorySelect();
    closeCategoryModal();
}

/**
 * Guardar datos de subcategoría
 */
function saveSubcategoryData() {
    const nameInput = document.getElementById('subcategoryName');
    const descInput = document.getElementById('subcategoryDescription');
    const iconInput = document.getElementById('selectedSubIcon');
    const parentSelect = document.getElementById('parentCategory');

    const subcategoryData = {
        name: nameInput.value.trim(),
        description: descInput.value.trim(),
        icon: iconInput.value,
        parentCategory: parentSelect.value
    };

    if (!subcategoryData.name) {
        showNotification('Por favor ingresa un nombre para la subcategoría', 'warning');
        return;
    }

    if (!subcategoryData.parentCategory || !categoriesData[subcategoryData.parentCategory]) {
        showNotification('Por favor selecciona una categoría padre válida', 'warning');
        return;
    }

    const parentCategory = categoriesData[subcategoryData.parentCategory];

    // Verificar si es edición o creación
    if (editingSubcategoryId) {
        // Editar subcategoría existente
        const subcategoryIndex = parentCategory.subcategories.findIndex(sub => sub.id === editingSubcategoryId);
        if (subcategoryIndex !== -1) {
            parentCategory.subcategories[subcategoryIndex].name = subcategoryData.name;
            parentCategory.subcategories[subcategoryIndex].description = subcategoryData.description;
            parentCategory.subcategories[subcategoryIndex].icon = subcategoryData.icon;

            showNotification('Subcategoría actualizada exitosamente', 'success');
        }
        editingSubcategoryId = null;
    } else {
        // Crear nueva subcategoría
        const subcategoryId = generateSubcategoryId(subcategoryData.name);

        // Verificar que no exista ya una subcategoría con ese ID en la categoría padre
        const existingSubcategory = parentCategory.subcategories.find(sub => sub.id === subcategoryId);
        if (existingSubcategory) {
            showNotification('Ya existe una subcategoría con ese nombre en esta categoría', 'warning');
            return;
        }

        const newSubcategory = {
            id: subcategoryId,
            name: subcategoryData.name,
            description: subcategoryData.description,
            icon: subcategoryData.icon,
            products: 0
        };

        parentCategory.subcategories.push(newSubcategory);
        showNotification('Subcategoría creada exitosamente', 'success');
    }

    // Actualizar la interfaz
    renderCategories();
    loadSubcategories(subcategoryData.parentCategory);
    closeSubcategoryModal();
}

/**
 * Editar categoría
 */
function editCategory(categoryId) {
    const category = categoriesData[categoryId];
    if (!category) return;

    editingCategoryId = categoryId;
    openCategoryModal(category);
}

/**
 * Eliminar categoría
 */
function deleteCategory(categoryId) {
    const category = categoriesData[categoryId];
    if (!category) return;

    const subcategoryCount = category.subcategories ? category.subcategories.length : 0;
    let confirmMessage = `¿Estás seguro de que deseas eliminar la categoría "${category.name}"?`;

    if (subcategoryCount > 0) {
        confirmMessage += `\n\nEsta acción también eliminará ${subcategoryCount} subcategoría(s).`;
    }

    if (confirm(confirmMessage)) {
        delete categoriesData[categoryId];

        // Si era la categoría actual, seleccionar otra
        if (currentCategory === categoryId) {
            const remainingCategories = Object.keys(categoriesData);
            currentCategory = remainingCategories.length > 0 ? remainingCategories[0] : null;
        }

        renderCategories();
        updateParentCategorySelect();
        showNotification('Categoría eliminada exitosamente', 'success');
    }
}

/**
 * Editar subcategoría
 */
function editSubcategory(subcategoryId) {
    // Buscar la subcategoría en la categoría actual
    const category = categoriesData[currentCategory];
    if (!category || !category.subcategories) return;

    const subcategory = category.subcategories.find(sub => sub.id === subcategoryId);
    if (!subcategory) return;

    editingSubcategoryId = subcategoryId;
    openSubcategoryModal(subcategory);
}

/**
 * Eliminar subcategoría
 */
function deleteSubcategory(subcategoryId) {
    const category = categoriesData[currentCategory];
    if (!category || !category.subcategories) return;

    const subcategoryIndex = category.subcategories.findIndex(sub => sub.id === subcategoryId);
    if (subcategoryIndex === -1) return;

    const subcategory = category.subcategories[subcategoryIndex];

    if (confirm(`¿Estás seguro de que deseas eliminar la subcategoría "${subcategory.name}"?`)) {
        category.subcategories.splice(subcategoryIndex, 1);

        renderCategories();
        loadSubcategories(currentCategory);
        showNotification('Subcategoría eliminada exitosamente', 'success');
    }
}

/**
 * Generar ID para categoría
 */
function generateCategoryId(name) {
    return name.toLowerCase()
        .replace(/[áàäâ]/g, 'a')
        .replace(/[éèëê]/g, 'e')
        .replace(/[íìïî]/g, 'i')
        .replace(/[óòöô]/g, 'o')
        .replace(/[úùüû]/g, 'u')
        .replace(/ñ/g, 'n')
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
}

/**
 * Generar ID para subcategoría
 */
function generateSubcategoryId(name) {
    return generateCategoryId(name);
}

/**
 * Actualizar select de categoría padre
 */
function updateParentCategorySelect() {
    const parentSelect = document.getElementById('parentCategory');
    if (!parentSelect) return;

    parentSelect.innerHTML = '';

    Object.values(categoriesData).forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        parentSelect.appendChild(option);
    });

    // Seleccionar la categoría actual por defecto
    if (currentCategory && categoriesData[currentCategory]) {
        parentSelect.value = currentCategory;
    }
}

// ==================== INICIALIZACIÓN ACTUALIZADA ====================

/**
 * Inicialización del dashboard
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard administrativo cargado');

    // Esperar un poco más para asegurar que todo esté renderizado
    setTimeout(() => {
        // Inicializar componentes
        initializeTabs();
        initializeKPIs();
        initializeProductManagement();
        initializeCategoryManagement();

        console.log('Todos los componentes inicializados correctamente');

        // Verificar que los botones existan
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');

        console.log('Verificación de botones:', {
            addCategoryBtn: !!addCategoryBtn,
            addSubcategoryBtn: !!addSubcategoryBtn
        });

        // Si los botones no existen, intentar configurar los modales de nuevo
        if (!addCategoryBtn || !addSubcategoryBtn) {
            console.warn('Botones no encontrados, reintentando configuración...');
            setTimeout(() => {
                setupCategoryModals();
                setupSubcategoryModals();
            }, 1000);
        }
    }, 100);
});

// Configuración adicional cuando la ventana esté completamente cargada
window.addEventListener('load', function() {
    console.log('Ventana completamente cargada, configurando modales...');

    // Esperar un poco más y reconfigurar
    setTimeout(() => {
        setupCategoryModals();
        setupSubcategoryModals();

        // Verificar nuevamente
        const addCategoryBtn = document.getElementById('addCategoryBtn');
        const addSubcategoryBtn = document.getElementById('addSubcategoryBtn');

        console.log('Verificación final de botones:', {
            addCategoryBtn: !!addCategoryBtn,
            addSubcategoryBtn: !!addSubcategoryBtn
        });

        // Si aún no funcionan, agregar event listeners directamente
        if (addCategoryBtn && !addCategoryBtn.hasAttribute('data-listener-added')) {
            addCategoryBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Event listener directo - Abriendo modal de categoría');
                openCategoryModal();
            });
            addCategoryBtn.setAttribute('data-listener-added', 'true');
            console.log('Event listener directo agregado al botón de categoría');
        }

        if (addSubcategoryBtn && !addSubcategoryBtn.hasAttribute('data-listener-added')) {
            addSubcategoryBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Event listener directo - Abriendo modal de subcategoría');
                openSubcategoryModal();
            });
            addSubcategoryBtn.setAttribute('data-listener-added', 'true');
            console.log('Event listener directo agregado al botón de subcategoría');
        }
    }, 500);
});

// ==================== FUNCIONES GLOBALES ====================
// Hacer funciones disponibles globalmente para los onclick en HTML
window.selectCategory = selectCategory;
window.editCategory = editCategory;
window.deleteCategory = deleteCategory;
window.editSubcategory = editSubcategory;
window.deleteSubcategory = deleteSubcategory;
window.openCategoryModal = openCategoryModal;
window.openSubcategoryModal = openSubcategoryModal;
window.setupCategoryButtons = setupCategoryButtons;

// Funciones alternativas simples para los modales
window.showCategoryModal = function() {
    console.log('showCategoryModal llamada');
    const modal = document.getElementById('categoryModal');
    if (modal) {
        modal.style.display = 'flex';
        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
        modal.classList.add('active');
        console.log('Modal de categoría mostrado');
        showNotification('Modal de categoría abierto', 'info');
    } else {
        console.error('Modal categoryModal no encontrado');
        showNotification('Error: Modal no encontrado', 'error');
    }
};

window.showSubcategoryModal = function() {
    console.log('showSubcategoryModal llamada');
    const modal = document.getElementById('subcategoryModal');
    if (modal) {
        modal.style.display = 'flex';
        modal.style.opacity = '1';
        modal.style.visibility = 'visible';
        modal.classList.add('active');
        console.log('Modal de subcategoría mostrado');
        showNotification('Modal de subcategoría abierto', 'info');
    } else {
        console.error('Modal subcategoryModal no encontrado');
        showNotification('Error: Modal no encontrado', 'error');
    }
};

// Función de prueba para verificar que todo funciona
window.testModals = function() {
    console.log('Probando modales...');
    console.log('openCategoryModal disponible:', typeof window.openCategoryModal);
    console.log('openSubcategoryModal disponible:', typeof window.openSubcategoryModal);

    // Probar modal de categoría
    try {
        window.openCategoryModal();
        console.log('Modal de categoría abierto exitosamente');
    } catch (error) {
        console.error('Error al abrir modal de categoría:', error);
    }
};

// ==================== EXPORT PARA DEBUGGING ====================
window.AdminApp = {
    switchSection,
    updateKPIs,
    showNotification,
    formatCurrency,
    formatNumber,
    currentSection,
    currentTab,
    currentPeriod,
    selectCategory,
    loadSubcategories,
    initializeCategoryManagement,
    categoriesData,
    currentCategory,
    renderCategories
};
