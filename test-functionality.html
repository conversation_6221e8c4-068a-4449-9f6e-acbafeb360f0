<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Panel de Administración</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #6a1b9a;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #4a148c;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test de Funcionalidades - Panel de Administración</h1>
        <p>Esta página verifica que todas las funciones JavaScript estén cargadas correctamente.</p>

        <div class="test-section">
            <h3>📋 Test de Funciones Principales</h3>
            <button class="test-button" onclick="testMainFunctions()">Probar Funciones Principales</button>
            <div id="main-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Test de Utilidades</h3>
            <button class="test-button" onclick="testUtilities()">Probar Utilidades</button>
            <div id="utils-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📦 Test de Productos</h3>
            <button class="test-button" onclick="testProductFunctions()">Probar Funciones de Productos</button>
            <div id="products-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test de KPIs</h3>
            <button class="test-button" onclick="testKPIFunctions()">Probar Funciones de KPIs</button>
            <div id="kpis-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🏷️ Test de Categorías</h3>
            <button class="test-button" onclick="testCategoryFunctions()">Probar Funciones de Categorías</button>
            <div id="categories-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🎨 Test de Notificaciones</h3>
            <button class="test-button" onclick="testNotifications()">Probar Notificaciones</button>
            <div id="notifications-result" class="test-result"></div>
        </div>
    </div>

    <!-- Cargar todos los scripts del panel de administración -->
    <script src="admin-main.js"></script>
    <script src="admin-utils.js"></script>
    <script src="admin-system.js"></script>
    <script src="admin-categories.js"></script>
    <script src="admin-products.js"></script>
    <script src="admin-kpis.js"></script>
    <script src="admin-reports.js"></script>
    <script src="admin-animations.js"></script>

    <script>
        // Funciones de test
        function testMainFunctions() {
            const result = document.getElementById('main-result');
            const tests = [];

            // Test de funciones principales
            tests.push(testFunction('loadCategories', loadCategories));
            tests.push(testFunction('updateStats', updateStats));
            tests.push(testFunction('switchTab', switchTab));
            tests.push(testFunction('editProduct', editProduct));
            tests.push(testFunction('deleteProduct', deleteProduct));
            tests.push(testFunction('toggleProductStatus', toggleProductStatus));

            displayResults(result, tests);
        }

        function testUtilities() {
            const result = document.getElementById('utils-result');
            const tests = [];

            tests.push(testFunction('showNotification', showNotification));
            tests.push(testFunction('formatPrice', formatPrice));
            tests.push(testFunction('debounce', debounce));
            tests.push(testFunction('validateEmail', validateEmail));
            tests.push(testFunction('generateUniqueId', generateUniqueId));

            displayResults(result, tests);
        }

        function testProductFunctions() {
            const result = document.getElementById('products-result');
            const tests = [];

            tests.push(testFunction('openProductModal', openProductModal));
            tests.push(testFunction('updateProductCounters', updateProductCounters));
            tests.push(testFunction('performAdvancedSearch', performAdvancedSearch));
            tests.push(testFunction('sortProducts', sortProducts));

            displayResults(result, tests);
        }

        function testKPIFunctions() {
            const result = document.getElementById('kpis-result');
            const tests = [];

            tests.push(testFunction('updateChartsAndData', updateChartsAndData));
            tests.push(testFunction('animateKPICards', animateKPICards));
            tests.push(testFunction('formatNumber', formatNumber));

            displayResults(result, tests);
        }

        function testCategoryFunctions() {
            const result = document.getElementById('categories-result');
            const tests = [];

            tests.push(testFunction('openUltraModal', openUltraModal));
            tests.push(testFunction('closeUltraModal', closeUltraModal));
            tests.push(testFunction('addSubcategory', addSubcategory));
            tests.push(testFunction('saveCategory', saveCategory));

            displayResults(result, tests);
        }

        function testNotifications() {
            const result = document.getElementById('notifications-result');
            
            if (typeof showNotification === 'function') {
                showNotification('✅ Test de notificación exitoso!', 'success');
                showNotification('⚠️ Test de advertencia', 'warning');
                showNotification('ℹ️ Test de información', 'info');
                showNotification('❌ Test de error', 'error');
                
                result.innerHTML = '<div class="success">✅ Notificaciones funcionando correctamente</div>';
            } else {
                result.innerHTML = '<div class="error">❌ Función showNotification no disponible</div>';
            }
        }

        function testFunction(name, func) {
            try {
                if (typeof func === 'function') {
                    return { name, status: 'success', message: 'Función disponible' };
                } else {
                    return { name, status: 'error', message: 'Función no definida' };
                }
            } catch (error) {
                return { name, status: 'error', message: error.message };
            }
        }

        function displayResults(container, tests) {
            const successCount = tests.filter(t => t.status === 'success').length;
            const totalCount = tests.length;
            
            let html = `<div class="${successCount === totalCount ? 'success' : 'error'}">`;
            html += `<strong>Resultado: ${successCount}/${totalCount} funciones disponibles</strong><br><br>`;
            
            tests.forEach(test => {
                const icon = test.status === 'success' ? '✅' : '❌';
                html += `${icon} ${test.name}: ${test.message}<br>`;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        // Test automático al cargar
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Página de test cargada');
            console.log('📋 Funciones disponibles en window.adminSystem:', window.adminSystem);
            console.log('🔧 Utilidades disponibles en window.adminUtils:', window.adminUtils);
        });
    </script>
</body>
</html>
