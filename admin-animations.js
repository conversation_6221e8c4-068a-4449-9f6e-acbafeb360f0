/**
 * ADMIN PANEL - ANIMACIONES Y EFECTOS VISUALES
 * Sistema de animaciones, transiciones y efectos de scroll
 */

// ==================== ANIMACIONES DE SCROLL ====================

/**
 * Inicializar animaciones de scroll
 */
function initializeScrollAnimations() {
    // Función para verificar si un elemento está en el viewport
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8
        );
    }

    // Función para manejar la animación de elementos al hacer scroll
    function handleScrollAnimations() {
        const containers = document.querySelectorAll('.container-charts, .container-products, .container-categories, .container-store-info');

        containers.forEach(container => {
            if (isElementInViewport(container) && !container.classList.contains('visible')) {
                container.classList.add('visible');
            }
        });
    }

    // Ejecutar al cargar la página
    handleScrollAnimations();

    // Ejecutar al hacer scroll con throttle para mejor rendimiento
    window.addEventListener('scroll', throttle(handleScrollAnimations, 100));

    console.log('🎬 Animaciones de scroll inicializadas');
}

// ==================== ANIMACIONES DE ENTRADA ====================

/**
 * Animar entrada de elementos
 */
function animateElementsOnLoad() {
    // Animar tarjetas de KPI
    const kpiCards = document.querySelectorAll('.admin-kpi-card');
    kpiCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // Animar tarjetas de estadísticas
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 * index);
    });

    // Animar elementos del menú
    const menuItems = document.querySelectorAll('.admin-menu-item');
    menuItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 50 * index);
    });
}

// ==================== ANIMACIONES DE HOVER ====================

/**
 * Configurar efectos de hover avanzados
 */
function setupHoverEffects() {
    // Efecto de hover para tarjetas de productos
    const productCards = document.querySelectorAll('.product-card:not(.add-product-card)');
    productCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px) scale(1.02)';
            card.style.boxShadow = '0 10px 30px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        });
    });

    // Efecto de hover para tarjetas de categorías
    const categoryCards = document.querySelectorAll('.category-card:not(.add-category-card)');
    categoryCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const icon = card.querySelector('.category-icon-container');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
            
            const subcategoryTags = card.querySelectorAll('.subcategory-tag');
            subcategoryTags.forEach((tag, index) => {
                setTimeout(() => {
                    tag.style.transform = 'translateY(-2px)';
                }, index * 50);
            });
        });

        card.addEventListener('mouseleave', () => {
            const icon = card.querySelector('.category-icon-container');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
            
            const subcategoryTags = card.querySelectorAll('.subcategory-tag');
            subcategoryTags.forEach(tag => {
                tag.style.transform = 'translateY(0)';
            });
        });
    });

    // Efecto de hover para botones
    const buttons = document.querySelectorAll('.admin-btn, .neo-btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
        });

        button.addEventListener('mousedown', () => {
            button.style.transform = 'translateY(0) scale(0.98)';
        });

        button.addEventListener('mouseup', () => {
            button.style.transform = 'translateY(-2px) scale(1)';
        });
    });
}

// ==================== ANIMACIONES DE MODAL ====================

/**
 * Animar apertura de modal
 */
function animateModalOpen(modal) {
    modal.style.opacity = '0';
    modal.style.visibility = 'visible';
    
    const modalContent = modal.querySelector('.admin-modal, .ultra-modal');
    if (modalContent) {
        modalContent.style.transform = 'translateY(40px) scale(0.9)';
    }

    // Animar overlay
    setTimeout(() => {
        modal.style.transition = 'opacity 0.4s ease';
        modal.style.opacity = '1';
    }, 10);

    // Animar contenido
    setTimeout(() => {
        if (modalContent) {
            modalContent.style.transition = 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            modalContent.style.transform = 'translateY(0) scale(1)';
        }
    }, 50);
}

/**
 * Animar cierre de modal
 */
function animateModalClose(modal) {
    const modalContent = modal.querySelector('.admin-modal, .ultra-modal');
    
    // Animar contenido
    if (modalContent) {
        modalContent.style.transform = 'translateY(40px) scale(0.9)';
    }

    // Animar overlay
    setTimeout(() => {
        modal.style.opacity = '0';
    }, 100);

    // Ocultar modal
    setTimeout(() => {
        modal.style.visibility = 'hidden';
        modal.classList.remove('active');
    }, 400);
}

// ==================== ANIMACIONES DE NOTIFICACIONES ====================

/**
 * Animar entrada de notificación
 */
function animateNotificationIn(notification) {
    notification.style.transform = 'translateX(400px)';
    notification.style.opacity = '0';

    setTimeout(() => {
        notification.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 100);
}

/**
 * Animar salida de notificación
 */
function animateNotificationOut(notification) {
    notification.style.transform = 'translateX(400px)';
    notification.style.opacity = '0';

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 400);
}

// ==================== ANIMACIONES DE CARGA ====================

/**
 * Crear animación de carga
 */
function createLoadingAnimation(container) {
    const loader = document.createElement('div');
    loader.className = 'loading-animation';
    loader.innerHTML = `
        <div class="loading-spinner"></div>
        <p>Cargando...</p>
    `;

    loader.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255,255,255,0.9);
        z-index: 1000;
    `;

    container.style.position = 'relative';
    container.appendChild(loader);

    return loader;
}

/**
 * Remover animación de carga
 */
function removeLoadingAnimation(loader) {
    if (loader && loader.parentNode) {
        fadeOut(loader, 300);
        setTimeout(() => {
            if (loader.parentNode) {
                loader.remove();
            }
        }, 300);
    }
}

// ==================== ANIMACIONES DE TRANSICIÓN ====================

/**
 * Animar cambio de pestaña
 */
function animateTabChange(oldContent, newContent) {
    // Ocultar contenido anterior
    oldContent.style.opacity = '0';
    oldContent.style.transform = 'translateX(-20px)';

    setTimeout(() => {
        oldContent.classList.remove('active');
        newContent.classList.add('active');

        // Mostrar nuevo contenido
        newContent.style.opacity = '0';
        newContent.style.transform = 'translateX(20px)';

        setTimeout(() => {
            newContent.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            newContent.style.opacity = '1';
            newContent.style.transform = 'translateX(0)';
        }, 50);
    }, 150);
}

/**
 * Animar cambio de paso en modal
 */
function animateStepChange(oldStep, newStep, direction = 'forward') {
    const translateValue = direction === 'forward' ? '20px' : '-20px';

    // Ocultar paso anterior
    oldStep.style.opacity = '0';
    oldStep.style.transform = `translateX(${direction === 'forward' ? '-20px' : '20px'})`;

    setTimeout(() => {
        oldStep.classList.remove('active');
        newStep.classList.add('active');

        // Mostrar nuevo paso
        newStep.style.opacity = '0';
        newStep.style.transform = `translateX(${translateValue})`;

        setTimeout(() => {
            newStep.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            newStep.style.opacity = '1';
            newStep.style.transform = 'translateX(0)';
        }, 50);
    }, 200);
}

// ==================== EFECTOS ESPECIALES ====================

/**
 * Efecto de partículas para celebración
 */
function createParticleEffect(element) {
    const particles = [];
    const colors = ['#667eea', '#764ba2', '#f093fb', '#4caf50', '#ff9800'];

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: 6px;
            height: 6px;
            background: ${colors[Math.floor(Math.random() * colors.length)]};
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
        `;

        const rect = element.getBoundingClientRect();
        particle.style.left = (rect.left + rect.width / 2) + 'px';
        particle.style.top = (rect.top + rect.height / 2) + 'px';

        document.body.appendChild(particle);
        particles.push(particle);

        // Animar partícula
        const angle = (Math.PI * 2 * i) / 20;
        const velocity = 100 + Math.random() * 100;
        const vx = Math.cos(angle) * velocity;
        const vy = Math.sin(angle) * velocity;

        let x = 0;
        let y = 0;
        let opacity = 1;

        function animateParticle() {
            x += vx * 0.02;
            y += vy * 0.02 + 2; // Gravedad
            opacity -= 0.02;

            particle.style.transform = `translate(${x}px, ${y}px)`;
            particle.style.opacity = opacity;

            if (opacity > 0) {
                requestAnimationFrame(animateParticle);
            } else {
                particle.remove();
            }
        }

        requestAnimationFrame(animateParticle);
    }
}

/**
 * Efecto de ondas (ripple)
 */
function createRippleEffect(element, event) {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255,255,255,0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
    `;

    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// ==================== ESTILOS DE ANIMACIÓN ====================

/**
 * Agregar estilos de animación dinámicamente
 */
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--purple-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 12px;
        }

        .category-card {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.5s ease forwards;
        }

        .subcategory-tag {
            animation: fadeIn 0.3s ease;
        }

        /* Delays para animaciones escalonadas */
        .category-card:nth-child(1) { animation-delay: 0.1s; }
        .category-card:nth-child(2) { animation-delay: 0.2s; }
        .category-card:nth-child(3) { animation-delay: 0.3s; }
        .category-card:nth-child(4) { animation-delay: 0.4s; }
        .category-card:nth-child(5) { animation-delay: 0.5s; }
    `;
    document.head.appendChild(style);
}

// ==================== INICIALIZACIÓN ====================

/**
 * Inicializar sistema de animaciones
 */
function initializeAnimationSystem() {
    console.log('🎬 Inicializando sistema de animaciones...');

    // Agregar estilos de animación
    addAnimationStyles();

    // Configurar animaciones de scroll
    initializeScrollAnimations();

    // Configurar efectos de hover
    setupHoverEffects();

    // Animar elementos al cargar
    setTimeout(() => {
        animateElementsOnLoad();
    }, 500);

    console.log('✅ Sistema de animaciones inicializado');
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', initializeAnimationSystem);

console.log('🎬 Admin Animations Script Loaded Successfully');
