/**
 * ADMIN PANEL - UTILIDADES Y FUNCIONES AUXILIARES
 * Funciones de soporte, notificaciones, validaciones y utilidades generales
 */

// ==================== SISTEMA DE NOTIFICACIONES ====================

/**
 * Mostrar notificación
 */
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `modern-notification notification-${type}`;
    
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="notification-content">
            <h4>${getNotificationTitle(type)}</h4>
            <p>${message}</p>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Mostrar notificación
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Event listener para cerrar
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // Auto-ocultar
    if (duration > 0) {
        setTimeout(() => {
            hideNotification(notification);
        }, duration);
    }
    
    return notification;
}

/**
 * Ocultar notificación
 */
function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 400);
}

/**
 * Obtener título de notificación
 */
function getNotificationTitle(type) {
    const titles = {
        success: 'Éxito',
        error: 'Error',
        warning: 'Advertencia',
        info: 'Información'
    };
    
    return titles[type] || titles.info;
}

// ==================== FUNCIONES DE VALIDACIÓN ====================

/**
 * Validar email
 */
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Validar URL
 */
function validateURL(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validar número de teléfono
 */
function validatePhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * Validar longitud de texto
 */
function validateLength(text, min = 0, max = Infinity) {
    const length = text.trim().length;
    return length >= min && length <= max;
}

/**
 * Validar formato de precio
 */
function validatePrice(price) {
    const priceRegex = /^\d+(\.\d{1,2})?$/;
    return priceRegex.test(price) && parseFloat(price) >= 0;
}

// ==================== FUNCIONES DE FORMATO ====================

/**
 * Formatear precio
 */
function formatPrice(price, currency = '$') {
    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) return `${currency}0`;
    
    return `${currency}${numPrice.toLocaleString('es-CL', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    })}`;
}

/**
 * Formatear fecha
 */
function formatDate(date, format = 'short') {
    const dateObj = new Date(date);
    
    const formats = {
        short: { day: '2-digit', month: '2-digit', year: 'numeric' },
        long: { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        },
        time: { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        },
        datetime: { 
            day: '2-digit', 
            month: '2-digit', 
            year: 'numeric',
            hour: '2-digit', 
            minute: '2-digit' 
        }
    };
    
    return dateObj.toLocaleDateString('es-CL', formats[format] || formats.short);
}

/**
 * Formatear texto a título
 */
function toTitleCase(str) {
    return str.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}

/**
 * Truncar texto
 */
function truncateText(text, maxLength = 100, suffix = '...') {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
}

// ==================== FUNCIONES DE UTILIDAD ====================

/**
 * Debounce function
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Generar ID único
 */
function generateUniqueId(prefix = 'id') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Copiar al portapapeles
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showNotification('Copiado al portapapeles', 'success');
        return true;
    } catch (err) {
        console.error('Error al copiar:', err);
        showNotification('Error al copiar al portapapeles', 'error');
        return false;
    }
}

/**
 * Descargar archivo
 */
function downloadFile(content, filename, contentType = 'text/plain') {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

/**
 * Leer archivo como texto
 */
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsText(file);
    });
}

// ==================== FUNCIONES DE ANIMACIÓN ====================

/**
 * Animar elemento con fade in
 */
function fadeIn(element, duration = 300) {
    element.style.opacity = '0';
    element.style.display = 'block';
    
    let start = null;
    function animate(timestamp) {
        if (!start) start = timestamp;
        const progress = timestamp - start;
        const opacity = Math.min(progress / duration, 1);
        
        element.style.opacity = opacity;
        
        if (progress < duration) {
            requestAnimationFrame(animate);
        }
    }
    
    requestAnimationFrame(animate);
}

/**
 * Animar elemento con fade out
 */
function fadeOut(element, duration = 300) {
    let start = null;
    const initialOpacity = parseFloat(getComputedStyle(element).opacity);
    
    function animate(timestamp) {
        if (!start) start = timestamp;
        const progress = timestamp - start;
        const opacity = initialOpacity * (1 - Math.min(progress / duration, 1));
        
        element.style.opacity = opacity;
        
        if (progress < duration) {
            requestAnimationFrame(animate);
        } else {
            element.style.display = 'none';
        }
    }
    
    requestAnimationFrame(animate);
}

/**
 * Animar elemento con slide up
 */
function slideUp(element, duration = 300) {
    const height = element.offsetHeight;
    element.style.overflow = 'hidden';
    element.style.transition = `height ${duration}ms ease`;
    element.style.height = height + 'px';
    
    setTimeout(() => {
        element.style.height = '0px';
    }, 10);
    
    setTimeout(() => {
        element.style.display = 'none';
        element.style.height = '';
        element.style.overflow = '';
        element.style.transition = '';
    }, duration);
}

/**
 * Animar elemento con slide down
 */
function slideDown(element, duration = 300) {
    element.style.display = 'block';
    const height = element.scrollHeight;
    element.style.overflow = 'hidden';
    element.style.height = '0px';
    element.style.transition = `height ${duration}ms ease`;
    
    setTimeout(() => {
        element.style.height = height + 'px';
    }, 10);
    
    setTimeout(() => {
        element.style.height = '';
        element.style.overflow = '';
        element.style.transition = '';
    }, duration);
}

// ==================== FUNCIONES DE ALMACENAMIENTO ====================

/**
 * Guardar en localStorage con manejo de errores
 */
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('Error al guardar en localStorage:', error);
        showNotification('Error al guardar datos localmente', 'error');
        return false;
    }
}

/**
 * Cargar desde localStorage con manejo de errores
 */
function loadFromStorage(key, defaultValue = null) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
        console.error('Error al cargar desde localStorage:', error);
        return defaultValue;
    }
}

/**
 * Eliminar de localStorage
 */
function removeFromStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error al eliminar de localStorage:', error);
        return false;
    }
}

/**
 * Limpiar localStorage
 */
function clearStorage() {
    try {
        localStorage.clear();
        showNotification('Datos locales eliminados', 'success');
        return true;
    } catch (error) {
        console.error('Error al limpiar localStorage:', error);
        showNotification('Error al limpiar datos locales', 'error');
        return false;
    }
}

// ==================== FUNCIONES DE DETECCIÓN ====================

/**
 * Detectar si es dispositivo móvil
 */
function isMobile() {
    return window.innerWidth <= 768;
}

/**
 * Detectar si es tablet
 */
function isTablet() {
    return window.innerWidth > 768 && window.innerWidth <= 1024;
}

/**
 * Detectar si es desktop
 */
function isDesktop() {
    return window.innerWidth > 1024;
}

/**
 * Detectar soporte para características
 */
function hasSupport(feature) {
    const features = {
        localStorage: typeof Storage !== 'undefined',
        clipboard: navigator.clipboard !== undefined,
        geolocation: navigator.geolocation !== undefined,
        notifications: 'Notification' in window,
        serviceWorker: 'serviceWorker' in navigator
    };
    
    return features[feature] || false;
}

// ==================== EXPORT DE UTILIDADES ====================

// Hacer utilidades disponibles globalmente
window.adminUtils = {
    showNotification,
    hideNotification,
    validateEmail,
    validateURL,
    validatePhone,
    validateLength,
    validatePrice,
    formatPrice,
    formatDate,
    toTitleCase,
    truncateText,
    debounce,
    throttle,
    generateUniqueId,
    copyToClipboard,
    downloadFile,
    readFileAsText,
    fadeIn,
    fadeOut,
    slideUp,
    slideDown,
    saveToStorage,
    loadFromStorage,
    removeFromStorage,
    clearStorage,
    isMobile,
    isTablet,
    isDesktop,
    hasSupport
};

console.log('🔧 Admin Utils Script Loaded Successfully');
