/**
 * ADMIN PANEL - SISTEMA DE REPORTES
 * Generación y visualización de reportes de productos y analytics
 */

// ==================== GENERACIÓN DE REPORTES ====================

/**
 * Generar reporte de productos
 */
function generateProductReport() {
    const products = document.querySelectorAll('.product-card:not(.add-product-card)');
    const report = {
        totalProducts: products.length,
        activeProducts: 0,
        inactiveProducts: 0,
        categoriesCount: {},
        priceRanges: {
            low: 0,    // < 50000
            medium: 0, // 50000 - 200000
            high: 0    // > 200000
        },
        generatedAt: new Date().toISOString()
    };

    products.forEach(product => {
        const isActive = product.querySelector('.product-toggle input').checked;
        const category = product.querySelector('.product-category').textContent.split(' / ')[0];
        const priceText = product.querySelector('.current-price').textContent.replace(/[^\d]/g, '');
        const price = parseFloat(priceText) || 0;

        if (isActive) {
            report.activeProducts++;
        } else {
            report.inactiveProducts++;
        }

        report.categoriesCount[category] = (report.categoriesCount[category] || 0) + 1;

        if (price < 50000) {
            report.priceRanges.low++;
        } else if (price <= 200000) {
            report.priceRanges.medium++;
        } else {
            report.priceRanges.high++;
        }
    });

    return report;
}

/**
 * Mostrar reporte
 */
function displayReport(report) {
    const reportModal = document.createElement('div');
    reportModal.className = 'admin-modal-overlay active';
    reportModal.innerHTML = `
        <div class="admin-modal">
            <div class="admin-modal-header">
                <h2 class="admin-modal-title">
                    <i class="fas fa-chart-bar"></i> Reporte de Productos
                </h2>
                <button class="admin-modal-close">&times;</button>
            </div>
            <div class="admin-modal-body">
                <div class="report-section">
                    <h3>Resumen General</h3>
                    <div class="report-stats">
                        <div class="report-stat">
                            <span class="stat-label">Total de productos:</span>
                            <span class="stat-value">${report.totalProducts}</span>
                        </div>
                        <div class="report-stat">
                            <span class="stat-label">Productos activos:</span>
                            <span class="stat-value success">${report.activeProducts}</span>
                        </div>
                        <div class="report-stat">
                            <span class="stat-label">Productos inactivos:</span>
                            <span class="stat-value warning">${report.inactiveProducts}</span>
                        </div>
                        <div class="report-stat">
                            <span class="stat-label">Porcentaje activo:</span>
                            <span class="stat-value">${((report.activeProducts / report.totalProducts) * 100).toFixed(1)}%</span>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>Distribución por Categorías</h3>
                    <div class="report-categories">
                        ${Object.entries(report.categoriesCount).map(([category, count]) => `
                            <div class="category-stat">
                                <span class="category-name">${category}</span>
                                <div class="category-bar">
                                    <div class="category-fill" style="width: ${(count / report.totalProducts) * 100}%"></div>
                                    <span class="category-count">${count} productos</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="report-section">
                    <h3>Rangos de Precio</h3>
                    <div class="report-prices">
                        <div class="price-stat">
                            <span class="price-label">Bajo (< $50.000)</span>
                            <div class="price-bar">
                                <div class="price-fill low" style="width: ${(report.priceRanges.low / report.totalProducts) * 100}%"></div>
                                <span class="price-count">${report.priceRanges.low} productos</span>
                            </div>
                        </div>
                        <div class="price-stat">
                            <span class="price-label">Medio ($50.000 - $200.000)</span>
                            <div class="price-bar">
                                <div class="price-fill medium" style="width: ${(report.priceRanges.medium / report.totalProducts) * 100}%"></div>
                                <span class="price-count">${report.priceRanges.medium} productos</span>
                            </div>
                        </div>
                        <div class="price-stat">
                            <span class="price-label">Alto (> $200.000)</span>
                            <div class="price-bar">
                                <div class="price-fill high" style="width: ${(report.priceRanges.high / report.totalProducts) * 100}%"></div>
                                <span class="price-count">${report.priceRanges.high} productos</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>Información Adicional</h3>
                    <div class="report-info">
                        <p><strong>Fecha de generación:</strong> ${formatDate(report.generatedAt, 'datetime')}</p>
                        <p><strong>Categorías totales:</strong> ${Object.keys(report.categoriesCount).length}</p>
                        <p><strong>Precio promedio:</strong> ${calculateAveragePrice()}</p>
                    </div>
                </div>
            </div>
            <div class="admin-modal-footer">
                <button id="downloadReportBtn" class="admin-btn admin-btn-primary">
                    <i class="fas fa-download"></i> Descargar Reporte
                </button>
                <button id="printReportBtn" class="admin-btn admin-btn-secondary">
                    <i class="fas fa-print"></i> Imprimir
                </button>
                <button class="admin-btn admin-btn-secondary" onclick="this.closest('.admin-modal-overlay').remove()">
                    Cerrar
                </button>
            </div>
        </div>
    `;

    // Agregar estilos para el reporte
    addReportStyles();

    document.body.appendChild(reportModal);

    // Event listeners
    reportModal.querySelector('.admin-modal-close').addEventListener('click', () => {
        reportModal.remove();
    });

    document.getElementById('downloadReportBtn').addEventListener('click', () => {
        downloadReportAsFile(report);
    });

    document.getElementById('printReportBtn').addEventListener('click', () => {
        printReport(reportModal);
    });

    // Animar entrada del modal
    setTimeout(() => {
        reportModal.classList.add('show');
    }, 100);
}

/**
 * Agregar estilos para reportes
 */
function addReportStyles() {
    if (document.getElementById('report-styles')) return;

    const reportStyles = document.createElement('style');
    reportStyles.id = 'report-styles';
    reportStyles.textContent = `
        .report-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        .report-section:last-child {
            border-bottom: none;
        }
        .report-section h3 {
            color: var(--purple-primary);
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .report-section h3::before {
            content: '';
            width: 4px;
            height: 20px;
            background: var(--purple-primary);
            border-radius: 2px;
        }
        .report-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        .report-stat:last-child {
            border-bottom: none;
        }
        .stat-label {
            font-weight: 500;
            color: var(--text-primary);
        }
        .stat-value {
            font-weight: 600;
            color: var(--purple-primary);
            font-size: 16px;
        }
        .stat-value.success {
            color: var(--success-color);
        }
        .stat-value.warning {
            color: var(--warning-color);
        }
        .category-stat, .price-stat {
            margin-bottom: 12px;
        }
        .category-name, .price-label {
            font-weight: 500;
            margin-bottom: 5px;
            display: block;
        }
        .category-bar, .price-bar {
            position: relative;
            background: #f0f0f0;
            border-radius: 10px;
            height: 25px;
            overflow: hidden;
            display: flex;
            align-items: center;
        }
        .category-fill, .price-fill {
            height: 100%;
            background: var(--purple-primary);
            border-radius: 10px;
            transition: width 0.8s ease;
        }
        .price-fill.low {
            background: var(--success-color);
        }
        .price-fill.medium {
            background: var(--warning-color);
        }
        .price-fill.high {
            background: var(--error-color);
        }
        .category-count, .price-count {
            position: absolute;
            right: 10px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .report-info p {
            margin: 8px 0;
            padding: 8px 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
            border-left: 4px solid var(--purple-primary);
        }
        @media print {
            .admin-modal-footer {
                display: none !important;
            }
            .admin-modal {
                box-shadow: none !important;
                max-width: none !important;
                margin: 0 !important;
            }
        }
    `;
    document.head.appendChild(reportStyles);
}

/**
 * Calcular precio promedio
 */
function calculateAveragePrice() {
    const products = document.querySelectorAll('.product-card:not(.add-product-card)');
    let totalPrice = 0;
    let count = 0;

    products.forEach(product => {
        const priceText = product.querySelector('.current-price').textContent.replace(/[^\d]/g, '');
        const price = parseFloat(priceText) || 0;
        if (price > 0) {
            totalPrice += price;
            count++;
        }
    });

    const average = count > 0 ? totalPrice / count : 0;
    return formatPrice(average);
}

/**
 * Descargar reporte como archivo
 */
function downloadReportAsFile(report) {
    const reportText = generateReportText(report);
    const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    link.href = url;
    link.download = `reporte-productos-${formatDate(new Date(), 'short').replace(/\//g, '-')}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showNotification('Reporte descargado correctamente', 'success');
}

/**
 * Generar texto del reporte
 */
function generateReportText(report) {
    return `
REPORTE DE PRODUCTOS
====================

Resumen General:
- Total de productos: ${report.totalProducts}
- Productos activos: ${report.activeProducts}
- Productos inactivos: ${report.inactiveProducts}
- Porcentaje activo: ${((report.activeProducts / report.totalProducts) * 100).toFixed(1)}%

Distribución por Categorías:
${Object.entries(report.categoriesCount).map(([cat, count]) => 
    `- ${cat}: ${count} productos (${((count / report.totalProducts) * 100).toFixed(1)}%)`
).join('\n')}

Rangos de Precio:
- Bajo (< $50.000): ${report.priceRanges.low} productos (${((report.priceRanges.low / report.totalProducts) * 100).toFixed(1)}%)
- Medio ($50.000 - $200.000): ${report.priceRanges.medium} productos (${((report.priceRanges.medium / report.totalProducts) * 100).toFixed(1)}%)
- Alto (> $200.000): ${report.priceRanges.high} productos (${((report.priceRanges.high / report.totalProducts) * 100).toFixed(1)}%)

Información Adicional:
- Fecha de generación: ${formatDate(report.generatedAt, 'datetime')}
- Categorías totales: ${Object.keys(report.categoriesCount).length}
- Precio promedio: ${calculateAveragePrice()}

---
Generado por Panel de Administración
${new Date().toLocaleString()}
    `.trim();
}

/**
 * Imprimir reporte
 */
function printReport(reportModal) {
    const printWindow = window.open('', '_blank');
    const reportContent = reportModal.querySelector('.admin-modal-body').innerHTML;
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Reporte de Productos</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    color: #333;
                }
                h2 {
                    color: #667eea;
                    border-bottom: 2px solid #667eea;
                    padding-bottom: 10px;
                }
                h3 {
                    color: #667eea;
                    margin-top: 25px;
                }
                .report-section {
                    margin-bottom: 25px;
                    page-break-inside: avoid;
                }
                .report-stat {
                    display: flex;
                    justify-content: space-between;
                    padding: 5px 0;
                    border-bottom: 1px solid #eee;
                }
                .stat-value {
                    font-weight: bold;
                    color: #667eea;
                }
                .category-stat, .price-stat {
                    margin: 10px 0;
                }
                .category-name, .price-label {
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .report-info p {
                    margin: 8px 0;
                    padding: 8px;
                    background: #f5f5f5;
                    border-left: 4px solid #667eea;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <h2>Reporte de Productos</h2>
            ${reportContent}
            <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
                Generado el ${new Date().toLocaleString()}
            </div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);

    showNotification('Reporte enviado a impresión', 'success');
}

// ==================== REPORTE DE ANALYTICS ====================

/**
 * Generar reporte de analytics
 */
function generateAnalyticsReport() {
    const report = {
        summary: {
            totalPageViews: analytics.pageViews,
            totalProductViews: Object.values(analytics.productViews).reduce((a, b) => a + b, 0),
            totalCategoryViews: Object.values(analytics.categoryViews).reduce((a, b) => a + b, 0),
            totalSearches: analytics.searchQueries.length
        },
        topProducts: Object.entries(analytics.productViews)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5),
        topCategories: Object.entries(analytics.categoryViews)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5),
        recentSearches: analytics.searchQueries.slice(-10),
        generatedAt: new Date().toISOString()
    };

    return report;
}

/**
 * Mostrar reporte de analytics
 */
function displayAnalyticsReport() {
    const report = generateAnalyticsReport();
    
    const analyticsModal = document.createElement('div');
    analyticsModal.className = 'admin-modal-overlay active';
    analyticsModal.innerHTML = `
        <div class="admin-modal">
            <div class="admin-modal-header">
                <h2 class="admin-modal-title">
                    <i class="fas fa-chart-line"></i> Reporte de Analytics
                </h2>
                <button class="admin-modal-close">&times;</button>
            </div>
            <div class="admin-modal-body">
                <div class="report-section">
                    <h3>Resumen de Actividad</h3>
                    <div class="report-stats">
                        <div class="report-stat">
                            <span class="stat-label">Vistas de página:</span>
                            <span class="stat-value">${report.summary.totalPageViews}</span>
                        </div>
                        <div class="report-stat">
                            <span class="stat-label">Vistas de productos:</span>
                            <span class="stat-value">${report.summary.totalProductViews}</span>
                        </div>
                        <div class="report-stat">
                            <span class="stat-label">Vistas de categorías:</span>
                            <span class="stat-value">${report.summary.totalCategoryViews}</span>
                        </div>
                        <div class="report-stat">
                            <span class="stat-label">Búsquedas realizadas:</span>
                            <span class="stat-value">${report.summary.totalSearches}</span>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>Productos Más Vistos</h3>
                    <div class="top-items">
                        ${report.topProducts.length > 0 ? 
                            report.topProducts.map(([product, views], index) => `
                                <div class="top-item">
                                    <span class="item-rank">#${index + 1}</span>
                                    <span class="item-name">${product}</span>
                                    <span class="item-count">${views} vistas</span>
                                </div>
                            `).join('') :
                            '<p class="no-data">No hay datos de productos vistos</p>'
                        }
                    </div>
                </div>

                <div class="report-section">
                    <h3>Categorías Más Visitadas</h3>
                    <div class="top-items">
                        ${report.topCategories.length > 0 ? 
                            report.topCategories.map(([category, views], index) => `
                                <div class="top-item">
                                    <span class="item-rank">#${index + 1}</span>
                                    <span class="item-name">${category}</span>
                                    <span class="item-count">${views} vistas</span>
                                </div>
                            `).join('') :
                            '<p class="no-data">No hay datos de categorías visitadas</p>'
                        }
                    </div>
                </div>

                <div class="report-section">
                    <h3>Búsquedas Recientes</h3>
                    <div class="recent-searches">
                        ${report.recentSearches.length > 0 ? 
                            report.recentSearches.map(search => `
                                <div class="search-item">
                                    <span class="search-query">"${search.query}"</span>
                                    <span class="search-time">${formatDate(search.timestamp, 'datetime')}</span>
                                </div>
                            `).join('') :
                            '<p class="no-data">No hay búsquedas registradas</p>'
                        }
                    </div>
                </div>
            </div>
            <div class="admin-modal-footer">
                <button id="downloadAnalyticsBtn" class="admin-btn admin-btn-primary">
                    <i class="fas fa-download"></i> Descargar
                </button>
                <button class="admin-btn admin-btn-secondary" onclick="this.closest('.admin-modal-overlay').remove()">
                    Cerrar
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(analyticsModal);

    // Event listeners
    analyticsModal.querySelector('.admin-modal-close').addEventListener('click', () => {
        analyticsModal.remove();
    });

    document.getElementById('downloadAnalyticsBtn').addEventListener('click', () => {
        downloadAnalyticsReport(report);
    });
}

/**
 * Descargar reporte de analytics
 */
function downloadAnalyticsReport(report) {
    const reportText = `
REPORTE DE ANALYTICS
===================

Resumen de Actividad:
- Vistas de página: ${report.summary.totalPageViews}
- Vistas de productos: ${report.summary.totalProductViews}
- Vistas de categorías: ${report.summary.totalCategoryViews}
- Búsquedas realizadas: ${report.summary.totalSearches}

Productos Más Vistos:
${report.topProducts.map(([product, views], index) => 
    `${index + 1}. ${product} - ${views} vistas`
).join('\n')}

Categorías Más Visitadas:
${report.topCategories.map(([category, views], index) => 
    `${index + 1}. ${category} - ${views} vistas`
).join('\n')}

Búsquedas Recientes:
${report.recentSearches.map(search => 
    `- "${search.query}" (${formatDate(search.timestamp, 'datetime')})`
).join('\n')}

Generado el: ${formatDate(report.generatedAt, 'datetime')}
    `.trim();

    downloadFile(reportText, `analytics-${formatDate(new Date(), 'short').replace(/\//g, '-')}.txt`);
    showNotification('Reporte de analytics descargado', 'success');
}

console.log('📊 Admin Reports Script Loaded Successfully');
