/**
 * ADMIN PANEL - GESTIÓN DE CATEGORÍAS
 * Sistema ultra moderno para gestión de categorías con modal por pasos
 */

// ==================== MODAL ULTRA MODERNO ====================

/**
 * Abrir modal ultra moderno
 */
function openUltraModal(category = null) {
    editingCategoryId = category ? category.id : null;
    currentStep = 1;
    
    if (!ultraModal) {
        console.error('Modal ultra moderno no encontrado');
        return;
    }

    // Reset form
    resetUltraForm();
    
    // Si estamos editando, llenar datos
    if (category) {
        fillEditForm(category);
    }

    // Mostrar modal
    ultraModal.classList.add('active');
    updateStepNavigation();
    showStep(1);
    
    // Focus en primer input
    setTimeout(() => {
        const firstInput = ultraModal.querySelector('.form-step.active input');
        if (firstInput) firstInput.focus();
    }, 300);
}

/**
 * Cerrar modal ultra moderno
 */
function closeUltraModal() {
    if (!ultraModal) return;
    
    ultraModal.classList.remove('active');
    resetUltraForm();
    editingCategoryId = null;
    currentStep = 1;
}

/**
 * Resetear formulario ultra moderno
 */
function resetUltraForm() {
    const form = document.getElementById('ultraCategoryForm');
    if (form) {
        form.reset();
    }
    
    // Reset subcategorías
    currentSubcategories = [];
    updateSubcategoriesDisplay();
    
    // Reset selecciones
    document.querySelectorAll('.icon-item.active').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelectorAll('.color-item.active').forEach(item => {
        item.classList.remove('active');
    });
    
    // Reset toggles
    document.querySelectorAll('.ultra-toggle input').forEach(toggle => {
        toggle.checked = false;
    });
}

/**
 * Llenar formulario para edición
 */
function fillEditForm(category) {
    // Información básica
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categorySlug').value = category.slug;
    document.getElementById('categoryDescription').value = category.description;
    document.getElementById('categoryMetaDescription').value = category.metaDescription || '';
    
    // Subcategorías
    currentSubcategories = [...category.subcategories];
    updateSubcategoriesDisplay();
    
    // Icono
    const iconElement = document.querySelector(`[data-icon="${category.icon}"]`);
    if (iconElement) {
        selectIcon(iconElement);
    }
    
    // Color
    const colorElement = document.querySelector(`[data-color="${category.color}"]`);
    if (colorElement) {
        selectColor(colorElement);
    }
    
    // Configuraciones
    document.getElementById('categoryVisible').checked = category.visible;
    document.getElementById('categoryFeatured').checked = category.featured;
    document.getElementById('categoryOrder').value = category.order || 1;
}

// ==================== NAVEGACIÓN POR PASOS ====================

/**
 * Ir al paso anterior
 */
function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
        updateStepNavigation();
    }
}

/**
 * Ir al siguiente paso
 */
function nextStep() {
    if (validateCurrentStep() && currentStep < totalSteps) {
        currentStep++;
        showStep(currentStep);
        updateStepNavigation();
    }
}

/**
 * Mostrar paso específico
 */
function showStep(step) {
    // Ocultar todos los pasos
    formSteps.forEach(stepEl => {
        stepEl.classList.remove('active');
    });
    
    // Mostrar paso actual
    const currentStepEl = document.querySelector(`[data-step="${step}"]`);
    if (currentStepEl) {
        currentStepEl.classList.add('active');
    }
    
    // Actualizar navegación de pasos
    stepElements.forEach((stepEl, index) => {
        stepEl.classList.toggle('active', index + 1 === step);
    });
}

/**
 * Actualizar navegación de pasos
 */
function updateStepNavigation() {
    if (prevStepBtn) {
        prevStepBtn.disabled = currentStep === 1;
    }
    
    if (nextStepBtn) {
        nextStepBtn.style.display = currentStep === totalSteps ? 'none' : 'flex';
    }
    
    if (saveUltraBtn) {
        saveUltraBtn.style.display = currentStep === totalSteps ? 'flex' : 'none';
    }
    
    // Actualizar indicador de paso
    const stepIndicator = document.querySelector('.step-indicator');
    if (stepIndicator) {
        stepIndicator.innerHTML = `Paso <span class="current-step">${currentStep}</span> de ${totalSteps}`;
    }
}

/**
 * Validar paso actual
 */
function validateCurrentStep() {
    const currentStepEl = document.querySelector(`[data-step="${currentStep}"]`);
    if (!currentStepEl) return true;
    
    const requiredInputs = currentStepEl.querySelectorAll('input[required], textarea[required]');
    let isValid = true;
    
    requiredInputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
            
            // Mostrar mensaje de error
            showFieldError(input, 'Este campo es requerido');
        } else {
            input.classList.remove('error');
            hideFieldError(input);
        }
    });
    
    // Validaciones específicas por paso
    switch (currentStep) {
        case 1:
            isValid = validateBasicInfo() && isValid;
            break;
        case 2:
            isValid = validateIconAndColor() && isValid;
            break;
        case 3:
            isValid = validateSubcategories() && isValid;
            break;
    }
    
    return isValid;
}

/**
 * Validar información básica
 */
function validateBasicInfo() {
    const nameInput = document.getElementById('categoryName');
    const slugInput = document.getElementById('categorySlug');
    
    if (!nameInput || !slugInput) return false;
    
    const name = nameInput.value.trim();
    const slug = slugInput.value.trim();
    
    // Validar nombre único
    const existingCategory = categories.find(cat => 
        cat.name.toLowerCase() === name.toLowerCase() && 
        cat.id !== editingCategoryId
    );
    
    if (existingCategory) {
        showFieldError(nameInput, 'Ya existe una categoría con este nombre');
        return false;
    }
    
    // Validar slug único
    const existingSlug = categories.find(cat => 
        cat.slug === slug && 
        cat.id !== editingCategoryId
    );
    
    if (existingSlug) {
        showFieldError(slugInput, 'Ya existe una categoría con este slug');
        return false;
    }
    
    // Auto-generar slug si está vacío
    if (!slug && name) {
        slugInput.value = generateSlug(name);
    }
    
    return true;
}

/**
 * Validar icono y color
 */
function validateIconAndColor() {
    const selectedIcon = document.querySelector('.icon-item.active');
    const selectedColor = document.querySelector('.color-item.active');
    
    if (!selectedIcon) {
        showNotification('Por favor selecciona un icono', 'warning');
        return false;
    }
    
    if (!selectedColor) {
        showNotification('Por favor selecciona un color', 'warning');
        return false;
    }
    
    return true;
}

/**
 * Validar subcategorías
 */
function validateSubcategories() {
    if (currentSubcategories.length === 0) {
        showNotification('Agrega al menos una subcategoría', 'warning');
        return false;
    }
    
    return true;
}

// ==================== GESTIÓN DE SUBCATEGORÍAS ====================

/**
 * Agregar subcategoría
 */
function addSubcategory() {
    const input = subcategoryInput;
    if (!input) return;
    
    const subcategoryName = input.value.trim();
    
    if (!subcategoryName) {
        showNotification('Ingresa el nombre de la subcategoría', 'warning');
        return;
    }
    
    if (currentSubcategories.includes(subcategoryName)) {
        showNotification('Esta subcategoría ya existe', 'warning');
        return;
    }
    
    currentSubcategories.push(subcategoryName);
    input.value = '';
    updateSubcategoriesDisplay();
    
    // Animar nueva subcategoría
    setTimeout(() => {
        const newItem = subcategoriesContainer.lastElementChild;
        if (newItem) {
            newItem.style.animation = 'slideInUp 0.3s ease';
        }
    }, 50);
}

/**
 * Eliminar subcategoría
 */
function removeSubcategory(index) {
    if (index >= 0 && index < currentSubcategories.length) {
        currentSubcategories.splice(index, 1);
        updateSubcategoriesDisplay();
    }
}

/**
 * Actualizar visualización de subcategorías
 */
function updateSubcategoriesDisplay() {
    if (!subcategoriesContainer) return;
    
    if (currentSubcategories.length === 0) {
        subcategoriesContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-tags"></i>
                <p>No hay subcategorías</p>
                <small>Agrega subcategorías para organizar mejor tus productos</small>
            </div>
        `;
        return;
    }
    
    subcategoriesContainer.innerHTML = currentSubcategories.map((sub, index) => `
        <div class="subcategory-item">
            <span class="subcategory-name">${sub}</span>
            <button type="button" class="remove-subcategory" onclick="removeSubcategory(${index})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

// ==================== SELECCIÓN DE ICONOS Y COLORES ====================

/**
 * Seleccionar icono
 */
function selectIcon(iconElement) {
    // Remover selección anterior
    document.querySelectorAll('.icon-item.active').forEach(item => {
        item.classList.remove('active');
    });
    
    // Seleccionar nuevo icono
    iconElement.classList.add('active');
    
    // Efecto visual
    iconElement.style.animation = 'pulse 0.3s ease';
    setTimeout(() => {
        iconElement.style.animation = '';
    }, 300);
}

/**
 * Seleccionar color
 */
function selectColor(colorElement) {
    // Remover selección anterior
    document.querySelectorAll('.color-item.active').forEach(item => {
        item.classList.remove('active');
    });
    
    // Seleccionar nuevo color
    colorElement.classList.add('active');
    
    // Efecto visual
    colorElement.style.animation = 'pulse 0.3s ease';
    setTimeout(() => {
        colorElement.style.animation = '';
    }, 300);
}

// ==================== GUARDAR CATEGORÍA ====================

/**
 * Guardar categoría
 */
function saveCategory() {
    if (!validateCurrentStep()) {
        return;
    }
    
    const formData = collectFormData();
    
    if (editingCategoryId) {
        updateCategory(editingCategoryId, formData);
    } else {
        createCategory(formData);
    }
    
    closeUltraModal();
    loadCategories();
    updateStats();
    updateProductCategorySelect();
    
    const action = editingCategoryId ? 'actualizada' : 'creada';
    showNotification(`Categoría ${action} correctamente`, 'success');
}

/**
 * Recopilar datos del formulario
 */
function collectFormData() {
    const selectedIcon = document.querySelector('.icon-item.active');
    const selectedColor = document.querySelector('.color-item.active');
    
    return {
        name: document.getElementById('categoryName').value.trim(),
        slug: document.getElementById('categorySlug').value.trim(),
        description: document.getElementById('categoryDescription').value.trim(),
        metaDescription: document.getElementById('categoryMetaDescription').value.trim(),
        icon: selectedIcon ? selectedIcon.dataset.icon : 'fas fa-folder',
        color: selectedColor ? selectedColor.dataset.color : '#667eea',
        subcategories: [...currentSubcategories],
        visible: document.getElementById('categoryVisible').checked,
        featured: document.getElementById('categoryFeatured').checked,
        order: parseInt(document.getElementById('categoryOrder').value) || 1,
        updatedAt: new Date().toISOString()
    };
}

/**
 * Crear nueva categoría
 */
function createCategory(data) {
    const newCategory = {
        id: Date.now(),
        ...data,
        productCount: 0,
        createdAt: new Date().toISOString()
    };
    
    categories.push(newCategory);
    markAsChanged();
}

/**
 * Actualizar categoría existente
 */
function updateCategory(id, data) {
    const categoryIndex = categories.findIndex(cat => cat.id === id);
    if (categoryIndex !== -1) {
        categories[categoryIndex] = {
            ...categories[categoryIndex],
            ...data
        };
        markAsChanged();
    }
}

// ==================== UTILIDADES ====================

/**
 * Generar slug desde nombre
 */
function generateSlug(name) {
    return name
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
}

/**
 * Mostrar error en campo
 */
function showFieldError(field, message) {
    hideFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    
    field.parentNode.appendChild(errorDiv);
    field.classList.add('error');
}

/**
 * Ocultar error en campo
 */
function hideFieldError(field) {
    const errorMessage = field.parentNode.querySelector('.error-message');
    if (errorMessage) {
        errorMessage.remove();
    }
    field.classList.remove('error');
}

/**
 * Animar tarjetas de categorías
 */
function animateCategoryCards() {
    const cards = document.querySelectorAll('.category-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}
