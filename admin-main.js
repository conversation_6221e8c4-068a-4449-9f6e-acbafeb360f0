/**
 * ADMIN PANEL - ARCHIVO PRINCIPAL
 * Sistema de administración moderno para tienda online
 * Incluye gestión de categorías, productos, KPIs y configuraciones
 */

// ==================== VARIABLES GLOBALES ====================

let currentSubcategories = [];
let editingCategoryId = null;
let categories = [
    {
        id: 1,
        name: 'Tecnología y Electrónicos',
        slug: 'tecnologia-electronicos',
        icon: 'fas fa-laptop',
        color: '#667eea',
        description: 'Dispositivos electrónicos de última generación y tecnología avanzada',
        subcategories: ['Computadoras', 'Smartphones', 'Tablets', 'Gaming', 'Audio', 'Fotografía'],
        visible: true,
        featured: true,
        order: 1,
        productCount: 45,
        metaDescription: 'Encuentra los mejores productos tecnológicos y electrónicos',
        createdAt: new Date().toISOString()
    },
    {
        id: 2,
        name: '<PERSON><PERSON> y J<PERSON>ín',
        slug: 'hogar-jardin',
        icon: 'fas fa-home',
        color: '#4caf50',
        description: 'Todo para tu hogar y espacios exteriores',
        subcategories: ['Muebles', 'Decoración', 'Jardín', 'Cocina', 'Baño', 'Iluminación'],
        visible: true,
        featured: true,
        order: 2,
        productCount: 32,
        metaDescription: 'Productos para el hogar y jardín de alta calidad',
        createdAt: new Date().toISOString()
    },
    {
        id: 3,
        name: 'Moda y Accesorios',
        slug: 'moda-accesorios',
        icon: 'fas fa-tshirt',
        color: '#e91e63',
        description: 'Las últimas tendencias en moda y accesorios',
        subcategories: ['Ropa Hombre', 'Ropa Mujer', 'Zapatos', 'Bolsos', 'Joyería', 'Relojes'],
        visible: true,
        featured: false,
        order: 3,
        productCount: 28,
        metaDescription: 'Moda y accesorios para todos los estilos',
        createdAt: new Date().toISOString()
    },
    {
        id: 4,
        name: 'Deportes y Fitness',
        slug: 'deportes-fitness',
        icon: 'fas fa-dumbbell',
        color: '#ff9800',
        description: 'Equipamiento deportivo y productos para mantenerte en forma',
        subcategories: ['Gimnasio', 'Running', 'Natación', 'Ciclismo', 'Yoga', 'Deportes de Equipo'],
        visible: true,
        featured: true,
        order: 4,
        productCount: 19,
        metaDescription: 'Equipamiento deportivo y fitness de calidad profesional',
        createdAt: new Date().toISOString()
    }
];

// Configuración del sistema
let adminSettings = {
    theme: 'light',
    language: 'es',
    autoSave: true,
    notifications: true,
    compactMode: false,
    itemsPerPage: 12,
    defaultView: 'grid'
};

// ==================== ELEMENTOS DEL DOM ====================

const categoriesGrid = document.getElementById('categoriesGrid');
const categoryModal = document.getElementById('categoryModal');
const categoryForm = document.getElementById('newCategoryForm');
const addCategoryBtn = document.getElementById('addCategoryBtn');
const saveCategoryBtn = document.getElementById('saveCategoryBtn');
const cancelCategoryBtn = document.getElementById('cancelCategoryBtn');
const subcategoryInput = document.getElementById('newSubcategoryInput');
const addSubcategoryBtn = document.getElementById('addSubBtn');
const subcategoriesContainer = document.getElementById('subcategoriesList');

// Elementos del modal ultra moderno
const ultraModal = document.getElementById('ultraModal');
const closeModalBtn = document.getElementById('closeUltraModal');
const prevStepBtn = document.getElementById('prevStepBtn');
const nextStepBtn = document.getElementById('nextStepBtn');
const saveUltraBtn = document.getElementById('saveUltraBtn');

// Elementos de pasos
const stepElements = document.querySelectorAll('.step');
const formSteps = document.querySelectorAll('.form-step');
let currentStep = 1;
const totalSteps = 4;

// ==================== FUNCIONES PRINCIPALES ====================

/**
 * Inicializar el sistema completo
 */
function initializeCompleteSystem() {
    console.log('🚀 Inicializando sistema completo...');

    // Inicializar sistema básico primero
    setupEventListeners();
    loadCategories();
    updateStats();

    // Inicializar funcionalidades básicas que siempre están disponibles
    initializeBasicFunctionality();

    // Mensaje de inicialización completa
    setTimeout(() => {
        if (typeof showNotification === 'function') {
            showNotification('Sistema inicializado correctamente', 'success');
        }
        console.log('✅ Sistema completamente inicializado');
        console.log('📁 Categorías:', categories.length);
    }, 500);
}

/**
 * Inicializar funcionalidades básicas
 */
function initializeBasicFunctionality() {
    // Animar las tarjetas de KPI al cargar
    const kpiCards = document.querySelectorAll('.admin-kpi-card');
    kpiCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });

    // Inicializar animaciones para las tarjetas de estadísticas
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 * index);
    });

    // Funcionalidad para el menú móvil
    initializeMobileMenu();
}

/**
 * Configurar event listeners principales
 */
function setupEventListeners() {
    // Tabs functionality
    const tabs = document.querySelectorAll('.admin-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => switchTab(tab));
    });

    // Product cards functionality
    document.addEventListener('click', (e) => {
        if (e.target.closest('.add-product-card')) {
            if (typeof openProductModal === 'function') {
                openProductModal();
            }
        }
        if (e.target.closest('.edit-product')) {
            editProduct(e.target.closest('.product-card'));
        }
        if (e.target.closest('.delete-product')) {
            deleteProduct(e.target.closest('.product-card'));
        }
    });

    // Toggle switches
    document.addEventListener('change', (e) => {
        if (e.target.matches('.product-toggle input')) {
            toggleProductStatus(e.target);
        }
    });

    // KPI period buttons
    const kpiButtons = document.querySelectorAll('.admin-kpi-period-btn');
    kpiButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            kpiButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            if (typeof updateChartsAndData === 'function') {
                updateChartsAndData(btn.textContent.trim());
            }
        });
    });

    // Botón para nueva categoría
    const newCategoryBtn = document.getElementById('newCategoryBtn');
    if (newCategoryBtn) {
        newCategoryBtn.addEventListener('click', () => {
            if (typeof openUltraModal === 'function') {
                openUltraModal();
            }
        });
    }
}

/**
 * Cambiar entre pestañas
 */
function switchTab(clickedTab) {
    // Remover clase active de todas las pestañas
    document.querySelectorAll('.admin-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Agregar clase active a la pestaña clickeada
    clickedTab.classList.add('active');

    // Ocultar todo el contenido de pestañas
    document.querySelectorAll('.admin-tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Mostrar el contenido correspondiente
    const targetId = clickedTab.getAttribute('data-tab');
    const targetContent = document.getElementById(targetId);
    if (targetContent) {
        targetContent.classList.add('active');
    }
}

/**
 * Editar producto
 */
function editProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    console.log('Editando producto:', productName);

    if (typeof openProductModal === 'function') {
        openProductModal();
        // Aquí se podría llenar el formulario con los datos del producto
    }
}

/**
 * Eliminar producto
 */
function deleteProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;

    if (confirm(`¿Estás seguro de que deseas eliminar "${productName}"?`)) {
        productCard.remove();
        if (typeof showNotification === 'function') {
            showNotification('Producto eliminado correctamente', 'success');
        }
    }
}

/**
 * Toggle del estado del producto
 */
function toggleProductStatus(toggle) {
    const productCard = toggle.closest('.product-card');
    const badge = productCard.querySelector('.product-badge');

    if (toggle.checked) {
        badge.classList.remove('badge-inactive');
        badge.classList.add('badge-active');
    } else {
        badge.classList.remove('badge-active');
        badge.classList.add('badge-inactive');
    }
}

/**
 * Cargar categorías en el grid
 */
function loadCategories() {
    const categoriesDisplay = document.getElementById('categoriesDisplay');
    if (!categoriesDisplay) return;

    categoriesDisplay.innerHTML = '';

    // Cargar categorías existentes
    categories
        .filter(cat => cat.visible !== false)
        .sort((a, b) => a.order - b.order)
        .forEach(category => {
            const categoryCard = createCategoryCard(category);
            categoriesDisplay.appendChild(categoryCard);
        });

    // Animar entrada de las tarjetas
    animateCategoryCards();
}

/**
 * Actualizar estadísticas
 */
function updateStats() {
    const totalCategories = categories.length;
    const activeCategories = categories.filter(cat => cat.visible !== false).length;
    const totalProducts = categories.reduce((sum, cat) => sum + (cat.productCount || 0), 0);

    // Actualizar stats en el hero section
    const heroStats = document.querySelectorAll('.hero-stat .stat-value');
    if (heroStats.length >= 3) {
        heroStats[0].textContent = totalCategories;
        heroStats[1].textContent = activeCategories;
        heroStats[2].textContent = totalProducts;
    }
}

/**
 * Animar tarjetas de categorías
 */
function animateCategoryCards() {
    const cards = document.querySelectorAll('.category-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });
}

/**
 * Crear tarjeta de categoría
 */
function createCategoryCard(category) {
    const card = document.createElement('div');
    card.className = 'category-card';
    card.dataset.categoryId = category.id;

    card.innerHTML = `
        <div class="category-icon-container" style="background: ${category.color}20;">
            <i class="${category.icon}" style="color: ${category.color};"></i>
        </div>
        <div class="category-info">
            <h3 class="category-title">${category.name}</h3>
            <p class="category-description">${category.description}</p>
            <div class="category-stats">
                <span class="product-count">${category.productCount || 0} productos</span>
                <span class="subcategory-count">${category.subcategories?.length || 0} subcategorías</span>
            </div>
            <div class="category-subcategories">
                ${(category.subcategories || []).slice(0, 3).map(sub =>
                    `<span class="subcategory-tag">${sub}</span>`
                ).join('')}
                ${(category.subcategories?.length || 0) > 3 ?
                    `<span class="subcategory-tag more">+${(category.subcategories?.length || 0) - 3}</span>` : ''
                }
            </div>
        </div>
        <div class="category-actions">
            <button class="category-action-btn edit-category" title="Editar">
                <i class="fas fa-edit"></i>
            </button>
            <button class="category-action-btn delete-category" title="Eliminar">
                <i class="fas fa-trash"></i>
            </button>
            <button class="category-action-btn toggle-category" title="Activar/Desactivar">
                <i class="fas fa-eye${category.visible !== false ? '' : '-slash'}"></i>
            </button>
        </div>
    `;

    // Event listeners para las acciones
    card.querySelector('.edit-category').addEventListener('click', (e) => {
        e.stopPropagation();
        editCategory(category);
    });

    card.querySelector('.delete-category').addEventListener('click', (e) => {
        e.stopPropagation();
        deleteCategory(category.id);
    });

    card.querySelector('.toggle-category').addEventListener('click', (e) => {
        e.stopPropagation();
        toggleCategoryVisibility(category.id);
    });

    return card;
}

/**
 * Editar categoría
 */
function editCategory(category) {
    console.log('Editando categoría:', category.name);
    if (typeof openUltraModal === 'function') {
        openUltraModal(category);
    }
}

/**
 * Eliminar categoría
 */
function deleteCategory(categoryId) {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return;

    if (confirm(`¿Estás seguro de que deseas eliminar la categoría "${category.name}"?`)) {
        const index = categories.findIndex(cat => cat.id === categoryId);
        if (index > -1) {
            categories.splice(index, 1);
            loadCategories();
            updateStats();
            if (typeof showNotification === 'function') {
                showNotification('Categoría eliminada correctamente', 'success');
            }
        }
    }
}

/**
 * Toggle visibilidad de categoría
 */
function toggleCategoryVisibility(categoryId) {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return;

    category.visible = !category.visible;
    loadCategories();
    updateStats();

    if (typeof showNotification === 'function') {
        showNotification(`Categoría ${category.visible ? 'activada' : 'desactivada'}`, 'success');
    }
}

/**
 * Inicializar menú móvil
 */
function initializeMobileMenu() {
    const menuToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.admin-sidebar');

    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });

        // Cerrar el menú al hacer clic en un enlace
        const menuLinks = document.querySelectorAll('.admin-menu-link');
        menuLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // Cerrar el menú al hacer clic fuera
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !menuToggle.contains(event.target) &&
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Cerrar con tecla Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });
    }
}

// ==================== EXPORT DE FUNCIONES GLOBALES ====================

// Hacer funciones disponibles globalmente para debugging
window.adminSystem = {
    categories,
    adminSettings,
    loadCategories,
    updateStats,
    switchTab,
    editProduct,
    deleteProduct,
    toggleProductStatus,
    editCategory,
    deleteCategory,
    toggleCategoryVisibility
};

// ==================== INICIALIZACIÓN ====================

// Ejecutar cuando el DOM esté completamente cargado
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeCompleteSystem);
} else {
    initializeCompleteSystem();
}

// Log final
console.log('📋 Admin Panel Script Loaded Successfully');
console.log('🔧 Funciones disponibles en window.adminSystem');
