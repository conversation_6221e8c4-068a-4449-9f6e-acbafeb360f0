<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Gestión de Tienda</title>

    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- ===== ESTILOS CSS ===== -->
    <style>
        /* ===== VARIABLES CSS GLOBALES ===== */
        :root {
            /* Colores principales */
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --purple-primary: #6a1b9a;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #3b82f6;

            /* Colores de texto */
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --text-white: #ffffff;

            /* Colores de fondo */
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --bg-dark: #1f2937;

            /* Bordes y radios */
            --border-color: #e5e7eb;
            --border-radius: 8px;
            --border-radius-lg: 12px;

            /* Sombras */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* Transiciones */
            --transition: all 0.2s ease-in-out;

            /* Tamaños de fuente */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
        }

        /* ===== RESET Y ESTILOS BASE ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* ===== LAYOUT PRINCIPAL ===== */
        /* Sidebar lateral izquierdo */
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            z-index: 1000;
            overflow-y: auto;
            transition: var(--transition);
        }

        /* Contenido principal (a la derecha del sidebar) */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: var(--transition);
        }

        /* ===== BOTÓN MENÚ MÓVIL ===== */
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* ===== HEADER DEL SIDEBAR ===== */
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Logo de la aplicación */
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .logo i {
            font-size: 1.5rem;
            color: #fbbf24;
        }

        .logo h2 {
            font-size: var(--font-size-xl);
            font-weight: 700;
        }

        /* Subtítulo del logo */
        .subtitle {
            font-size: var(--font-size-sm);
            opacity: 0.8;
        }

        /* ===== NAVEGACIÓN DEL SIDEBAR ===== */
        .sidebar-nav {
            padding: 1rem 0;
        }

        /* Secciones de navegación */
        .nav-section {
            margin-bottom: 2rem;
        }

        /* Títulos de las secciones */
        .nav-section h3 {
            padding: 0 1.5rem;
            font-size: var(--font-size-xs);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            opacity: 0.7;
            margin-bottom: 0.75rem;
            font-weight: 600;
        }

        .nav-section ul {
            list-style: none;
        }

        /* Enlaces de navegación */
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Estado activo del enlace */
        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }

        /* Indicador visual del enlace activo */
        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #fbbf24;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Badges de notificación en el menú */
        .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: var(--font-size-xs);
            font-weight: 600;
            margin-left: auto;
        }

        /* ===== FOOTER DEL SIDEBAR ===== */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1.5rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Perfil del usuario */
        .user-profile {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        /* Avatar del usuario */
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Información del usuario */
        .user-info h4 {
            font-size: var(--font-size-sm);
            font-weight: 600;
        }

        .user-info p {
            font-size: var(--font-size-xs);
            opacity: 0.7;
        }

        /* Botón de cerrar sesión */
        .logout-btn {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            margin-left: auto;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* ===== HEADER PRINCIPAL ===== */
        .main-header {
            background: var(--bg-primary);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
        }

        /* Lado izquierdo del header (título y subtítulo) */
        .header-left h1 {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .header-left p {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        /* Lado derecho del header (búsqueda y botones) */
        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* ===== CAJA DE BÚSQUEDA ===== */
        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            color: var(--text-secondary);
        }

        .search-box input {
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background: var(--bg-secondary);
            width: 300px;
            transition: var(--transition);
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* ===== BOTONES DEL HEADER ===== */
        .notification-btn,
        .settings-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition);
        }

        .notification-btn:hover,
        .settings-btn:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        /* Contador de notificaciones */
        .notification-count {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            background: var(--error-color);
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 10px;
            font-weight: 600;
        }

        /* ===== ÁREA DE CONTENIDO ===== */
        .content-area {
            padding: 2rem;
        }

        /* Header de las secciones */
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .section-header h2 {
            font-size: var(--font-size-2xl);
            font-weight: 700;
        }

        /* ===== BOTONES GENERALES ===== */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: var(--font-size-sm);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        /* Botón primario */
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        /* Botón secundario */
        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-tertiary);
        }

        /* ===== TARJETAS (CARDS) ===== */
        .admin-card {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            overflow: hidden;
        }

        .admin-card-body {
            padding: 1.5rem;
        }

        /* ===== SECCIÓN DE KPIs (INDICADORES) ===== */
        .kpi-section {
            margin-bottom: 3rem;
        }

        /* Header de los KPIs */
        .kpi-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .kpi-header h2 {
            font-size: var(--font-size-2xl);
            font-weight: 700;
        }

        /* Filtro de tiempo */
        .time-filter {
            display: flex;
            gap: 0.5rem;
            background: var(--bg-primary);
            padding: 0.25rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        /* Botones del filtro de tiempo */
        .time-btn {
            padding: 0.5rem 1rem;
            border: none;
            background: none;
            border-radius: calc(var(--border-radius) - 2px);
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-secondary);
        }

        .time-btn.active,
        .time-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Grid de tarjetas KPI */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
        }


        /* Tarjetas individuales de KPI */
        .kpi-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: 1rem;
            position: relative;
            overflow: hidden;
            transition: var(--transition);
        }

        .kpi-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Contenido de las tarjetas KPI */
        .kpi-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* Información del KPI */
        .kpi-info h3 {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        /* Valor principal del KPI */
        .kpi-value {
            font-size: var(--font-size-3xl);
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        /* Cambio/tendencia del KPI */
        .kpi-change {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: var(--font-size-sm);
            font-weight: 600;
        }

        .kpi-change.positive {
            color: var(--success-color);
        }

        .kpi-change.negative {
            color: var(--error-color);
        }

        /* Gráfico pequeño del KPI */
        .kpi-chart {
            margin-left: 1rem;
        }

        /* ===== GESTIÓN DE PRODUCTOS ===== */
        .container-products {
            margin-bottom: 3rem;
        }

        /* Título del contenedor de productos */
        .container-title {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            margin-bottom: 2rem;
        }

        /* ===== TABS DE ADMINISTRACIÓN ===== */
        .admin-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
            overflow-x: auto;
            padding-bottom: 0;
        }

    /* Estilos para las pestañas */
        .admin-tab {
            padding: 0.75rem 1.5rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-bottom: none;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            cursor: pointer;
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-secondary);
            transition: var(--transition);
            margin-right: 0.25rem;
        }

        .admin-tab:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .admin-tab.active {
            background: var(--bg-primary);
            color: var(--primary-color);
            border-color: var(--primary-color);
            font-weight: 600;
        }

        /* Contenido de las pestañas */
        .admin-tab-content {
            display: none;
            padding: 1.5rem 0;
        }

        .admin-tab-content.active {
            display: block;
        }

        /* ===== GRID DE PRODUCTOS ===== */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 1rem;
        }


        /* ===== TARJETAS DE PRODUCTO ===== */
        .product-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: 1rem;
            transition: var(--transition);
            cursor: pointer;
        }


        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        /* Tarjeta para agregar nuevo producto */
        .add-product-card {
            border: 2px dashed var(--border-color);
            background: var(--bg-secondary);
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            transition: var(--transition);
        }

        .add-product-card:hover {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-2px);
        }

        /* Imagen del producto */
        .product-image {
            width: 100%;
            height: 150px;
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 0.75rem;
            position: relative;
        }


        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .product-card:hover .product-image img {
            transform: scale(1.05);
        }

        /* Overlay de acciones del producto */
        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            opacity: 0;
            transition: var(--transition);
        }

        .product-card:hover .product-overlay {
            opacity: 1;
        }

        /* Botones del overlay */
        .product-overlay-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background: white;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            box-shadow: var(--shadow);
        }

        .product-overlay-btn:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        /* Botón de editar producto */
        .edit-product:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Botón de eliminar producto */
        .delete-product:hover {
            background: var(--error-color);
            color: white;
        }

        /* ===== DETALLES DEL PRODUCTO ===== */
        .product-details {
            padding: 0.5rem;
        }

        /* Categoría del producto */
        .product-category {
            font-size: 0.55rem;
            color: var(--text-secondary);
            margin-bottom: 0.3rem;
            text-transform: uppercase;
            letter-spacing: 0.02em;
            line-height: 1.0;
        }

        /* Nombre del producto */
        .product-name {
            font-size: 0.7rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.3rem;
            line-height: 1.1;
        }


        /* Precios del producto */
        .product-price {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .product-price {
            margin: 0.3rem 0;
        }

        .product-status {
            margin-top: 0.3rem;
        }

        /* Precio actual */
        .current-price {
            font-size: 0.8rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        /* Precio original (tachado) */
        .original-price {
            font-size: 0.6rem;
            color: var(--text-light);
            text-decoration: line-through;
            margin-left: 0.3rem;
        }

        /* Estado del producto */
        .product-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        /* Badge del producto */
        .product-badge {
            font-size: 0.5rem;
            font-weight: 600;
            padding: 0.15rem 0.3rem;
            border-radius: 8px;
            text-transform: uppercase;
            letter-spacing: 0.02em;
        }

        .badge-active {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
        }

        /* ===== TOGGLE SWITCH ===== */
        .product-toggle {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .product-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        /* Slider del toggle */
        .product-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: var(--transition);
            border-radius: 24px;
        }

        .product-toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: var(--transition);
            border-radius: 50%;
        }

        .product-toggle input:checked + .product-toggle-slider {
            background-color: var(--primary-color);
        }

        .product-toggle input:checked + .product-toggle-slider:before {
            transform: translateX(20px);
        }

        /* ===== MODAL ===== */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Contenedor del modal */
        .modal-container {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: var(--transition);
        }

        .modal-overlay.active .modal-container {
            transform: scale(1);
        }

        /* Header del modal */
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Botón cerrar modal */
        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .modal-close:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        /* Cuerpo del modal */
        .modal-body {
            padding: 1.5rem;
        }

        /* Footer del modal */
        .modal-footer {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding: 1.5rem;
            border-top: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        /* ===== FORMULARIOS ===== */
        /* Grid de formularios */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-bottom: 2rem;
        }

        /* Sección de formulario */
        .form-section {
            background: var(--bg-secondary);
            padding: 2rem;
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        /* Título de sección de formulario */
        .form-section-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--border-color);
        }

        .form-section-title i {
            color: var(--primary-color);
            font-size: 1.25rem;
        }

        /* Grupo de formulario */
        .form-group {
            margin-bottom: 1.5rem;
        }
        /* Etiqueta de formulario */
        .form-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-label i {
            color: var(--primary-color);
            width: 16px;
        }

        /* Campos de entrada del formulario */
        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: var(--font-size-base);
            transition: var(--transition);
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Área de texto */
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* Texto de ayuda del formulario */
        .form-help {
            display: block;
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            margin-top: 0.25rem;
            font-style: italic;
        }

        /* Fila de formulario (dos columnas) */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        /* Acciones del formulario */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        /* ===== VISTA PREVIA Y MAPA ===== */
        /* Vista previa del mapa */
        .map-preview {
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            background: var(--bg-tertiary);
        }

        /* Contenedor de vista previa */
        .preview-container {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-top: 1rem;
        }

        .preview-header h1 {
            font-size: var(--font-size-2xl);
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .preview-header p {
            color: var(--text-secondary);
            font-size: var(--font-size-base);
            line-height: 1.6;
        }

        /* Secciones de vista previa */
        .preview-about,
        .preview-contact {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .preview-about h3,
        .preview-contact h3 {
            font-size: var(--font-size-lg);
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        /* Información de contacto */
        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }

        .contact-item i {
            color: var(--primary-color);
            width: 20px;
            text-align: center;
        }

        /* ===== ÁREA DE SUBIDA DE ARCHIVOS ===== */
        .file-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            background: var(--bg-secondary);
            transition: var(--transition);
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
        }

        .file-upload-placeholder i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .file-upload-placeholder p {
            font-size: var(--font-size-base);
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .file-upload-placeholder span {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        /* ===== CHECKBOXES ===== */
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-size: var(--font-size-sm);
            color: var(--text-primary);
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        /* Checkmark personalizado */
        .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 3px;
            position: relative;
            transition: var(--transition);
        }

        .checkbox-item input[type="checkbox"]:checked + .checkmark {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkbox-item input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* ===== ESTADOS DE VALIDACIÓN ===== */
        .form-input.error,
        .form-textarea.error,
        .form-select.error {
            border-color: var(--error-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success,
        .form-textarea.success,
        .form-select.success {
            border-color: var(--success-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        /* ===== CONTADOR DE CARACTERES ===== */
        .char-counter {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            text-align: right;
            margin-top: 0.25rem;
        }

        .char-counter.warning {
            color: var(--warning-color);
        }

        .char-counter.error {
            color: var(--error-color);
        }

        /* ===== NOTIFICACIONES ===== */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        }

        .notification.success {
            border-left: 4px solid var(--success-color);
        }

        .notification.error {
            border-left: 4px solid var(--error-color);
        }

        .notification.warning {
            border-left: 4px solid var(--warning-color);
        }

        .notification.info {
            border-left: 4px solid var(--info-color);
        }

        .notification-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .notification-close:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        /* ===== TOOLTIPS ===== */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: var(--bg-dark);
            color: var(--text-white);
            text-align: center;
            border-radius: var(--border-radius);
            padding: 0.5rem;
            font-size: var(--font-size-sm);
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: var(--bg-dark) transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* ===== ESTADOS DE CARGA ===== */
        .loading {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* ===== ACCESIBILIDAD ===== */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Estados de foco mejorados */
        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus,
        .btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* ===== MODO OSCURO (OPCIONAL) ===== */
        @media (prefers-color-scheme: dark) {
            :root {
                --text-primary: #f9fafb;
                --text-secondary: #d1d5db;
                --text-light: #9ca3af;
                --bg-primary: #1f2937;
                --bg-secondary: #111827;
                --bg-tertiary: #374151;
                --border-color: #374151;
            }
        }
        @media (max-width: 1400px) {
            .products-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 768px) {
            /* Mostrar botón de menú móvil */
            .mobile-menu-toggle {
                display: block;
            }

            /* Ocultar sidebar por defecto en móvil */
            .admin-sidebar {
                transform: translateX(-100%);
            }

            /* Mostrar sidebar cuando esté activo */
            .admin-sidebar.active {
                transform: translateX(0);
            }

            /* Ajustar contenido principal */
            .main-content {
                margin-left: 0;
            }

            /* Ajustar header */
            .main-header {
                padding: 1rem;
                margin-left: 60px;
            }

            .header-right {
                gap: 0.5rem;
            }

            .search-box input {
                width: 200px;
            }

            /* Ajustar contenido */
            .content-area {
                padding: 1rem;
            }

            /* Grid de una columna */
            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            /* Ajustar tabs */
            .admin-tabs {
                flex-wrap: nowrap;
                overflow-x: auto;
            }

            /* Ajustar header de sección */
            .section-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            /* Ajustar modal */
            .modal-container {
                width: 95%;
                margin: 1rem;
            }

            /* Ajustar acciones de formulario */
            .form-actions {
                flex-direction: column;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .contact-info {
                gap: 1rem;
            }

            .preview-container {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            /* Header en columna para pantallas muy pequeñas */
            .main-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .header-right {
                justify-content: space-between;
            }

            .search-box input {
                width: 100%;
            }

            /* Ajustar KPI cards */
            .kpi-card {
                padding: 1rem;
            }

            .kpi-value {
                font-size: var(--font-size-2xl);
            }

            /* Ajustar detalles de producto */
            .product-details {
                padding: 1rem;
            }
            .products-grid {
                grid-template-columns: 1fr;
            }
        }

        /* ===== ANIMACIONES ===== */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Aplicar animaciones a elementos */
        .kpi-card,
        .product-card,
        .admin-card {
            animation: fadeIn 0.6s ease forwards;
        }

        /* Animaciones escalonadas para KPI cards */
        .kpi-card:nth-child(1) { animation-delay: 0.1s; }
        .kpi-card:nth-child(2) { animation-delay: 0.2s; }
        .kpi-card:nth-child(3) { animation-delay: 0.3s; }
        .kpi-card:nth-child(4) { animation-delay: 0.4s; }
        .kpi-card:nth-child(5) { animation-delay: 0.5s; }
        .kpi-card:nth-child(6) { animation-delay: 0.6s; }
        .kpi-card:nth-child(7) { animation-delay: 0.7s; }
        .kpi-card:nth-child(8) { animation-delay: 0.8s; }

        /* Animaciones para formularios */
        .form-section {
            animation: fadeInUp 0.6s ease forwards;
        }

        .form-section:nth-child(1) { animation-delay: 0.1s; }
        .form-section:nth-child(2) { animation-delay: 0.2s; }

        /* ===== SCROLLBAR PERSONALIZADO ===== */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

/* ===== ESTILOS ESPECÍFICOS PARA EL MODAL DE AGREGAR PRODUCTO ===== */

/* Contenedor de input de precio */
.price-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.currency-symbol {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    z-index: 1;
}

.price-input {
    padding-left: 2.5rem !important;
}

/* Área de subida de imagen mejorada */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--bg-secondary);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.file-upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.file-upload-placeholder i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.file-upload-placeholder p {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.file-upload-placeholder span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Vista previa de imagen */
.image-preview {
    position: relative;
    max-width: 100%;
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.change-image-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition);
}

.change-image-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Contador de caracteres */
.char-counter {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    text-align: right;
    margin-top: 0.25rem;
}

.char-counter.warning {
    color: var(--warning-color);
}

.char-counter.danger {
    color: var(--error-color);
}

/* Estados de validación mejorados */
.form-input.invalid,
.form-select.invalid,
.form-textarea.invalid {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.valid,
.form-select.valid,
.form-textarea.valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Animación del modal */
.modal-overlay {
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    transform: scale(0.7) translateY(-50px);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

/* Responsive para el modal */
@media (max-width: 768px) {
    .modal-container {
        width: 95%;
        max-height: 95vh;
        margin: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .file-upload-area {
        padding: 1.5rem;
        min-height: 150px;
    }
    
    .file-upload-placeholder i {
        font-size: 2rem;
    }
}

    </style>

</head>
<body>
    <!-- ===== BOTÓN DE MENÚ MÓVIL ===== -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- ===== SIDEBAR LATERAL ===== -->
    <aside class="admin-sidebar">
        <!-- Header del sidebar con logo -->
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h2>AdminPanel</h2>
            </div>
            <p class="subtitle">Gestión de Tienda</p>
        </div>

        <!-- Navegación principal -->
        <nav class="sidebar-nav">
            <!-- Sección principal -->
            <div class="nav-section">
                <h3>Principal</h3>
                <ul>
                    <li>
                        <a href="#dashboard" class="nav-link active" data-section="dashboard">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>

                </ul>
            </div>

            <!-- Sección de configuración -->
            <div class="nav-section">
                <h3>Configuración</h3>
                <ul>
                    <li>
                        <a href="#empresa" class="nav-link" data-section="empresa">
                            <i class="fas fa-building"></i>
                            <span>Datos Empresa</span>
                        </a>
                    </li>
                    <li>
                        <a href="#tienda" class="nav-link" data-section="tienda">
                            <i class="fas fa-cog"></i>
                            <span>Configuración</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Footer del sidebar con perfil de usuario -->
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-info">
                    <h4>Administrador</h4>
                    <p><EMAIL></p>
                </div>
                <button class="logout-btn" title="Cerrar Sesión">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- ===== CONTENIDO PRINCIPAL ===== -->
    <main class="main-content">
        <!-- Header principal con título y búsqueda -->
        <header class="main-header">
            <div class="header-left">
                <h1 class="page-title">Dashboard</h1>
                <p class="page-subtitle">Resumen general de tu tienda</p>
            </div>
            <div class="header-right">
                <!-- Caja de búsqueda -->
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Buscar productos, pedidos...">
                </div>
                <!-- Botón de notificaciones -->
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">2</span>
                </button>
                <!-- Botón de configuración -->
                <button class="settings-btn">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- ===== CONTENIDO DEL DASHBOARD ===== -->
        <div class="content-area" id="dashboard">
            <!-- Sección de KPIs (Indicadores Clave) -->
            <section class="kpi-section">
                <div class="kpi-header">
                    <h2>Indicadores Clave</h2>
                    <!-- Filtro de tiempo -->
                    <div class="time-filter">
                        <button class="time-btn active" data-period="today">Hoy</button>
                        <button class="time-btn" data-period="week">Semana</button>
                        <button class="time-btn" data-period="month">Mes</button>
                        <button class="time-btn" data-period="year">Año</button>
                    </div>
                </div>

                <!-- Grid de tarjetas KPI -->
                <div class="kpi-grid">
                    <!-- KPI 1: Visitas a la página -->
                    <div class="kpi-card visits">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Visitas a la página</h3>
                                <div class="kpi-value">1,254</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>12.5%</span>
                                </div>
                            </div>
                        </div>
                        <!-- Gráfico SVG pequeño -->
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <path d="M0,25 L10,22 L20,18 L30,15 L40,12 L50,8 L60,5" stroke="#8b5cf6" stroke-width="2" fill="none"></path>
                                <path d="M0,25 L10,22 L20,18 L30,15 L40,12 L50,8 L60,5 L60,30 L0,30" fill="#8b5cf6" opacity="0.1"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 2: Visitas desde móvil -->
                    <div class="kpi-card mobile">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Visitas desde móvil</h3>
                                <div class="kpi-value">68%</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>5.3%</span>
                                </div>
                            </div>
                        </div>
                        <!-- Gráfico de barras SVG -->
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <rect x="5" y="20" width="6" height="10" fill="#8b5cf6" opacity="0.4"></rect>
                                <rect x="13" y="18" width="6" height="12" fill="#8b5cf6" opacity="0.5"></rect>
                                <rect x="21" y="15" width="6" height="15" fill="#8b5cf6" opacity="0.6"></rect>
                                <rect x="29" y="12" width="6" height="18" fill="#8b5cf6" opacity="0.7"></rect>
                                <rect x="37" y="8" width="6" height="22" fill="#8b5cf6" opacity="0.8"></rect>
                                <rect x="45" y="5" width="6" height="25" fill="#8b5cf6"></rect>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 3: Tiempo promedio -->
                    <div class="kpi-card time">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Tiempo promedio</h3>
                                <div class="kpi-value">3m 42s</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>0:45</span>
                                </div>
                            </div>
                        </div>
                        <!-- Gráfico circular SVG -->
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <circle cx="30" cy="15" r="12" fill="none" stroke="#8b5cf6" stroke-width="3" stroke-dasharray="75 25"></circle>
                                <text x="30" y="19" text-anchor="middle" font-size="8" fill="#8b5cf6" font-weight="bold">75%</text>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 4: Productos compartidos -->
                    <div class="kpi-card shared">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Productos compartidos</h3>
                                <div class="kpi-value">42</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>15.7%</span>
                                </div>
                            </div>
                        </div>
                        <!-- Gráfico de puntos SVG -->
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <circle cx="15" cy="15" r="4" fill="#8b5cf6" opacity="0.3"></circle>
                                <circle cx="25" cy="10" r="3" fill="#8b5cf6" opacity="0.5"></circle>
                                <circle cx="35" cy="20" r="5" fill="#8b5cf6" opacity="0.7"></circle>
                                <circle cx="45" cy="8" r="3" fill="#8b5cf6" opacity="0.6"></circle>
                                <circle cx="50" cy="18" r="4" fill="#8b5cf6"></circle>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 5: Total Productos -->
                    <div class="kpi-card total-products">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Total Productos</h3>
                                <div class="kpi-value">18</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>3 nuevos este mes</span>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- KPI 7: Productos en Oferta -->
                    <div class="kpi-card products-offer">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Productos en Oferta</h3>
                                <div class="kpi-value">3</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>1 nuevo este mes</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- KPI 8: Productos Destacados -->
                    <div class="kpi-card products-featured">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Productos Destacados</h3>
                                <div class="kpi-value">3</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>1 nuevo este mes</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- ===== GESTIÓN DE PRODUCTOS ===== -->
            <section class="container-products">
                <h2 class="container-title">Gestión de Productos</h2>
                <div class="admin-card">
                    <div class="admin-card-body">
                        <!-- Pestañas de categorías de productos -->
                        <div class="admin-tabs">
                            <div class="admin-tab active" data-tab="destacados">Destacados</div>
                            <div class="admin-tab" data-tab="ofertas">Ofertas</div>
                            <div class="admin-tab" data-tab="novedades">Novedades</div>
                            <div class="admin-tab" data-tab="mas-vistos">Más Vistos</div>
                            <div class="admin-tab" data-tab="tendencias">Tendencias</div>
                            <div class="admin-tab" data-tab="liquidaciones">Liquidaciones</div>
                        </div>

                        <!-- ===== CONTENIDO PESTAÑA DESTACADOS ===== -->
                        <div class="admin-tab-content active" id="destacados">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar nuevo producto destacado -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto destacado</div>
                                    </div>
                                </div>

                                <!-- Producto Destacado 1: iPhone 15 Pro -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/667eea/ffffff?text=iPhone+15+Pro" alt="iPhone 15 Pro">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Smartphones</div>
                                        <h3 class="product-name">iPhone 15 Pro 256GB Titanio Natural</h3>
                                        <div class="product-price">
                                            <span class="current-price">$1.299.990</span>
                                            <span class="original-price">$1.399.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Destacado</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto Destacado 2: MacBook Pro -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/764ba2/ffffff?text=MacBook+Pro" alt="MacBook Pro">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Computación / Laptops</div>
                                        <h3 class="product-name">MacBook Pro 14" M3 Pro 512GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$2.499.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Destacado</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto Destacado 3: AirPods Pro -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/f093fb/ffffff?text=AirPods+Pro" alt="AirPods Pro">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Audio</div>
                                        <h3 class="product-name">AirPods Pro (2ª generación) USB-C</h3>
                                        <div class="product-price">
                                            <span class="current-price">$279.990</span>
                                            <span class="original-price">$299.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Destacado</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ===== CONTENIDO PESTAÑA OFERTAS ===== -->
                        <div class="admin-tab-content" id="ofertas">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar nuevo producto en oferta -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en oferta</div>
                                    </div>
                                </div>

                                <!-- Producto en Oferta 1: Samsung Galaxy S24 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/ef4444/ffffff?text=Samsung+Galaxy" alt="Samsung Galaxy S24">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Smartphones</div>
                                        <h3 class="product-name">Samsung Galaxy S24 Ultra 256GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$999.990</span>
                                            <span class="original-price">$1.299.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Oferta</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto en Oferta 2: iPad Air -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/f59e0b/ffffff?text=iPad+Air" alt="iPad Air">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Tablets</div>
                                        <h3 class="product-name">iPad Air 11" M2 128GB Wi-Fi</h3>
                                        <div class="product-price">
                                            <span class="current-price">$649.990</span>
                                            <span class="original-price">$799.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Oferta</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto en Oferta 3: Apple Watch -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/10b981/ffffff?text=Apple+Watch" alt="Apple Watch">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Wearables</div>
                                        <h3 class="product-name">Apple Watch Series 9 GPS 45mm</h3>
                                        <div class="product-price">
                                            <span class="current-price">$399.990</span>
                                            <span class="original-price">$499.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Oferta</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ===== CONTENIDO PESTAÑA NOVEDADES ===== -->
                        <div class="admin-tab-content" id="novedades">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar nueva novedad -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto de novedad</div>
                                    </div>
                                </div>

                                <!-- Novedad 1: Apple Vision Pro -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=Vision+Pro" alt="Apple Vision Pro">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Realidad Virtual</div>
                                        <h3 class="product-name">Apple Vision Pro 256GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$3.499.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Novedad</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Novedad 2: MacBook Air M3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/06b6d4/ffffff?text=MacBook+Air" alt="MacBook Air M3">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Computación / Laptops</div>
                                        <h3 class="product-name">MacBook Air 15" M3 512GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$1.699.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Novedad</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Novedad 3: Studio Display -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/ec4899/ffffff?text=Studio+Display" alt="Studio Display">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Monitores</div>
                                        <h3 class="product-name">Apple Studio Display 27" 5K</h3>
                                        <div class="product-price">
                                            <span class="current-price">$1.599.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Novedad</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ===== CONTENIDO PESTAÑA MÁS VISTOS ===== -->
                        <div class="admin-tab-content" id="mas-vistos">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto más visto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto más visto</div>
                                    </div>
                                </div>

                                <!-- Más Visto 1: iPhone 15 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/667eea/ffffff?text=iPhone+15" alt="iPhone 15">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Smartphones</div>
                                        <h3 class="product-name">iPhone 15 128GB Azul</h3>
                                        <div class="product-price">
                                            <span class="current-price">$899.990</span>
                                            <span class="original-price">$999.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Más Visto</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Más Visto 2: AirPods 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/f59e0b/ffffff?text=AirPods+3" alt="AirPods 3">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Audio</div>
                                        <h3 class="product-name">AirPods (3ª generación)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$179.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Más Visto</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Más Visto 3: iPad Pro -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/10b981/ffffff?text=iPad+Pro" alt="iPad Pro">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Tablets</div>
                                        <h3 class="product-name">iPad Pro 12.9" M2 256GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$1.199.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Más Visto</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ===== CONTENIDO PESTAÑA TENDENCIAS ===== -->
                        <div class="admin-tab-content" id="tendencias">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto en tendencia -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en tendencia</div>
                                    </div>
                                </div>

                                <!-- Tendencia 1: Mac Studio -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=Mac+Studio" alt="Mac Studio">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Computación / Desktops</div>
                                        <h3 class="product-name">Mac Studio M2 Max 512GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$1.999.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Tendencia</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tendencia 2: Magic Keyboard -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/06b6d4/ffffff?text=Magic+Keyboard" alt="Magic Keyboard">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Accesorios / Teclados</div>
                                        <h3 class="product-name">Magic Keyboard para iPad Pro</h3>
                                        <div class="product-price">
                                            <span class="current-price">$349.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Tendencia</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Tendencia 3: Apple Pencil -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/ec4899/ffffff?text=Apple+Pencil" alt="Apple Pencil">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Accesorios / Stylus</div>
                                        <h3 class="product-name">Apple Pencil (2ª generación)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$129.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Tendencia</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ===== CONTENIDO PESTAÑA LIQUIDACIONES ===== -->
                        <div class="admin-tab-content" id="liquidaciones">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto en liquidación -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en liquidación</div>
                                    </div>
                                </div>

                                <!-- Liquidación 1: iPhone 14 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/ef4444/ffffff?text=iPhone+14" alt="iPhone 14">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Smartphones</div>
                                        <h3 class="product-name">iPhone 14 128GB (Modelo Anterior)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$699.990</span>
                                            <span class="original-price">$899.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Liquidación</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Liquidación 2: MacBook Air M1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/f59e0b/ffffff?text=MacBook+Air+M1" alt="MacBook Air M1">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Computación / Laptops</div>
                                        <h3 class="product-name">MacBook Air M1 256GB (Exhibición)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$899.990</span>
                                            <span class="original-price">$1.199.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Liquidación</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Liquidación 3: iPad 9 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200/10b981/ffffff?text=iPad+9" alt="iPad 9">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Tablets</div>
                                        <h3 class="product-name">iPad (9ª generación) 64GB (Última Unidad)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$299.990</span>
                                            <span class="original-price">$399.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Liquidación</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>



 <!-- ===== SECCIÓN DE DATOS DE EMPRESA ===== -->
        <div class="content-area" id="empresa">
            <div class="section-header">
                <h2>Datos de la Empresa</h2>
                <button class="btn btn-primary" id="saveCompanyData">
                    <i class="fas fa-save"></i>
                    Guardar Cambios
                </button>
            </div>

            <div class="admin-card">
                <div class="admin-card-body" style="padding: 2rem;">
                    <form id="companyDataForm">
                        <div class="form-grid">
                            <!-- ===== INFORMACIÓN BÁSICA ===== -->
                            <div class="form-section">
                                <h3 class="form-section-title">
                                    <i class="fas fa-info-circle"></i>
                                    Información Básica
                                </h3>

                                <div class="form-group">
                                    <label class="form-label" for="companyTitle">
                                        <i class="fas fa-heading"></i>
                                        Título de la Página
                                    </label>
                                    <input type="text"
                                           id="companyTitle"
                                           class="form-input"
                                           placeholder="Ej: TechStore - Tu tienda de tecnología"
                                           value="TechStore - Tecnología Premium"
                                           maxlength="60">
                                    <small class="form-help">Máximo 60 caracteres. Aparece en la pestaña del navegador.</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="companyDescription">
                                        <i class="fas fa-align-left"></i>
                                        Descripción de la Tienda
                                    </label>
                                    <textarea id="companyDescription"
                                              class="form-textarea"
                                              placeholder="Descripción breve de tu tienda..."
                                              rows="3"
                                              maxlength="250">Tienda especializada en productos Apple y tecnología premium. Ofrecemos los últimos dispositivos con garantía oficial y el mejor servicio al cliente.</textarea>
                                    <small class="form-help">Máximo 50 palabras. Descripción que aparece en la página principal.</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="companyAbout">
                                        <i class="fas fa-users"></i>
                                        Sobre Nosotros
                                    </label>
                                    <textarea id="companyAbout"
                                              class="form-textarea"
                                              placeholder="Cuéntanos sobre tu empresa..."
                                              rows="5"
                                              maxlength="500">Somos una empresa familiar con más de 10 años de experiencia en el sector tecnológico. Nos especializamos en productos Apple y dispositivos premium, ofreciendo siempre la mejor calidad y servicio personalizado. Nuestro compromiso es brindar a nuestros clientes la mejor experiencia de compra, con productos originales, garantía oficial y soporte técnico especializado. Creemos en la tecnología como herramienta para mejorar la vida de las personas.</textarea>
                                    <small class="form-help">Máximo 100 palabras. Historia y descripción detallada de tu negocio.</small>
                                </div>
                            </div>

                            <!-- ===== INFORMACIÓN DE CONTACTO ===== -->
                            <div class="form-section">
                                <h3 class="form-section-title">
                                    <i class="fas fa-address-book"></i>
                                    Información de Contacto
                                </h3>

                                <div class="form-group">
                                    <label class="form-label" for="companyAddress">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Dirección
                                    </label>
                                    <input type="text"
                                           id="companyAddress"
                                           class="form-input"
                                           placeholder="Ej: Av. Providencia 1234, Providencia, Santiago"
                                           value="Av. Providencia 1234, Local 56, Providencia, Santiago">
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label" for="companyPhone">
                                            <i class="fas fa-phone"></i>
                                            Teléfono
                                        </label>
                                        <input type="tel"
                                               id="companyPhone"
                                               class="form-input"
                                               placeholder="Ej: +56 2 2345 6789"
                                               value="+56 2 2345 6789">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label" for="companyWhatsapp">
                                            <i class="fab fa-whatsapp"></i>
                                            WhatsApp
                                        </label>
                                        <input type="tel"
                                               id="companyWhatsapp"
                                               class="form-input"
                                               placeholder="Ej: +56 9 8765 4321"
                                               value="+56 9 8765 4321">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="companyLocation">
                                        <i class="fas fa-globe"></i>
                                        Ubicación (Google Maps)
                                    </label>
                                    <input type="url"
                                           id="companyLocation"
                                           class="form-input"
                                           placeholder="Pega aquí el enlace de Google Maps"
                                           value="https://maps.google.com/embed?pb=!1m18!1m12!1m3!1d3329.8">
                                    <small class="form-help">Copia el enlace de inserción de Google Maps para mostrar tu ubicación.</small>
                                </div>

                                <!-- Vista previa del mapa -->
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-eye"></i>
                                        Vista Previa del Mapa
                                    </label>
                                    <div class="map-preview">
                                        <iframe id="mapPreview"
                                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3329.8!2d-70.6!3d-33.4!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzPCsDI0JzAwLjAiUyA3MMKwMzYnMDAuMCJX!5e0!3m2!1ses!2scl!4v1"
                                                width="100%"
                                                height="200"
                                                style="border:0; border-radius: 8px;"
                                                allowfullscreen=""
                                                loading="lazy">
                                        </iframe>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Botones de acción -->
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" id="resetCompanyData">
                                <i class="fas fa-undo"></i>
                                Restablecer
                            </button>
                            <button type="button" class="btn btn-primary" id="previewChanges">
                                <i class="fas fa-eye"></i>
                                Vista Previa
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Guardar Cambios
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- ===== SECCIÓN DE CONFIGURACIÓN ===== -->
        <div class="content-area" id="tienda">
            <div class="section-header">
                <h2>Configuración de Tienda</h2>
            </div>
            <div class="admin-card">
                <div class="admin-card-body">
                    <p>Sección de configuración de tienda en desarrollo...</p>
                </div>
            </div>
        </div>

    </main>

    <!-- ===== MODAL PARA AGREGAR/EDITAR PRODUCTO ===== -->
    <div class="modal-overlay" id="productModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-box"></i>
                    <span id="productModalTitle">Agregar Nuevo Producto</span>
                </h3>
                <button class="modal-close" id="closeProductModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Nombre del Producto</label>
                            <input type="text" class="form-input" id="productName" placeholder="Ej: iPhone 15 Pro Max" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Categoría</label>
                            <select class="form-select" id="productCategory" required>
                                <option value="">Seleccionar categoría</option>
                                <option value="electronica">Electrónica</option>
                                <option value="computacion">Computación</option>
                                <option value="accesorios">Accesorios</option>
                                <option value="audio">Audio</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Precio Actual</label>
                            <input type="number" class="form-input" id="productCurrentPrice" placeholder="999990" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Precio Original</label>
                            <input type="number" class="form-input" id="productOriginalPrice" placeholder="1199990">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Descripción</label>
                        <textarea class="form-textarea" id="productDescription" placeholder="Descripción del producto..." rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Imagen del Producto</label>
                        <div class="file-upload-area">
                            <input type="file" id="productImage" accept="image/*" style="display: none;">
                            <div class="file-upload-placeholder" onclick="document.getElementById('productImage').click();">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>Haz clic para subir una imagen</p>
                                <span>JPG, PNG, GIF hasta 5MB</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tipo de Producto</label>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" id="isDestacado" name="productType" value="destacado">
                                <span class="checkmark"></span>
                                Destacado
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isOferta" name="productType" value="oferta">
                                <span class="checkmark"></span>
                                En Oferta
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isNovedad" name="productType" value="novedad">
                                <span class="checkmark"></span>
                                Novedad
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isTendencia" name="productType" value="tendencia">
                                <span class="checkmark"></span>
                                Tendencia
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isLiquidacion" name="productType" value="liquidacion">
                                <span class="checkmark"></span>
                                Liquidación
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelProductBtn">Cancelar</button>
                <button type="submit" class="btn btn-primary" id="saveProductBtn">
                    <i class="fas fa-save"></i>
                    Guardar Producto
                </button>
            </div>
        </div>
    </div>

    <!-- ===== MODAL PARA AGREGAR PRODUCTO MEJORADO ===== -->
<div class="modal-overlay" id="addProductModal">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-plus-circle"></i>
                <span>Agregar Nuevo Producto</span>
            </h3>
            <button class="modal-close" id="closeAddProductModal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="addProductForm" enctype="multipart/form-data">
                <!-- Subir Imagen -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-image"></i>
                        Imagen del Producto *
                    </label>
                    <div class="file-upload-area" id="imageUploadArea">
                        <input type="file" id="productImageInput" accept=".jpg,.jpeg" style="display: none;" required>
                        <div class="file-upload-placeholder" id="uploadPlaceholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Haz clic para subir imagen</p>
                            <span>Solo archivos JPG (máximo 5MB)</span>
                        </div>
                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" src="" alt="Vista previa">
                            <button type="button" class="change-image-btn" id="changeImageBtn">
                                <i class="fas fa-edit"></i>
                                Cambiar imagen
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Nombre del Producto -->
                <div class="form-group">
                    <label class="form-label" for="productNameInput">
                        <i class="fas fa-tag"></i>
                        Nombre del Producto *
                    </label>
                    <input type="text" 
                           id="productNameInput" 
                           class="form-input" 
                           placeholder="Ej: iPhone 15 Pro Max 256GB"
                           maxlength="100"
                           required>
                    <div class="char-counter">
                        <span id="nameCharCount">0</span>/100 caracteres
                    </div>
                </div>

                <!-- Precios -->
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="productPriceInput">
                            <i class="fas fa-dollar-sign"></i>
                            Precio Actual *
                        </label>
                        <div class="price-input-container">
                            <span class="currency-symbol">$</span>
                            <input type="number" 
                                   id="productPriceInput" 
                                   class="form-input price-input" 
                                   placeholder="999990"
                                   min="0"
                                   step="10"
                                   required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="productOriginalPriceInput">
                            <i class="fas fa-percentage"></i>
                            Precio Antes (Opcional)
                        </label>
                        <div class="price-input-container">
                            <span class="currency-symbol">$</span>
                            <input type="number" 
                                   id="productOriginalPriceInput" 
                                   class="form-input price-input" 
                                   placeholder="1199990"
                                   min="0"
                                   step="10">
                        </div>
                    </div>
                </div>

                <!-- Categoría y Subcategoría -->
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="productCategorySelect">
                            <i class="fas fa-list"></i>
                            Categoría *
                        </label>
                        <select id="productCategorySelect" class="form-select" required>
                            <option value="">Seleccionar categoría</option>
                            <option value="smartphones">Smartphones</option>
                            <option value="computacion">Computación</option>
                            <option value="tablets">Tablets</option>
                            <option value="audio">Audio y Sonido</option>
                            <option value="wearables">Wearables</option>
                            <option value="accesorios">Accesorios</option>
                            <option value="gaming">Gaming</option>
                            <option value="hogar">Hogar Inteligente</option>
                            <option value="fotografia">Fotografía</option>
                            <option value="almacenamiento">Almacenamiento</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="productSubcategorySelect">
                            <i class="fas fa-sitemap"></i>
                            Subcategoría *
                        </label>
                        <select id="productSubcategorySelect" class="form-select" required disabled>
                            <option value="">Primero selecciona una categoría</option>
                        </select>
                    </div>
                </div>

                <!-- Pestaña de Destino -->
                <div class="form-group">
                    <label class="form-label" for="productTabSelect">
                        <i class="fas fa-folder"></i>
                        ¿En qué sección aparecerá? *
                    </label>
                    <select id="productTabSelect" class="form-select" required>
                        <option value="">Seleccionar sección</option>
                        <option value="destacados">Destacados</option>
                        <option value="ofertas">Ofertas</option>
                        <option value="novedades">Novedades</option>
                        <option value="mas-vistos">Más Vistos</option>
                        <option value="tendencias">Tendencias</option>
                        <option value="liquidaciones">Liquidaciones</option>
                    </select>
                </div>

                <!-- Descripción (Opcional) -->
                <div class="form-group">
                    <label class="form-label" for="productDescriptionInput">
                        <i class="fas fa-align-left"></i>
                        Descripción (Opcional)
                    </label>
                    <textarea id="productDescriptionInput" 
                              class="form-textarea" 
                              placeholder="Descripción detallada del producto..."
                              rows="3"
                              maxlength="500"></textarea>
                    <div class="char-counter">
                        <span id="descCharCount">0</span>/500 caracteres
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelAddProductBtn">
                <i class="fas fa-times"></i>
                Cancelar
            </button>
            <button type="button" class="btn btn-primary" id="saveAddProductBtn">
                <i class="fas fa-plus"></i>
                Agregar Producto
            </button>
        </div>
    </div>
</div>

    <!-- ===== JAVASCRIPT ===== -->
    <script>
        // ===== VARIABLES GLOBALES =====
        let currentSection = 'dashboard';
        let currentTab = 'destacados';

    // ===== PESTAÑAS DE GESTIÓN DE PRODUCTOS =====
    function initializeProductTabs() {
        const tabs = document.querySelectorAll('.admin-tab');
        const tabContents = document.querySelectorAll('.admin-tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');

                // Remover clase active de todas las pestañas
                tabs.forEach(t => t.classList.remove('active'));

                // Agregar clase active a la pestaña clickeada
                this.classList.add('active');

                // Ocultar todo el contenido de pestañas
                tabContents.forEach(content => {
                    content.classList.remove('active');
                    content.style.display = 'none';
                });

                // Mostrar el contenido de la pestaña seleccionada
                const targetContent = document.getElementById(targetTab);
                if (targetContent) {
                    targetContent.classList.add('active');
                    targetContent.style.display = 'block';
                }

                console.log('Pestaña cambiada a:', targetTab);
            });
        });

        // Asegurar que la primera pestaña esté activa al cargar
        const firstTab = document.querySelector('.admin-tab');
        const firstContent = document.querySelector('.admin-tab-content');

        if (firstTab && firstContent) {
            firstTab.classList.add('active');
            firstContent.classList.add('active');
            firstContent.style.display = 'block';
        }
    }

        // ===== INICIALIZACIÓN =====
        document.addEventListener('DOMContentLoaded', function() {
                // Inicializar pestañas de productos
            initializeProductTabs();
            initializeAdmin();
            initializeCompanyForm();
            initializeProductModal();
        });

        // ===== FUNCIÓN PRINCIPAL DE INICIALIZACIÓN =====
        function initializeAdmin() {
            // Navegación del sidebar
            initializeSidebarNavigation();

            // Menú móvil
            initializeMobileMenu();

            // Pestañas de productos
            initializeProductTabs();

            // Filtros de tiempo en KPIs
            initializeTimeFilters();

            // Botones de productos
            initializeProductButtons();

            // Atajos de teclado
            initializeKeyboardShortcuts();
        }

        // ===== NAVEGACIÓN DEL SIDEBAR =====
        function initializeSidebarNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const contentAreas = document.querySelectorAll('.content-area');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetSection = this.getAttribute('data-section');

                    // Remover clase active de todos los enlaces
                    navLinks.forEach(nav => nav.classList.remove('active'));

                    // Agregar clase active al enlace clickeado
                    this.classList.add('active');

                    if (targetSection === 'dashboard') {
                        // Mostrar todas las secciones
                        contentAreas.forEach(area => area.style.display = 'block');
                    } else {
                        // Ocultar todas las áreas de contenido
                        contentAreas.forEach(area => area.style.display = 'none');

                        // Mostrar solo el área de contenido correspondiente
                        const targetArea = document.getElementById(targetSection);
                        if (targetArea) {
                            targetArea.style.display = 'block';
                        }
                    }

                    // Actualizar título de página
                    updatePageTitle(targetSection);

                    // Actualizar sección actual
                    currentSection = targetSection;
                });
            });
        }

        // ===== FUNCIÓN DE NOTIFICACIONES =====
        function showNotification(message, type = 'info') {
            // Crear elemento de notificación
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            notification.innerHTML = `
                <div class="notification-content">
                    <span>${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Agregar al DOM
            document.body.appendChild(notification);

            // Auto-eliminar después de 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }


        // ===== ACTUALIZAR TÍTULO DE PÁGINA =====
        function updatePageTitle(section) {
            const pageTitle = document.querySelector('.page-title');
            const pageSubtitle = document.querySelector('.page-subtitle');

            const titles = {
                'dashboard': {
                    title: 'Dashboard',
                    subtitle: 'Resumen general de tu tienda'
                },


                'empresa': {
                    title: 'Datos de la Empresa',
                    subtitle: 'Información y configuración de tu negocio'
                },
                'tienda': {
                    title: 'Configuración',
                    subtitle: 'Ajustes generales de la tienda'
                },
            };

            if (titles[section]) {
                if (pageTitle) pageTitle.textContent = titles[section].title;
                if (pageSubtitle) pageSubtitle.textContent = titles[section].subtitle;
            }
        }

        // ===== MENÚ MÓVIL =====
        function initializeMobileMenu() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.querySelector('.admin-sidebar');

            if (mobileMenuToggle && sidebar) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('mobile-open');
                    this.classList.toggle('active');
                });

                // Cerrar menú al hacer clic fuera
                document.addEventListener('click', function(e) {
                    if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                        sidebar.classList.remove('mobile-open');
                        mobileMenuToggle.classList.remove('active');
                    }
                });
            }
        }

        // ===== PESTAÑAS DE PRODUCTOS =====
        function initializeProductTabs() {
            const tabs = document.querySelectorAll('.admin-tab');
            const tabContents = document.querySelectorAll('.admin-tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remover clase active de todas las pestañas
                    tabs.forEach(t => t.classList.remove('active'));

                    // Agregar clase active a la pestaña clickeada
                    this.classList.add('active');

                    // Ocultar todo el contenido de pestañas
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Mostrar el contenido de la pestaña correspondiente
                    const targetContent = document.getElementById(targetTab);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }

                    // Actualizar pestaña actual
                    currentTab = targetTab;
                });
            });
        }

        // ===== FILTROS DE TIEMPO EN KPIs =====
        function initializeTimeFilters() {
            const timeButtons = document.querySelectorAll('.time-btn');

            timeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const period = this.getAttribute('data-period');

                    // Remover clase active de todos los botones
                    timeButtons.forEach(b => b.classList.remove('active'));

                    // Agregar clase active al botón clickeado
                    this.classList.add('active');

                    // Simular actualización de datos
                    updateKPIData(period);
                });
            });
        }

        // ===== ACTUALIZAR DATOS DE KPI =====
        function updateKPIData(period) {
            // Simular datos diferentes según el período
            const kpiData = {
                'today': {
                    visits: '1,254',
                    mobile: '68%',
                    time: '3m 42s',
                    shared: '42'
                },
                'week': {
                    visits: '8,756',
                    mobile: '72%',
                    time: '4m 15s',
                    shared: '287'
                },
                'month': {
                    visits: '34,521',
                    mobile: '75%',
                    time: '4m 38s',
                    shared: '1,156'
                },
                'year': {
                    visits: '425,678',
                    mobile: '78%',
                    time: '5m 12s',
                    shared: '12,847'
                }
            };

            const data = kpiData[period];
            if (data) {
                // Actualizar valores con animación
                animateKPIUpdate('.visits .kpi-value', data.visits);
                animateKPIUpdate('.mobile .kpi-value', data.mobile);
                animateKPIUpdate('.time .kpi-value', data.time);
                animateKPIUpdate('.shared .kpi-value', data.shared);
            }
        }

        // ===== ANIMAR ACTUALIZACIÓN DE KPI =====
        function animateKPIUpdate(selector, newValue) {
            const element = document.querySelector(selector);
            if (element) {
                element.style.opacity = '0.5';
                setTimeout(() => {
                    element.textContent = newValue;
                    element.style.opacity = '1';
                }, 200);
            }
        }

        // ===== BOTONES DE PRODUCTOS =====
        function initializeProductButtons() {
            // Botones de agregar producto
            const addProductCards = document.querySelectorAll('.add-product-card');
            addProductCards.forEach(card => {
                card.addEventListener('click', function() {
                    openProductModal('add');
                });
            });

            // Botones de editar producto
            const editButtons = document.querySelectorAll('.edit-product');
            editButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    openProductModal('edit', this.closest('.product-card'));
                });
            });

            // Botones de eliminar producto
            const deleteButtons = document.querySelectorAll('.delete-product');
            deleteButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    deleteProduct(this.closest('.product-card'));
                });
            });

            // Toggles de productos
            const productToggles = document.querySelectorAll('.product-toggle input');
            productToggles.forEach(toggle => {
                toggle.addEventListener('change', function() {
                    toggleProductStatus(this.closest('.product-card'), this.checked);
                });
            });
        }

        // ===== MODAL DE PRODUCTOS =====
        function initializeProductModal() {
            const modal = document.getElementById('productModal');
            const closeBtn = document.getElementById('closeProductModal');
            const cancelBtn = document.getElementById('cancelProductBtn');
            const saveBtn = document.getElementById('saveProductBtn');
            const form = document.getElementById('productForm');

            // Cerrar modal
            [closeBtn, cancelBtn].forEach(btn => {
                if (btn) {
                    btn.addEventListener('click', function() {
                        closeProductModal();
                    });
                }
            });

            // Cerrar modal al hacer clic fuera
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeProductModal();
                    }
                });
            }

            // Guardar producto
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveProduct();
                });
            }

            // Preview de imagen
            const imageInput = document.getElementById('productImage');
            if (imageInput) {
                imageInput.addEventListener('change', function() {
                    previewProductImage(this);
                });
            }
        }

        // ===== ELIMINAR PRODUCTO =====
        function deleteProduct(productCard) {
            const productName = productCard.querySelector('.product-name')?.textContent || 'este producto';

            if (confirm(`¿Estás seguro de que quieres eliminar "${productName}"?`)) {
                // Animación de eliminación
                productCard.style.opacity = '0.5';
                productCard.style.transform = 'scale(0.95)';

                setTimeout(() => {
                    productCard.remove();
                    showNotification('Producto eliminado correctamente', 'success');
                }, 300);
            }
        }

        // ===== TOGGLE ESTADO DEL PRODUCTO =====
        function toggleProductStatus(productCard, isActive) {
            const badge = productCard.querySelector('.product-badge');
            if (badge) {
                if (isActive) {
                    badge.classList.add('badge-active');
                    badge.classList.remove('badge-inactive');
                } else {
                    badge.classList.remove('badge-active');
                    badge.classList.add('badge-inactive');
                }
            }

            showNotification(`Producto ${isActive ? 'activado' : 'desactivado'} correctamente`, 'info');
        }

        // ===== PREVIEW DE IMAGEN =====
        function previewProductImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    const placeholder = input.parentElement.querySelector('.file-upload-placeholder');
                    placeholder.innerHTML = `
                        <img src="${e.target.result}" style="max-width: 100%; max-height: 150px; border-radius: 8px;">
                        <p style="margin-top: 10px;">Imagen cargada correctamente</p>
                        <span>Haz clic para cambiar</span>
                    `;
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

  // ===== FORMULARIO DE EMPRESA =====
        function initializeCompanyForm() {
            // Referencias a elementos del formulario
            const companyTitle = document.getElementById('companyTitle');
            const companyDescription = document.getElementById('companyDescription');
            const companyAbout = document.getElementById('companyAbout');
            const companyAddress = document.getElementById('companyAddress');
            const companyPhone = document.getElementById('companyPhone');
            const companyWhatsapp = document.getElementById('companyWhatsapp');
            const companyLocation = document.getElementById('companyLocation');

            // Referencias a elementos de vista previa
            const previewTitle = document.getElementById('previewTitle');
            const previewDescription = document.getElementById('previewDescription');
            const previewAbout = document.getElementById('previewAbout');
            const previewAddress = document.getElementById('previewAddress');
            const previewPhone = document.getElementById('previewPhone');
            const previewWhatsapp = document.getElementById('previewWhatsapp');
            const mapPreview = document.getElementById('mapPreview');

            // Función para actualizar vista previa en tiempo real
            function updatePreview() {
                if (companyTitle && previewTitle) {
                    previewTitle.textContent = companyTitle.value || 'Título de la tienda';
                }
                if (companyDescription && previewDescription) {
                    previewDescription.textContent = companyDescription.value || 'Descripción de la tienda';
                }
                if (companyAbout && previewAbout) {
                    previewAbout.textContent = companyAbout.value || 'Información sobre la empresa';
                }
                if (companyAddress && previewAddress) {
                    previewAddress.textContent = companyAddress.value || 'Dirección de la empresa';
                }
                if (companyPhone && previewPhone) {
                    previewPhone.textContent = companyPhone.value || 'Teléfono';
                }
                if (companyWhatsapp && previewWhatsapp) {
                    previewWhatsapp.textContent = companyWhatsapp.value || 'WhatsApp';
                }
                if (companyLocation && mapPreview) {
                    if (companyLocation.value && companyLocation.value.includes('maps.google.com')) {
                        mapPreview.src = companyLocation.value;
                    }
                }
            }

            // Agregar event listeners para actualización en tiempo real
            [companyTitle, companyDescription, companyAbout, companyAddress, companyPhone, companyWhatsapp].forEach(element => {
                if (element) {
                    element.addEventListener('input', updatePreview);
                }
            });

            if (companyLocation) {
                companyLocation.addEventListener('change', updatePreview);
            }

            // Manejar envío del formulario
            const companyForm = document.getElementById('companyDataForm');
            if (companyForm) {
                companyForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Simular guardado
                    const saveBtn = document.getElementById('saveCompanyData');
                    if (saveBtn) {
                        saveBtn.classList.add('loading');
                        saveBtn.disabled = true;
                    }

                    setTimeout(() => {
                        showNotification('Datos de la empresa guardados correctamente', 'success');
                        if (saveBtn) {
                            saveBtn.classList.remove('loading');
                            saveBtn.disabled = false;
                        }
                    }, 2000);
                });
            }

            // Botón de restablecer
            const resetBtn = document.getElementById('resetCompanyData');
            if (resetBtn) {
                resetBtn.addEventListener('click', function() {
                    if (confirm('¿Estás seguro de que quieres restablecer todos los datos?')) {
                        companyForm.reset();
                        updatePreview();
                        showNotification('Datos restablecidos', 'info');
                    }
                });
            }

            // Botón de vista previa
            const previewBtn = document.getElementById('previewChanges');
            if (previewBtn) {
                previewBtn.addEventListener('click', function() {
                    updatePreview();
                    showNotification('Vista previa actualizada', 'info');

                    // Scroll suave hacia la vista previa
                    const previewContainer = document.querySelector('.preview-container');
                    if (previewContainer) {
                        previewContainer.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            }

            // Inicializar vista previa
            updatePreview();
        }

        // ===== FUNCIÓN PARA MOSTRAR NOTIFICACIONES =====
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span>${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remover después de 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // ===== FUNCIONES DE UTILIDAD =====

        // Formatear números con separadores de miles
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        }

        // Formatear precios
        function formatPrice(price) {
            return `$${formatNumber(price)}`;
        }

        // Validar email
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        // Validar teléfono
        function validatePhone(phone) {
            const re = /^[\+]?[0-9\s\-\(\)]+$/;
            return re.test(phone);
        }

        // Debounce para optimizar búsquedas
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // ===== BÚSQUEDA EN TIEMPO REAL =====
        function initializeSearch() {
            const searchInput = document.querySelector('.search-box input');
            if (searchInput) {
                const debouncedSearch = debounce(performSearch, 300);
                searchInput.addEventListener('input', function() {
                    debouncedSearch(this.value);
                });
            }
        }

        function performSearch(query) {
            if (query.length < 2) return;

            // Simular búsqueda
            console.log('Buscando:', query);
            // Aquí implementarías la lógica de búsqueda real
        }

        // ===== MANEJO DE ERRORES GLOBALES =====
        window.addEventListener('error', function(e) {
            console.error('Error capturado:', e.error);
            showNotification('Ha ocurrido un error inesperado', 'error');
        });

        // ===== FUNCIONES DE EXPORTACIÓN =====
        function exportData(format) {
            showNotification(`Exportando datos en formato ${format}...`, 'info');

            // Simular exportación
            setTimeout(() => {
                showNotification(`Datos exportados correctamente en ${format}`, 'success');
            }, 2000);
        }

        // ===== FUNCIONES DE BACKUP =====
        function createBackup() {
            showNotification('Creando respaldo de datos...', 'info');

            // Simular backup
            setTimeout(() => {
                showNotification('Respaldo creado correctamente', 'success');
            }, 3000);
        }

        // ===== CONFIGURACIÓN DE TEMA =====
        function toggleTheme() {
            const body = document.body;
            const isDark = body.classList.contains('dark-theme');

            if (isDark) {
                body.classList.remove('dark-theme');
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark-theme');
                localStorage.setItem('theme', 'dark');
            }
        }

        // Cargar tema guardado
        function loadSavedTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
            }
        }

        // ===== INICIALIZACIÓN FINAL =====
        document.addEventListener('DOMContentLoaded', function() {
            // Cargar tema guardado
            loadSavedTheme();

            // Inicializar búsqueda
            initializeSearch();

            // Configurar tooltips
            initializeTooltips();

            // Configurar atajos de teclado
            initializeKeyboardShortcuts();
        });

        // ===== TOOLTIPS =====
        function initializeTooltips() {
            const tooltipElements = document.querySelectorAll('[data-tooltip]');
            tooltipElements.forEach(element => {
                element.addEventListener('mouseenter', showTooltip);
                element.addEventListener('mouseleave', hideTooltip);
            });
        }

        function showTooltip(e) {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip-popup';
            tooltip.textContent = e.target.getAttribute('data-tooltip');
            document.body.appendChild(tooltip);

            const rect = e.target.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        }

        function hideTooltip() {
            const tooltip = document.querySelector('.tooltip-popup');
            if (tooltip) {
                tooltip.remove();
            }
        }

        // ===== ATAJOS DE TECLADO =====
        function initializeKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + S para guardar
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    if (currentSection === 'empresa') {
                        document.getElementById('companyDataForm').dispatchEvent(new Event('submit'));
                    }
                }

                // Escape para cerrar modales
                if (e.key === 'Escape') {
                    closeProductModal();
                }

                // Ctrl/Cmd + N para nuevo producto
                if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                    e.preventDefault();
                    if (currentSection === 'dashboard') {
                        openProductModal('add');
                    }
                }
            });
        }

        // ===== FUNCIONES DE VALIDACIÓN =====
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                    field.classList.add('success');
                }
            });

            return isValid;
        }

        // ===== ANIMACIONES Y EFECTOS =====
        function animateElement(element, animation) {
            element.style.animation = animation;
            element.addEventListener('animationend', function() {
                element.style.animation = '';
            }, { once: true });
        }

        // ===== GESTIÓN DE ESTADO =====
        const AppState = {
            currentUser: 'Administrador',
            currentSection: 'dashboard',
            currentTab: 'destacados',
            unsavedChanges: false,

            setState(newState) {
                Object.assign(this, newState);
                this.saveToLocalStorage();
            },

            saveToLocalStorage() {
                localStorage.setItem('adminState', JSON.stringify(this));
            },

            loadFromLocalStorage() {
                const saved = localStorage.getItem('adminState');
                if (saved) {
                    Object.assign(this, JSON.parse(saved));
                }
            }
        };

        // Cargar estado al iniciar
        AppState.loadFromLocalStorage();

        // ===== MANEJO DE CONEXIÓN =====
        function checkConnection() {
            if (navigator.onLine) {
                showNotification('Conexión restaurada', 'success');
            } else {
                showNotification('Sin conexión a internet', 'warning');
            }
        }

        window.addEventListener('online', checkConnection);
        window.addEventListener('offline', checkConnection);

        // ===== FUNCIONES DE PERFORMANCE =====
        function optimizeImages() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (img.loading !== 'lazy') {
                    img.loading = 'lazy';
                }
            });
        }

        // Ejecutar optimizaciones
        setTimeout(optimizeImages, 1000);

        // ===== LOG DE ACTIVIDADES =====
        const ActivityLogger = {
            logs: [],

            log(action, details = '') {
                const logEntry = {
                    timestamp: new Date().toISOString(),
                    action: action,
                    details: details,
                    user: AppState.currentUser
                };

                this.logs.push(logEntry);
                console.log('Activity:', logEntry);

                // Mantener solo los últimos 100 logs
                if (this.logs.length > 100) {
                    this.logs = this.logs.slice(-100);
                }
            },

            exportLogs() {
                const dataStr = JSON.stringify(this.logs, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = 'activity-logs.json';
                link.click();

                URL.revokeObjectURL(url);
            }
        };



        // ===== ABRIR MODAL DE PRODUCTO =====
        function openProductModal(mode, productCard = null) {
            const modal = document.getElementById('productModal');
            const modalTitle = document.getElementById('productModalTitle');
            const form = document.getElementById('productForm');

            if (modal) {
                modal.style.display = 'flex';
                document.body.style.overflow = 'hidden';

                if (mode === 'add') {
                    modalTitle.textContent = 'Agregar Nuevo Producto';
                    form.reset();
                } else if (mode === 'edit' && productCard) {
                    modalTitle.textContent = 'Editar Producto';
                    populateFormWithProductData(productCard);
                }
            }
        }

        // ===== CERRAR MODAL DE PRODUCTO =====
        function closeProductModal() {
            const modal = document.getElementById('productModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // ===== POBLAR FORMULARIO CON DATOS DEL PRODUCTO =====
        function populateFormWithProductData(productCard) {
            const name = productCard.querySelector('.product-name')?.textContent || '';
            const category = productCard.querySelector('.product-category')?.textContent || '';
            const currentPrice = productCard.querySelector('.current-price')?.textContent.replace(/[^\d]/g, '') || '';
            const originalPrice = productCard.querySelector('.original-price')?.textContent.replace(/[^\d]/g, '') || '';

            document.getElementById('productName').value = name;
            document.getElementById('productCurrentPrice').value = currentPrice;
            document.getElementById('productOriginalPrice').value = originalPrice;

            // Determinar categoría
            if (category.includes('Electrónica')) {
                document.getElementById('productCategory').value = 'electronica';
            } else if (category.includes('Computación')) {
                document.getElementById('productCategory').value = 'computacion';
            } else if (category.includes('Accesorios')) {
                document.getElementById('productCategory').value = 'accesorios';
            } else if (category.includes('Audio')) {
                document.getElementById('productCategory').value = 'audio';
            }

            // Determinar tipo de producto
            const badge = productCard.querySelector('.product-badge')?.textContent || '';
            resetProductTypeCheckboxes();

            if (badge.includes('Destacado')) {
                document.getElementById('isDestacado').checked = true;
            } else if (badge.includes('Oferta')) {
                document.getElementById('isOferta').checked = true;
            } else if (badge.includes('Novedad')) {
                document.getElementById('isNovedad').checked = true;
            } else if (badge.includes('Tendencia')) {
                document.getElementById('isTendencia').checked = true;
            } else if (badge.includes('Liquidación')) {
                document.getElementById('isLiquidacion').checked = true;
            }
        }

        // ===== RESETEAR CHECKBOXES DE TIPO DE PRODUCTO =====
        function resetProductTypeCheckboxes() {
            const checkboxes = document.querySelectorAll('input[name="productType"]');
            checkboxes.forEach(cb => cb.checked = false);
        }

        // ===== GUARDAR PRODUCTO =====
        function saveProduct() {
            const formData = new FormData(document.getElementById('productForm'));
            const productData = Object.fromEntries(formData.entries());

            // Validar datos
            if (!productData.productName || !productData.productCategory || !productData.productCurrentPrice) {
                showNotification('Por favor completa todos los campos obligatorios', 'error');
                return;
            }

            // Simular guardado
            const saveBtn = document.getElementById('saveProductBtn');
            saveBtn.classList.add('loading');
            saveBtn.disabled = true;

            setTimeout(() => {
                showNotification('Producto guardado correctamente', 'success');
                closeProductModal();
                saveBtn.classList.remove('loading');
                saveBtn.disabled = false;

                // Aquí podrías actualizar la lista de productos
                // updateProductList(productData);
            }, 2000);
        }

        // ===== FINALIZACIÓN DEL SCRIPT =====
        console.log('Admin Panel inicializado correctamente');
        ActivityLogger.log('SYSTEM_START', 'Admin panel loaded successfully');
    </script>

<script>
    // ===== DATOS DE SUBCATEGORÍAS =====
const subcategoriesData = {
    smartphones: [
        'iPhone', 'Samsung Galaxy', 'Google Pixel', 'Xiaomi', 'OnePlus', 'Huawei', 'Otros Android'
    ],
    computacion: [
        'MacBook', 'Laptops Windows', 'Desktops', 'All-in-One', 'Mini PC', 'Workstations', 'Servidores'
    ],
    tablets: [
        'iPad', 'Samsung Galaxy Tab', 'Microsoft Surface', 'Tablets Android', 'E-readers'
    ],
    audio: [
        'AirPods', 'Auriculares Bluetooth', 'Auriculares Gaming', 'Parlantes', 'Soundbars', 'Equipos de Audio'
    ],
    wearables: [
        'Apple Watch', 'Samsung Galaxy Watch', 'Fitness Trackers', 'Smartwatches Android', 'Bandas Deportivas'
    ],
    accesorios: [
        'Cargadores', 'Cables', 'Fundas y Protectores', 'Soportes', 'Adaptadores', 'Power Banks'
    ],
    gaming: [
        'Consolas', 'Controles', 'Headsets Gaming', 'Teclados Gaming', 'Mouse Gaming', 'Monitores Gaming'
    ],
    hogar: [
        'Asistentes Virtuales', 'Cámaras de Seguridad', 'Dispositivos IoT', 'Iluminación Inteligente'
    ],
    fotografia: [
        'Cámaras Digitales', 'Lentes', 'Trípodes', 'Iluminación', 'Accesorios de Fotografía'
    ],
    almacenamiento: [
        'Discos Duros Externos', 'SSD', 'Memorias USB', 'Tarjetas de Memoria', 'NAS'
    ]
};

// ===== VARIABLES GLOBALES =====
let currentSection = 'dashboard';
let currentTab = 'destacados';

// ===== INICIALIZACIÓN =====
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
    initializeCompanyForm();
    initializeAddProductModal();
});

// ===== FUNCIÓN PRINCIPAL DE INICIALIZACIÓN =====
function initializeAdmin() {
    initializeSidebarNavigation();
    initializeMobileMenu();
    initializeProductTabs();
    initializeTimeFilters();
    initializeProductButtons();
    initializeKeyboardShortcuts();
}

// ===== INICIALIZAR MODAL DE AGREGAR PRODUCTO =====
function initializeAddProductModal() {
    const modal = document.getElementById('addProductModal');
    const closeBtn = document.getElementById('closeAddProductModal');
    const cancelBtn = document.getElementById('cancelAddProductBtn');
    const saveBtn = document.getElementById('saveAddProductBtn');
    const form = document.getElementById('addProductForm');
    
    // Event listeners para cerrar modal
    [closeBtn, cancelBtn].forEach(btn => {
        if (btn) {
            btn.addEventListener('click', closeAddProductModal);
        }
    });

    // Cerrar modal al hacer clic fuera
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddProductModal();
            }
        });
    }

    // Guardar producto
    if (saveBtn) {
        saveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            saveNewProduct();
        });
    }

    // Inicializar funcionalidades del formulario
    initializeImageUpload();
    initializeCategoryDependency();
    initializeCharCounters();
    initializeFormValidation();
}

// ===== INICIALIZAR SUBIDA DE IMAGEN =====
function initializeImageUpload() {
    const imageInput = document.getElementById('productImageInput');
    const uploadArea = document.getElementById('imageUploadArea');
    const placeholder = document.getElementById('uploadPlaceholder');
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const changeBtn = document.getElementById('changeImageBtn');

    // Click en área de subida
    uploadArea.addEventListener('click', function() {
        imageInput.click();
    });

    // Cambio de archivo
    imageInput.addEventListener('change', function(e) {
        handleImageUpload(e.target.files[0]);
    });

    // Botón cambiar imagen
    if (changeBtn) {
        changeBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            imageInput.click();
        });
    }

    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageUpload(files[0]);
        }
    });
}

// ===== MANEJAR SUBIDA DE IMAGEN =====
function handleImageUpload(file) {
    const placeholder = document.getElementById('uploadPlaceholder');
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    // Validar archivo
    if (!file) return;

    if (!file.type.match('image/jpeg')) {
        showNotification('Solo se permiten archivos JPG', 'error');
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
        showNotification('El archivo es demasiado grande. Máximo 5MB', 'error');
        return;
    }

    // Mostrar preview
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImg.src = e.target.result;
        placeholder.style.display = 'none';
        preview.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

// ===== INICIALIZAR DEPENDENCIA DE CATEGORÍAS =====
function initializeCategoryDependency() {
    const categorySelect = document.getElementById('productCategorySelect');
    const subcategorySelect = document.getElementById('productSubcategorySelect');

    if (categorySelect && subcategorySelect) {
        categorySelect.addEventListener('change', function() {
            const selectedCategory = this.value;
            updateSubcategories(selectedCategory, subcategorySelect);
        });
    }
}

// ===== ACTUALIZAR SUBCATEGORÍAS =====
function updateSubcategories(category, subcategorySelect) {
    // Limpiar opciones existentes
    subcategorySelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
    
    if (category && subcategoriesData[category]) {
        // Habilitar select
        subcategorySelect.disabled = false;
        
        // Agregar nuevas opciones
        subcategoriesData[category].forEach(subcategory => {
            const option = document.createElement('option');
            option.value = subcategory.toLowerCase().replace(/\s+/g, '-');
            option.textContent = subcategory;
            subcategorySelect.appendChild(option);
        });
    } else {
        // Deshabilitar select
        subcategorySelect.disabled = true;
        subcategorySelect.innerHTML = '<option value="">Primero selecciona una categoría</option>';
    }
}

// ===== INICIALIZAR CONTADORES DE CARACTERES =====
function initializeCharCounters() {
    const nameInput = document.getElementById('productNameInput');
    const descInput = document.getElementById('productDescriptionInput');
    const nameCounter = document.getElementById('nameCharCount');
    const descCounter = document.getElementById('descCharCount');

    if (nameInput && nameCounter) {
        nameInput.addEventListener('input', function() {
            updateCharCounter(this, nameCounter, 100);
        });
    }

    if (descInput && descCounter) {
        descInput.addEventListener('input', function() {
            updateCharCounter(this, descCounter, 500);
        });
    }
}

// ===== ACTUALIZAR CONTADOR DE CARACTERES =====
function updateCharCounter(input, counter, maxLength) {
    const currentLength = input.value.length;
    counter.textContent = currentLength;
    
    // Cambiar color según proximidad al límite
    counter.parentElement.classList.remove('warning', 'danger');
    
    if (currentLength > maxLength * 0.9) {
        counter.parentElement.classList.add('danger');
    } else if (currentLength > maxLength * 0.7) {
        counter.parentElement.classList.add('warning');
    }
}

// ===== INICIALIZAR VALIDACIÓN DE FORMULARIO =====
function initializeFormValidation() {
    const form = document.getElementById('addProductForm');
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');

    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('invalid')) {
                validateField(this);
            }
        });
    });
}

// ===== VALIDAR CAMPO =====
function validateField(field) {
    const isValid = field.checkValidity() && field.value.trim() !== '';
    
    field.classList.remove('valid', 'invalid');
    field.classList.add(isValid ? 'valid' : 'invalid');
    
    return isValid;
}

// ===== ABRIR MODAL DE AGREGAR PRODUCTO =====
function openAddProductModal() {
    const modal = document.getElementById('addProductModal');
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Focus en primer campo
        setTimeout(() => {
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) firstInput.focus();
        }, 300);
    }
}

// ===== CERRAR MODAL DE AGREGAR PRODUCTO =====
function closeAddProductModal() {
    const modal = document.getElementById('addProductModal');
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = 'auto';
        
        // Limpiar formulario después de la animación
        setTimeout(() => {
            resetAddProductForm();
        }, 300);
    }
}

// ===== RESETEAR FORMULARIO =====
function resetAddProductForm() {
    const form = document.getElementById('addProductForm');
    const placeholder = document.getElementById('uploadPlaceholder');
    const preview = document.getElementById('imagePreview');
    const subcategorySelect = document.getElementById('productSubcategorySelect');
    
    if (form) {
        form.reset();
        
        // Resetear vista de imagen
        if (placeholder && preview) {
            placeholder.style.display = 'flex';
            preview.style.display = 'none';
        }
        
        // Resetear subcategorías
        if (subcategorySelect) {
            subcategorySelect.disabled = true;
            subcategorySelect.innerHTML = '<option value="">Primero selecciona una categoría</option>';
        }
        
        // Limpiar clases de validación
        const fields = form.querySelectorAll('.valid, .invalid');
        fields.forEach(field => {
            field.classList.remove('valid', 'invalid');
        });
        
        // Resetear contadores
        document.getElementById('nameCharCount').textContent = '0';
        document.getElementById('descCharCount').textContent = '0';
    }
}

// ===== GUARDAR NUEVO PRODUCTO =====
function saveNewProduct() {
    const form = document.getElementById('addProductForm');
    const saveBtn = document.getElementById('saveAddProductBtn');
    
    // Validar formulario
    if (!validateAddProductForm()) {
        showNotification('Por favor completa todos los campos obligatorios correctamente', 'error');
        return;
    }
    
    // Obtener datos del formulario
    const formData = getAddProductFormData();
    
    // Mostrar estado de carga
    saveBtn.classList.add('loading');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
    
    // Simular guardado (aquí conectarías con tu backend)
    setTimeout(() => {
        // Crear y agregar el producto a la pestaña correspondiente
        addProductToTab(formData);
        
        // Mostrar notificación de éxito
        showNotification(`Producto "${formData.name}" agregado correctamente a ${formData.tab}`, 'success');
        
        // Cerrar modal
        closeAddProductModal();
        
        // Restaurar botón
        saveBtn.classList.remove('loading');
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-plus"></i> Agregar Producto';
        
        // Cambiar a la pestaña donde se agregó el producto
        switchToTab(formData.tab);
        
    }, 2000);
}

// ===== VALIDAR FORMULARIO COMPLETO =====
function validateAddProductForm() {
    const form = document.getElementById('addProductForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Validar que se haya subido una imagen
    const imageInput = document.getElementById('productImageInput');
    if (!imageInput.files || imageInput.files.length === 0) {
        showNotification('Debes subir una imagen del producto', 'error');
        isValid = false;
    }
    
    // Validar que el precio antes sea mayor al precio actual (si se especifica)
    const currentPrice = parseFloat(document.getElementById('productPriceInput').value);
    const originalPrice = parseFloat(document.getElementById('productOriginalPriceInput').value);
    
    if (originalPrice && originalPrice <= currentPrice) {
        showNotification('El precio antes debe ser mayor al precio actual', 'error');
        isValid = false;
    }
    
    return isValid;
}

// ===== OBTENER DATOS DEL FORMULARIO =====
function getAddProductFormData() {
    return {
        name: document.getElementById('productNameInput').value.trim(),
        price: parseFloat(document.getElementById('productPriceInput').value),
        originalPrice: parseFloat(document.getElementById('productOriginalPriceInput').value) || null,
        category: document.getElementById('productCategorySelect').value,
        subcategory: document.getElementById('productSubcategorySelect').value,
        tab: document.getElementById('productTabSelect').value,
        description: document.getElementById('productDescriptionInput').value.trim(),
        image: document.getElementById('productImageInput').files[0],
        imageUrl: document.getElementById('previewImg').src
    };
}

// ===== AGREGAR PRODUCTO A PESTAÑA =====
function addProductToTab(productData) {
    const tabContent = document.getElementById(productData.tab);
    const productsGrid = tabContent.querySelector('.products-grid');
    
    if (productsGrid) {
        const productCard = createProductCard(productData);
        
        // Insertar antes de la tarjeta "Agregar producto"
        const addCard = productsGrid.querySelector('.add-product-card');
        if (addCard) {
            productsGrid.insertBefore(productCard, addCard.nextSibling);
        } else {
            productsGrid.appendChild(productCard);
        }
        
        // Animar la aparición
        setTimeout(() => {
            productCard.style.opacity = '1';
            productCard.style.transform = 'translateY(0)';
        }, 100);
    }
}

// ===== CREAR TARJETA DE PRODUCTO =====
function createProductCard(data) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'all 0.3s ease';
    
    const tabLabels = {
        'destacados': 'Destacado',
        'ofertas': 'Oferta',
        'novedades': 'Novedad',
        'mas-vistos': 'Más Visto',
        'tendencias': 'Tendencia',
        'liquidaciones': 'Liquidación'
    };
    
    const categoryLabels = {
        'smartphones': 'Electrónica / Smartphones',
        'computacion': 'Computación / Laptops',
        'tablets': 'Electrónica / Tablets',
        'audio': 'Electrónica / Audio',
        'wearables': 'Electrónica / Wearables',
        'accesorios': 'Accesorios',
        'gaming': 'Gaming',
        'hogar': 'Hogar Inteligente',
        'fotografia': 'Fotografía',
        'almacenamiento': 'Almacenamiento'
    };
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${data.imageUrl}" alt="${data.name}">
            <div class="product-overlay">
                <button class="product-overlay-btn edit-product">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="product-overlay-btn delete-product">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <div class="product-details">
            <div class="product-category">${categoryLabels[data.category] || data.category}</div>
            <h3 class="product-name">${data.name}</h3>
            <div class="product-price">
                <span class="current-price">$${formatNumber(data.price)}</span>
                ${data.originalPrice ? `<span class="original-price">$${formatNumber(data.originalPrice)}</span>` : ''}
            </div>
            <div class="product-status">
                <span class="product-badge badge-active">${tabLabels[data.tab]}</span>
                <label class="product-toggle">
                    <input type="checkbox" checked>
                    <span class="product-toggle-slider"></span>
                </label>
            </div>
        </div>
    `;
    
    // Agregar event listeners a los botones
    const editBtn = card.querySelector('.edit-product');
    const deleteBtn = card.querySelector('.delete-product');
    const toggle = card.querySelector('.product-toggle input');
    
    editBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        editProduct(card);
    });
    
    deleteBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        deleteProduct(card);
    });
    
    toggle.addEventListener('change', function() {
        toggleProductStatus(card, this.checked);
    });
    
    return card;
}

// ===== CAMBIAR A PESTAÑA ESPECÍFICA =====
function switchToTab(tabName) {
    const tab = document.querySelector(`[data-tab="${tabName}"]`);
    if (tab) {
        tab.click();
    }
}

// ===== NAVEGACIÓN DEL SIDEBAR =====
function initializeSidebarNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const contentAreas = document.querySelectorAll('.content-area');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSection = this.getAttribute('data-section');

            // Remover clase active de todos los enlaces
            navLinks.forEach(nav => nav.classList.remove('active'));

            // Agregar clase active al enlace clickeado
            this.classList.add('active');

            if (targetSection === 'dashboard') {
                // Mostrar todas las secciones
                contentAreas.forEach(area => area.style.display = 'block');
            } else {
                // Ocultar todas las áreas de contenido
                contentAreas.forEach(area => area.style.display = 'none');

                // Mostrar solo el área de contenido correspondiente
                const targetArea = document.getElementById(targetSection);
                if (targetArea) {
                    targetArea.style.display = 'block';
                }
            }

            // Actualizar título de página
            updatePageTitle(targetSection);

            // Actualizar sección actual
            currentSection = targetSection;
        });
    });
}

// ===== ACTUALIZAR TÍTULO DE PÁGINA =====
function updatePageTitle(section) {
    const pageTitle = document.querySelector('.page-title');
    const pageSubtitle = document.querySelector('.page-subtitle');

    const titles = {
        'dashboard': {
            title: 'Dashboard',
            subtitle: 'Resumen general de tu tienda'
        },
        'empresa': {
            title: 'Datos de la Empresa',
            subtitle: 'Información y configuración de tu negocio'
        },
        'tienda': {
            title: 'Configuración',
            subtitle: 'Ajustes generales de la tienda'
        },
    };

    if (titles[section]) {
        if (pageTitle) pageTitle.textContent = titles[section].title;
        if (pageSubtitle) pageSubtitle.textContent = titles[section].subtitle;
    }
}

// ===== MENÚ MÓVIL =====
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.querySelector('.admin-sidebar');

    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');
            this.classList.toggle('active');
        });

        // Cerrar menú al hacer clic fuera
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                sidebar.classList.remove('mobile-open');
                mobileMenuToggle.classList.remove('active');
            }
        });
    }
}

// ===== PESTAÑAS DE PRODUCTOS =====
function initializeProductTabs() {
    const tabs = document.querySelectorAll('.admin-tab');
    const tabContents = document.querySelectorAll('.admin-tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remover clase active de todas las pestañas
            tabs.forEach(t => t.classList.remove('active'));

            // Agregar clase active a la pestaña clickeada
            this.classList.add('active');

            // Ocultar todo el contenido de pestañas
            tabContents.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // Mostrar el contenido de la pestaña seleccionada
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.display = 'block';
            }

            // Actualizar pestaña actual
            currentTab = targetTab;
        });
    });

    // Asegurar que la primera pestaña esté activa al cargar
    const firstTab = document.querySelector('.admin-tab');
    const firstContent = document.querySelector('.admin-tab-content');

    if (firstTab && firstContent) {
        firstTab.classList.add('active');
        firstContent.classList.add('active');
        firstContent.style.display = 'block';
    }
}

// ===== FILTROS DE TIEMPO EN KPIs =====
function initializeTimeFilters() {
    const timeButtons = document.querySelectorAll('.time-btn');

    timeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.getAttribute('data-period');

            // Remover clase active de todos los botones
            timeButtons.forEach(b => b.classList.remove('active'));

            // Agregar clase active al botón clickeado
            this.classList.add('active');

            // Simular actualización de datos
            updateKPIData(period);
        });
    });
}

// ===== ACTUALIZAR DATOS DE KPI =====
function updateKPIData(period) {
    const kpiData = {
        'today': {
            visits: '1,254',
            mobile: '68%',
            time: '3m 42s',
            shared: '42'
        },
        'week': {
            visits: '8,756',
            mobile: '72%',
            time: '4m 15s',
            shared: '287'
        },
        'month': {
            visits: '34,521',
            mobile: '75%',
            time: '4m 38s',
            shared: '1,156'
        },
        'year': {
            visits: '425,678',
            mobile: '78%',
            time: '5m 12s',
            shared: '12,847'
        }
    };

    const data = kpiData[period];
    if (data) {
        animateKPIUpdate('.visits .kpi-value', data.visits);
        animateKPIUpdate('.mobile .kpi-value', data.mobile);
        animateKPIUpdate('.time .kpi-value', data.time);
        animateKPIUpdate('.shared .kpi-value', data.shared);
    }
}

// ===== ANIMAR ACTUALIZACIÓN DE KPI =====
function animateKPIUpdate(selector, newValue) {
    const element = document.querySelector(selector);
    if (element) {
        element.style.opacity = '0.5';
        setTimeout(() => {
            element.textContent = newValue;
            element.style.opacity = '1';
        }, 200);
    }
}

// ===== BOTONES DE PRODUCTOS =====
function initializeProductButtons() {
    // Botones de agregar producto
    const addProductCards = document.querySelectorAll('.add-product-card');
    addProductCards.forEach(card => {
        card.addEventListener('click', function() {
            openAddProductModal();
        });
    });

    // Botones de editar producto
    const editButtons = document.querySelectorAll('.edit-product');
    editButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            editProduct(this.closest('.product-card'));
        });
    });

    // Botones de eliminar producto
    const deleteButtons = document.querySelectorAll('.delete-product');
    deleteButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            deleteProduct(this.closest('.product-card'));
        });
    });

    // Toggles de productos
    const productToggles = document.querySelectorAll('.product-toggle input');
    productToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            toggleProductStatus(this.closest('.product-card'), this.checked);
        });
    });
}

// ===== EDITAR PRODUCTO =====
function editProduct(productCard) {
    showNotification('Función de editar producto en desarrollo', 'info');
    // Aquí implementarías la lógica para editar el producto
}

// ===== ELIMINAR PRODUCTO =====
function deleteProduct(productCard) {
    const productName = productCard.querySelector('.product-name')?.textContent || 'este producto';

    if (confirm(`¿Estás seguro de que quieres eliminar "${productName}"?`)) {
        // Animación de eliminación
        productCard.style.opacity = '0.5';
        productCard.style.transform = 'scale(0.95)';

        setTimeout(() => {
            productCard.remove();
            showNotification('Producto eliminado correctamente', 'success');
        }, 300);
    }
}

// ===== TOGGLE ESTADO DEL PRODUCTO =====
function toggleProductStatus(productCard, isActive) {
    const badge = productCard.querySelector('.product-badge');
    if (badge) {
        if (isActive) {
            badge.classList.add('badge-active');
            badge.classList.remove('badge-inactive');
        } else {
            badge.classList.remove('badge-active');
            badge.classList.add('badge-inactive');
        }
    }

    showNotification(`Producto ${isActive ? 'activado' : 'desactivado'} correctamente`, 'info');
}

// ===== FORMULARIO DE EMPRESA =====
function initializeCompanyForm() {
    const companyTitle = document.getElementById('companyTitle');
    const companyDescription = document.getElementById('companyDescription');
    const companyAbout = document.getElementById('companyAbout');
    const companyAddress = document.getElementById('companyAddress');
    const companyPhone = document.getElementById('companyPhone');
    const companyWhatsapp = document.getElementById('companyWhatsapp');
    const companyLocation = document.getElementById('companyLocation');

    function updatePreview() {
        // Función de vista previa (implementar según necesidades)
    }

    [companyTitle, companyDescription, companyAbout, companyAddress, companyPhone, companyWhatsapp].forEach(element => {
        if (element) {
            element.addEventListener('input', updatePreview);
        }
    });

    if (companyLocation) {
        companyLocation.addEventListener('change', updatePreview);
    }

    const companyForm = document.getElementById('companyDataForm');
    if (companyForm) {
        companyForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const saveBtn = document.getElementById('saveCompanyData');
            if (saveBtn) {
                saveBtn.classList.add('loading');
                saveBtn.disabled = true;
            }

            setTimeout(() => {
                showNotification('Datos de la empresa guardados correctamente', 'success');
                if (saveBtn) {
                    saveBtn.classList.remove('loading');
                    saveBtn.disabled = false;
                }
            }, 2000);
        });
    }

    const resetBtn = document.getElementById('resetCompanyData');
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            if (confirm('¿Estás seguro de que quieres restablecer todos los datos?')) {
                companyForm.reset();
                updatePreview();
                showNotification('Datos restablecidos', 'info');
            }
        });
    }

    const previewBtn = document.getElementById('previewChanges');
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            updatePreview();
            showNotification('Vista previa actualizada', 'info');
        });
    }

    updatePreview();
}

// ===== ATAJOS DE TECLADO =====
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S para guardar
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            if (currentSection === 'empresa') {
                document.getElementById('companyDataForm').dispatchEvent(new Event('submit'));
            }
        }

        // Escape para cerrar modales
        if (e.key === 'Escape') {
            closeAddProductModal();
        }

        // Ctrl/Cmd + N para nuevo producto
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            if (currentSection === 'dashboard') {
                openAddProductModal();
            }
        }
    });
}

// ===== FUNCIÓN PARA MOSTRAR NOTIFICACIONES =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// ===== FUNCIONES DE UTILIDAD =====

// Formatear números con separadores de miles
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

// Formatear precios
function formatPrice(price) {
    return `$${formatNumber(price)}`;
}

// Validar email
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Validar teléfono
function validatePhone(phone) {
    const re = /^[\+]?[0-9\s\-\(\)]+$/;
    return re.test(phone);
}

// Debounce para optimizar búsquedas
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== BÚSQUEDA EN TIEMPO REAL =====
function initializeSearch() {
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        const debouncedSearch = debounce(performSearch, 300);
        searchInput.addEventListener('input', function() {
            debouncedSearch(this.value);
        });
    }
}

function performSearch(query) {
    if (query.length < 2) return;

    // Simular búsqueda
    console.log('Buscando:', query);
    showNotification(`Buscando: "${query}"`, 'info');
}

// ===== MANEJO DE ERRORES GLOBALES =====
window.addEventListener('error', function(e) {
    console.error('Error capturado:', e.error);
    showNotification('Ha ocurrido un error inesperado', 'error');
});

// ===== FUNCIONES DE EXPORTACIÓN =====
function exportData(format) {
    showNotification(`Exportando datos en formato ${format}...`, 'info');

    setTimeout(() => {
        showNotification(`Datos exportados correctamente en ${format}`, 'success');
    }, 2000);
}

// ===== FUNCIONES DE BACKUP =====
function createBackup() {
    showNotification('Creando respaldo de datos...', 'info');

    setTimeout(() => {
        showNotification('Respaldo creado correctamente', 'success');
    }, 3000);
}

// ===== CONFIGURACIÓN DE TEMA =====
function toggleTheme() {
    const body = document.body;
    const isDark = body.classList.contains('dark-theme');

    if (isDark) {
        body.classList.remove('dark-theme');
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.add('dark-theme');
        localStorage.setItem('theme', 'dark');
    }
}

// Cargar tema guardado
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }
}

// ===== TOOLTIPS =====
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-popup';
    tooltip.textContent = e.target.getAttribute('data-tooltip');
    document.body.appendChild(tooltip);

    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip-popup');
    if (tooltip) {
        tooltip.remove();
    }
}

// ===== FUNCIONES DE VALIDACIÓN =====
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;

    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
            field.classList.add('success');
        }
    });

    return isValid;
}

// ===== ANIMACIONES Y EFECTOS =====
function animateElement(element, animation) {
    element.style.animation = animation;
    element.addEventListener('animationend', function() {
        element.style.animation = '';
    }, { once: true });
}

// ===== GESTIÓN DE ESTADO =====
const AppState = {
    currentUser: 'Administrador',
    currentSection: 'dashboard',
    currentTab: 'destacados',
    unsavedChanges: false,

    setState(newState) {
        Object.assign(this, newState);
        this.saveToLocalStorage();
    },

    saveToLocalStorage() {
        localStorage.setItem('adminState', JSON.stringify(this));
    },

    loadFromLocalStorage() {
        const saved = localStorage.getItem('adminState');
        if (saved) {
            Object.assign(this, JSON.parse(saved));
        }
    }
};

// ===== MANEJO DE CONEXIÓN =====
function checkConnection() {
    if (navigator.onLine) {
        showNotification('Conexión restaurada', 'success');
    } else {
        showNotification('Sin conexión a internet', 'warning');
    }
}

window.addEventListener('online', checkConnection);
window.addEventListener('offline', checkConnection);

// ===== FUNCIONES DE PERFORMANCE =====
function optimizeImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (img.loading !== 'lazy') {
            img.loading = 'lazy';
        }
    });
}

// ===== LOG DE ACTIVIDADES =====
const ActivityLogger = {
    logs: [],

    log(action, details = '') {
        const logEntry = {
            timestamp: new Date().toISOString(),
            action: action,
            details: details,
            user: AppState.currentUser
        };

        this.logs.push(logEntry);
        console.log('Activity:', logEntry);

        if (this.logs.length > 100) {
            this.logs = this.logs.slice(-100);
        }
    },

    exportLogs() {
        const dataStr = JSON.stringify(this.logs, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = 'activity-logs.json';
        link.click();

        URL.revokeObjectURL(url);
    }
};

// ===== FUNCIONES DE INICIALIZACIÓN ADICIONALES =====
function initializeAdditionalFeatures() {
    // Cargar tema guardado
    loadSavedTheme();

    // Inicializar búsqueda
    initializeSearch();

    // Configurar tooltips
    initializeTooltips();

    // Cargar estado de la aplicación
    AppState.loadFromLocalStorage();

    // Optimizar imágenes
    setTimeout(optimizeImages, 1000);

    // Log de inicio
    ActivityLogger.log('SYSTEM_START', 'Admin panel loaded successfully');
}

// ===== FUNCIONES DE LIMPIEZA =====
function cleanupResources() {
    // Limpiar event listeners
    const modals = document.querySelectorAll('.modal-overlay');
    modals.forEach(modal => {
        modal.removeEventListener('click', closeAddProductModal);
    });

    // Limpiar timeouts
    clearTimeout();
}

// ===== MANEJO DE VISIBILIDAD DE PÁGINA =====
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        ActivityLogger.log('PAGE_HIDDEN', 'User switched to another tab');
    } else {
        ActivityLogger.log('PAGE_VISIBLE', 'User returned to admin panel');
    }
});

// ===== MANEJO DE BEFOREUNLOAD =====
window.addEventListener('beforeunload', function(e) {
    if (AppState.unsavedChanges) {
        e.preventDefault();
        e.returnValue = '¿Estás seguro de que quieres salir? Tienes cambios sin guardar.';
        return e.returnValue;
    }
});

// ===== INICIALIZACIÓN COMPLETA =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Iniciando Admin Panel...');
    
    try {
        // Inicializar funciones principales
        initializeAdmin();
        initializeCompanyForm();
        initializeAddProductModal();
        initializeAdditionalFeatures();
        
        console.log('✅ Admin Panel inicializado correctamente');
        showNotification('Panel de administración cargado correctamente', 'success');
        
    } catch (error) {
        console.error('❌ Error al inicializar Admin Panel:', error);
        showNotification('Error al cargar el panel de administración', 'error');
    }
});

// ===== CLEANUP AL CERRAR =====
window.addEventListener('unload', function() {
    cleanupResources();
    ActivityLogger.log('SYSTEM_SHUTDOWN', 'Admin panel closed');
});

// ===== FUNCIONES PÚBLICAS PARA DEBUGGING =====
window.AdminPanel = {
    openAddProductModal,
    closeAddProductModal,
    showNotification,
    switchToTab,
    exportData,
    createBackup,
    toggleTheme,
    ActivityLogger,
    AppState
};

console.log('📋 Admin Panel cargado. Funciones disponibles en window.AdminPanel');




</script>

</body>
</html>

