<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administrador de Tienda - Panel de Control</title>

    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Estilos CSS -->
    <link rel="stylesheet" href="administrador-styles.css">
</head>
<body>
    <!-- Botón de menú móvil -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h2>TiendaAdmin</h2>
            </div>
            <p class="subtitle">Panel de Control</p>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-section">
                <h3>Principal</h3>
                <ul>
                    <li>
                        <a href="#dashboard" class="nav-link active" data-section="dashboard">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                            <div class="nav-indicator"></div>
                        </a>
                    </li>
                    <li>
                        <a href="#productos" class="nav-link" data-section="productos">
                            <i class="fas fa-box"></i>
                            <span>Productos</span>
                            <span class="badge">124</span>
                        </a>
                    </li>
                    <li>
                        <a href="#categorias" class="nav-link" data-section="categorias">
                            <i class="fas fa-tags"></i>
                            <span>Categorías</span>
                            <span class="badge">8</span>
                        </a>
                    </li>
                    <li>
                        <a href="#inventario" class="nav-link" data-section="inventario">
                            <i class="fas fa-warehouse"></i>
                            <span>Inventario</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="nav-section">
                <h3>Configuración</h3>
                <ul>
                    <li>
                        <a href="#tienda" class="nav-link" data-section="tienda">
                            <i class="fas fa-cog"></i>
                            <span>Configuración</span>
                        </a>
                    </li>
                    <li>
                        <a href="#reportes" class="nav-link" data-section="reportes">
                            <i class="fas fa-chart-bar"></i>
                            <span>Reportes</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-info">
                    <h4>Administrador</h4>
                    <p><EMAIL></p>
                </div>
                <button class="logout-btn" title="Cerrar Sesión">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- Contenido Principal -->
    <main class="main-content">
        <!-- Header -->
        <header class="main-header">
            <div class="header-left">
                <h1 class="page-title">Dashboard</h1>
                <p class="page-subtitle">Resumen general de tu tienda</p>
            </div>
            <div class="header-right">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Buscar productos, pedidos...">
                </div>
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">3</span>
                </button>
                <button class="settings-btn">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="content-area" id="dashboard">
            <!-- KPIs Mejorados -->
            <section class="kpi-section">
                <div class="kpi-header">
                    <h2>Indicadores Clave</h2>
                    <div class="time-filter">
                        <button class="time-btn active" data-period="today">Hoy</button>
                        <button class="time-btn" data-period="week">Semana</button>
                        <button class="time-btn" data-period="month">Mes</button>
                        <button class="time-btn" data-period="year">Año</button>
                    </div>
                </div>

                <div class="kpi-grid">
                    <!-- KPI 1: Visitas a la página -->
                    <div class="kpi-card visits">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Visitas a la página</h3>
                                <div class="kpi-value">1,254</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>12.5%</span>
                                </div>
                            </div>
                        </div>
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <path d="M0,25 L10,22 L20,18 L30,15 L40,12 L50,8 L60,5" stroke="#8b5cf6" stroke-width="2" fill="none"></path>
                                <path d="M0,25 L10,22 L20,18 L30,15 L40,12 L50,8 L60,5 L60,30 L0,30" fill="#8b5cf6" opacity="0.1"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 2: Visitas desde móvil -->
                    <div class="kpi-card mobile">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Visitas desde móvil</h3>
                                <div class="kpi-value">68%</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>5.3%</span>
                                </div>
                            </div>
                        </div>
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <rect x="5" y="20" width="6" height="10" fill="#8b5cf6" opacity="0.4"></rect>
                                <rect x="13" y="18" width="6" height="12" fill="#8b5cf6" opacity="0.5"></rect>
                                <rect x="21" y="15" width="6" height="15" fill="#8b5cf6" opacity="0.6"></rect>
                                <rect x="29" y="12" width="6" height="18" fill="#8b5cf6" opacity="0.7"></rect>
                                <rect x="37" y="8" width="6" height="22" fill="#8b5cf6" opacity="0.8"></rect>
                                <rect x="45" y="5" width="6" height="25" fill="#8b5cf6"></rect>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 3: Tiempo promedio -->
                    <div class="kpi-card time">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Tiempo promedio</h3>
                                <div class="kpi-value">3m 42s</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>0:45</span>
                                </div>
                            </div>
                        </div>
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <circle cx="30" cy="15" r="12" fill="none" stroke="#8b5cf6" stroke-width="3" stroke-dasharray="75 25"></circle>
                                <text x="30" y="19" text-anchor="middle" font-size="8" fill="#8b5cf6" font-weight="bold">75%</text>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 4: Productos compartidos -->
                    <div class="kpi-card shared">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Productos compartidos</h3>
                                <div class="kpi-value">42</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>15.7%</span>
                                </div>
                            </div>
                        </div>
                        <div class="kpi-chart">
                            <svg width="60" height="30" viewBox="0 0 60 30">
                                <circle cx="15" cy="15" r="4" fill="#8b5cf6" opacity="0.3"></circle>
                                <circle cx="25" cy="10" r="3" fill="#8b5cf6" opacity="0.5"></circle>
                                <circle cx="35" cy="20" r="5" fill="#8b5cf6" opacity="0.7"></circle>
                                <circle cx="45" cy="8" r="3" fill="#8b5cf6" opacity="0.6"></circle>
                                <circle cx="50" cy="18" r="4" fill="#8b5cf6"></circle>
                            </svg>
                        </div>
                    </div>

                    <!-- KPI 5: Total Productos -->
                    <div class="kpi-card total-products">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Total Productos</h3>
                                <div class="kpi-value">124</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>12% desde el mes pasado</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- KPI 6: Categorías -->
                    <div class="kpi-card categories">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Categorías</h3>
                                <div class="kpi-value">8</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>2 nuevas este mes</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- KPI 7: Productos en Oferta -->
                    <div class="kpi-card products-offer">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Productos en Oferta</h3>
                                <div class="kpi-value">32</div>
                                <div class="kpi-change negative">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>5% desde el mes pasado</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- KPI 8: Productos Destacados -->
                    <div class="kpi-card products-featured">
                        <div class="kpi-content">
                            <div class="kpi-info">
                                <h3>Productos Destacados</h3>
                                <div class="kpi-value">16</div>
                                <div class="kpi-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>4 nuevos este mes</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Gestión de Productos -->
            <section class="container-products">
                <h2 class="container-title">Gestión de Productos</h2>
                <div class="admin-card">
                    <div class="admin-card-body">
                        <div class="admin-tabs">
                            <div class="admin-tab active" data-tab="destacados">Destacados</div>
                            <div class="admin-tab" data-tab="ofertas">Ofertas</div>
                            <div class="admin-tab" data-tab="novedades">Novedades</div>
                            <div class="admin-tab" data-tab="mas-vistos">Más Vistos</div>
                            <div class="admin-tab" data-tab="tendencias">Tendencias</div>
                            <div class="admin-tab" data-tab="liquidaciones">Liquidaciones</div>
                        </div>

                        <!-- Contenido de pestañas -->
                        <div class="admin-tab-content active" id="destacados">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto destacado</div>
                                    </div>
                                </div>

                                <!-- Producto 1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smartphone Premium">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Smartphones</div>
                                        <h3 class="product-name">Smartphone Premium X12 Pro</h3>
                                        <div class="product-price">
                                            <span class="current-price">$399.990</span>
                                            <span class="original-price">$469.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Destacado</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 2 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Audífonos Inalámbricos">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Audio</div>
                                        <h3 class="product-name">Audífonos Inalámbricos SoundPlus</h3>
                                        <div class="product-price">
                                            <span class="current-price">$89.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Destacado</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smartwatch Fitness">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Wearables</div>
                                        <h3 class="product-name">Smartwatch Fitness Tracker Pro</h3>
                                        <div class="product-price">
                                            <span class="current-price">$79.990</span>
                                            <span class="original-price">$99.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Destacado</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="admin-tab-content" id="ofertas">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en oferta</div>
                                    </div>
                                </div>

                                <!-- Producto 1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Tablet Ultra">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Tablets</div>
                                        <h3 class="product-name">Tablet Ultra 10.5" 128GB</h3>
                                        <div class="product-price">
                                            <span class="current-price">$199.990</span>
                                            <span class="original-price">$269.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Oferta</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 2 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Laptop Gaming">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Computación / Laptops</div>
                                        <h3 class="product-name">Laptop Gaming Pro 15.6"</h3>
                                        <div class="product-price">
                                            <span class="current-price">$899.990</span>
                                            <span class="original-price">$1.099.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Oferta</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smart TV">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Televisores</div>
                                        <h3 class="product-name">Smart TV 4K 55" UltraHD</h3>
                                        <div class="product-price">
                                            <span class="current-price">$499.990</span>
                                            <span class="original-price">$649.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Oferta</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="admin-tab-content" id="novedades">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto de novedad</div>
                                    </div>
                                </div>

                                <!-- Producto 1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Drone 4K">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Drones</div>
                                        <h3 class="product-name">Drone 4K Pro con Cámara Estabilizada</h3>
                                        <div class="product-price">
                                            <span class="current-price">$349.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Novedad</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 2 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Consola de Videojuegos">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Videojuegos</div>
                                        <h3 class="product-name">Consola de Videojuegos NextGen</h3>
                                        <div class="product-price">
                                            <span class="current-price">$599.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Novedad</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Altavoz Inteligente">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Audio</div>
                                        <h3 class="product-name">Altavoz Inteligente con Asistente Virtual</h3>
                                        <div class="product-price">
                                            <span class="current-price">$129.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Novedad</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="admin-tab-content" id="mas-vistos">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto más visto</div>
                                    </div>
                                </div>

                                <!-- Producto 1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smartphone Premium">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Smartphones</div>
                                        <h3 class="product-name">Smartphone Premium X12 Pro</h3>
                                        <div class="product-price">
                                            <span class="current-price">$399.990</span>
                                            <span class="original-price">$469.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Más Visto</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 2 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smart TV">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Televisores</div>
                                        <h3 class="product-name">Smart TV 4K 55" UltraHD</h3>
                                        <div class="product-price">
                                            <span class="current-price">$499.990</span>
                                            <span class="original-price">$649.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Más Visto</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Audífonos Inalámbricos">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Audio</div>
                                        <h3 class="product-name">Audífonos Inalámbricos SoundPlus</h3>
                                        <div class="product-price">
                                            <span class="current-price">$89.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Más Visto</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="admin-tab-content" id="tendencias">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en tendencia</div>
                                    </div>
                                </div>

                                <!-- Producto 1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Drone 4K">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Drones</div>
                                        <h3 class="product-name">Drone 4K Pro con Cámara Estabilizada</h3>
                                        <div class="product-price">
                                            <span class="current-price">$349.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Tendencia</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 2 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smartwatch Fitness">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Wearables</div>
                                        <h3 class="product-name">Smartwatch Fitness Tracker Pro</h3>
                                        <div class="product-price">
                                            <span class="current-price">$79.990</span>
                                            <span class="original-price">$99.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Tendencia</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Consola de Videojuegos">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Videojuegos</div>
                                        <h3 class="product-name">Consola de Videojuegos NextGen</h3>
                                        <div class="product-price">
                                            <span class="current-price">$599.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Tendencia</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="admin-tab-content" id="liquidaciones">
                            <div class="products-grid">
                                <!-- Tarjeta para agregar producto -->
                                <div class="product-card add-product-card">
                                    <div class="product-image" style="display: flex; align-items: center; justify-content: center; background-color: rgba(106, 27, 154, 0.05);">
                                        <button class="product-overlay-btn" style="position: static; width: 60px; height: 60px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                            <i class="fas fa-plus" style="font-size: 24px; color: var(--purple-primary);"></i>
                                        </button>
                                    </div>
                                    <div class="product-details" style="text-align: center;">
                                        <h3 class="product-name" style="margin-top: 15px; color: var(--purple-primary);">Agregar Producto</h3>
                                        <div class="product-category" style="margin-top: 5px;">Añadir nuevo producto en liquidación</div>
                                    </div>
                                </div>

                                <!-- Producto 1 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Laptop Gaming">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Computación / Laptops</div>
                                        <h3 class="product-name">Laptop Gaming Pro 15.6" (Modelo Anterior)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$699.990</span>
                                            <span class="original-price">$1.099.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Liquidación</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 2 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Tablet Ultra">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Tablets</div>
                                        <h3 class="product-name">Tablet Ultra 9" 64GB (Última Unidad)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$149.990</span>
                                            <span class="original-price">$229.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Liquidación</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Producto 3 -->
                                <div class="product-card">
                                    <div class="product-image">
                                        <img src="https://via.placeholder.com/300x200" alt="Smart TV">
                                        <div class="product-overlay">
                                            <button class="product-overlay-btn edit-product">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="product-overlay-btn delete-product">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-details">
                                        <div class="product-category">Electrónica / Televisores</div>
                                        <h3 class="product-name">Smart TV 4K 50" (Exhibición)</h3>
                                        <div class="product-price">
                                            <span class="current-price">$399.990</span>
                                            <span class="original-price">$599.990</span>
                                        </div>
                                        <div class="product-status">
                                            <span class="product-badge badge-active">Liquidación</span>
                                            <label class="product-toggle">
                                                <input type="checkbox" checked>
                                                <span class="product-toggle-slider"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Gestión Simple de Categorías -->
            <section class="container-categories">
                <h2 class="container-title">Gestión de Categorías</h2>

                <!-- Panel Principal de Selección -->
                <div class="categories-main-panel">
                    <!-- Lado Izquierdo - Categorías -->
                    <div class="categories-section">
                        <div class="section-header">
                            <h3>Agregar Categoría</h3>
                            <div class="dropdown-container">
                                <button class="dropdown-btn" id="categoryDropdownBtn" onclick="toggleCategoryDropdown()">
                                    <span>Seleccionar</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="dropdown-menu" id="categoryDropdownMenu">
                                    <!-- Las categorías se cargarán aquí -->
                                </div>
                            </div>
                        </div>

                        <!-- Categorías Seleccionadas -->
                        <div class="selected-items-container">
                            <div class="selected-items" id="selectedCategoriesContainer">
                                <!-- Los bloques de categorías seleccionadas aparecerán aquí -->
                            </div>
                        </div>
                    </div>

                    <!-- Lado Derecho - Subcategorías -->
                    <div class="subcategories-section">
                        <div class="section-header">
                            <h3>Agregar Subcategoría</h3>
                            <div class="dropdown-container">
                                <button class="dropdown-btn" id="subcategoryDropdownBtn" onclick="toggleSubcategoryDropdown()" disabled>
                                    <span>Seleccionar</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="dropdown-menu" id="subcategoryDropdownMenu">
                                    <!-- Las subcategorías se cargarán aquí -->
                                </div>
                            </div>
                        </div>

                        <!-- Subcategorías Seleccionadas -->
                        <div class="selected-items-container">
                            <div class="selected-items" id="selectedSubcategoriesContainer">
                                <!-- Los bloques de subcategorías seleccionadas aparecerán aquí -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Botón de Guardar -->
                <div class="save-section">
                    <button class="save-btn" id="saveCategoriesBtn" onclick="saveSimpleCategoriesConfiguration()" style="display: none;">
                        <i class="fas fa-save"></i>
                        Guardar Configuración
                    </button>
                </div>
            </section>
        </div>

        <!-- Secciones adicionales del panel de administración -->
        <div class="content-area" id="productos" style="display: none;">
            <div class="section-header">
                <h2>Gestión de Productos</h2>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Nuevo Producto
                </button>
            </div>
            <div class="admin-card">
                <div class="admin-card-body">
                    <p>Sección de gestión completa de productos en desarrollo...</p>
                </div>
            </div>
        </div>

        <div class="content-area" id="categorias" style="display: none;">
            <div class="section-header">
                <h2>Gestión de Categorías</h2>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Nueva Categoría
                </button>
            </div>
            <div class="admin-card">
                <div class="admin-card-body">
                    <p>Sección de gestión completa de categorías en desarrollo...</p>
                </div>
            </div>
        </div>

        <div class="content-area" id="inventario" style="display: none;">
            <div class="section-header">
                <h2>Gestión de Inventario</h2>
                <button class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Actualizar Stock
                </button>
            </div>
            <div class="admin-card">
                <div class="admin-card-body">
                    <p>Sección de gestión de inventario en desarrollo...</p>
                </div>
            </div>
        </div>

        <div class="content-area" id="tienda" style="display: none;">
            <div class="section-header">
                <h2>Configuración de Tienda</h2>
            </div>
            <div class="admin-card">
                <div class="admin-card-body">
                    <p>Sección de configuración de tienda en desarrollo...</p>
                </div>
            </div>
        </div>

        <div class="content-area" id="reportes" style="display: none;">
            <div class="section-header">
                <h2>Reportes y Análisis</h2>
            </div>
            <div class="admin-card">
                <div class="admin-card-body">
                    <p>Sección de reportes en desarrollo...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal para Agregar/Editar Categoría -->
    <div class="modal-overlay" id="categoryModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-sitemap"></i>
                    <span id="categoryModalTitle">Agregar Nueva Categoría</span>
                </h3>
                <button class="modal-close" id="closeCategoryModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <div class="form-group">
                        <label class="form-label">Nombre de la Categoría</label>
                        <input type="text" class="form-input" id="categoryName" placeholder="Ej: Electrónica" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Descripción</label>
                        <textarea class="form-textarea" id="categoryDescription" placeholder="Descripción de la categoría..." rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Icono</label>
                        <div class="icon-selector">
                            <div class="icon-option active" data-icon="fas fa-laptop">
                                <i class="fas fa-laptop"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-tshirt">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-home">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-dumbbell">
                                <i class="fas fa-dumbbell"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-car">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-book">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-utensils">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-baby">
                                <i class="fas fa-baby"></i>
                            </div>
                        </div>
                        <input type="hidden" id="selectedIcon" value="fas fa-laptop">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelCategoryBtn">Cancelar</button>
                <button type="submit" class="btn btn-primary" id="saveCategoryBtn">
                    <i class="fas fa-save"></i>
                    Guardar Categoría
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para Agregar/Editar Subcategoría -->
    <div class="modal-overlay" id="subcategoryModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-tags"></i>
                    <span id="subcategoryModalTitle">Agregar Nueva Subcategoría</span>
                </h3>
                <button class="modal-close" id="closeSubcategoryModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="subcategoryForm">
                    <div class="form-group">
                        <label class="form-label">Categoría Padre</label>
                        <select class="form-select" id="parentCategory" required>
                            <option value="electronica">Electrónica</option>
                            <option value="moda">Moda y Ropa</option>
                            <option value="hogar">Hogar y Jardín</option>
                            <option value="deportes">Deportes</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Nombre de la Subcategoría</label>
                        <input type="text" class="form-input" id="subcategoryName" placeholder="Ej: Smartphones" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Descripción</label>
                        <textarea class="form-textarea" id="subcategoryDescription" placeholder="Descripción de la subcategoría..." rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Icono</label>
                        <div class="icon-selector">
                            <div class="icon-option active" data-icon="fas fa-mobile-alt">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-laptop">
                                <i class="fas fa-laptop"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-tv">
                                <i class="fas fa-tv"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-gamepad">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-headphones">
                                <i class="fas fa-headphones"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-camera">
                                <i class="fas fa-camera"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-tablet-alt">
                                <i class="fas fa-tablet-alt"></i>
                            </div>
                            <div class="icon-option" data-icon="fas fa-keyboard">
                                <i class="fas fa-keyboard"></i>
                            </div>
                        </div>
                        <input type="hidden" id="selectedSubIcon" value="fas fa-mobile-alt">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelSubcategoryBtn">Cancelar</button>
                <button type="submit" class="btn btn-primary" id="saveSubcategoryBtn">
                    <i class="fas fa-save"></i>
                    Guardar Subcategoría
                </button>
            </div>
        </div>
    </div>

    <!-- Modal para Agregar/Editar Producto -->
    <div class="modal-overlay" id="productModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-box"></i>
                    <span id="productModalTitle">Agregar Nuevo Producto</span>
                </h3>
                <button class="modal-close" id="closeProductModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Nombre del Producto</label>
                            <input type="text" class="form-input" id="productName" placeholder="Ej: Smartphone Premium X12 Pro" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Categoría</label>
                            <select class="form-select" id="productCategory" required>
                                <option value="">Seleccionar categoría</option>
                                <option value="electronica">Electrónica</option>
                                <option value="moda">Moda y Ropa</option>
                                <option value="hogar">Hogar y Jardín</option>
                                <option value="deportes">Deportes</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Precio Actual</label>
                            <input type="number" class="form-input" id="productCurrentPrice" placeholder="399990" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Precio Original</label>
                            <input type="number" class="form-input" id="productOriginalPrice" placeholder="469990">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Descripción</label>
                        <textarea class="form-textarea" id="productDescription" placeholder="Descripción del producto..." rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Imagen del Producto</label>
                        <div class="file-upload-area">
                            <input type="file" id="productImage" accept="image/*" style="display: none;">
                            <div class="file-upload-placeholder" onclick="document.getElementById('productImage').click();">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>Haz clic para subir una imagen</p>
                                <span>JPG, PNG, GIF hasta 5MB</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Tipo de Producto</label>
                        <div class="checkbox-group">
                            <label class="checkbox-item">
                                <input type="checkbox" id="isDestacado" name="productType" value="destacado">
                                <span class="checkmark"></span>
                                Destacado
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isOferta" name="productType" value="oferta">
                                <span class="checkmark"></span>
                                En Oferta
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isNovedad" name="productType" value="novedad">
                                <span class="checkmark"></span>
                                Novedad
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isTendencia" name="productType" value="tendencia">
                                <span class="checkmark"></span>
                                Tendencia
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" id="isLiquidacion" name="productType" value="liquidacion">
                                <span class="checkmark"></span>
                                Liquidación
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelProductBtn">Cancelar</button>
                <button type="submit" class="btn btn-primary" id="saveProductBtn">
                    <i class="fas fa-save"></i>
                    Guardar Producto
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="categorias-data.js"></script>
    <script src="administrador-scripts.js"></script>
</body>
</html>


