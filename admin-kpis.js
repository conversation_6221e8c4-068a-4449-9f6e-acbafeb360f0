/**
 * ADMIN PANEL - SISTEMA DE KPIs Y GRÁFICOS
 * Gestión de indicadores clave de rendimiento y visualización de datos
 */

// ==================== DATOS DE KPIs ====================

const kpiData = {
    today: {
        pageViews: 1254,
        mobileViews: 68,
        avgTime: '3m 42s',
        shares: 42,
        bounceRate: 32,
        conversions: 8.5
    },
    week: {
        pageViews: 8750,
        mobileViews: 72,
        avgTime: '4m 15s',
        shares: 298,
        bounceRate: 28,
        conversions: 12.3
    },
    month: {
        pageViews: 35420,
        mobileViews: 75,
        avgTime: '4m 52s',
        shares: 1205,
        bounceRate: 25,
        conversions: 15.8
    },
    year: {
        pageViews: 425040,
        mobileViews: 78,
        avgTime: '5m 18s',
        shares: 14460,
        bounceRate: 22,
        conversions: 18.9
    }
};

// ==================== INICIALIZACIÓN DE KPIs ====================

/**
 * Inicializar sistema de KPIs
 */
function initializeKPISystem() {
    console.log('📊 Inicializando sistema de KPIs...');
    
    // Animar tarjetas de KPI al cargar
    animateKPICards();
    
    // Configurar botones de período
    setupPeriodButtons();
    
    // Cargar datos iniciales
    updateChartsAndData('Hoy');
    
    // Actualizar datos periódicamente
    startPeriodicUpdates();
    
    console.log('✅ Sistema de KPIs inicializado');
}

/**
 * Animar tarjetas de KPI
 */
function animateKPICards() {
    const kpiCards = document.querySelectorAll('.admin-kpi-card');
    kpiCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease, box-shadow 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });
}

/**
 * Configurar botones de período
 */
function setupPeriodButtons() {
    const periodButtons = document.querySelectorAll('.admin-kpi-period-btn');
    
    periodButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remover clase active de todos los botones
            periodButtons.forEach(btn => btn.classList.remove('active'));
            
            // Agregar clase active al botón clickeado
            button.classList.add('active');
            
            // Actualizar datos
            const period = button.textContent.trim();
            updateChartsAndData(period);
            
            // Efecto visual
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

/**
 * Actualizar gráficos y datos
 */
function updateChartsAndData(period) {
    console.log(`📈 Actualizando datos para período: ${period}`);
    
    const periodKey = getPeriodKey(period);
    const data = kpiData[periodKey];
    
    if (!data) {
        console.error('Datos no encontrados para el período:', period);
        return;
    }
    
    // Actualizar valores de KPI
    updateKPIValues(data);
    
    // Actualizar gráficos
    updateCharts(data, period);
    
    // Actualizar estadísticas generales
    updateGeneralStats(data);
    
    // Mostrar notificación
    showNotification(`Datos actualizados para ${period.toLowerCase()}`, 'info');
}

/**
 * Obtener clave de período
 */
function getPeriodKey(period) {
    const periodMap = {
        'Hoy': 'today',
        'Semana': 'week',
        'Mes': 'month',
        'Año': 'year'
    };
    
    return periodMap[period] || 'today';
}

/**
 * Actualizar valores de KPI
 */
function updateKPIValues(data) {
    // Visitas a la página
    updateKPICard(0, {
        value: formatNumber(data.pageViews),
        trend: calculateTrend(data.pageViews, 'pageViews'),
        trendValue: '12.5%'
    });
    
    // Visitas desde móvil
    updateKPICard(1, {
        value: `${data.mobileViews}%`,
        trend: data.mobileViews > 70 ? 'positive' : 'negative',
        trendValue: '5.3%'
    });
    
    // Tiempo promedio
    updateKPICard(2, {
        value: data.avgTime,
        trend: 'positive',
        trendValue: '0:45'
    });
    
    // Productos compartidos
    updateKPICard(3, {
        value: formatNumber(data.shares),
        trend: 'positive',
        trendValue: '15.7%'
    });
}

/**
 * Actualizar tarjeta de KPI individual
 */
function updateKPICard(index, data) {
    const cards = document.querySelectorAll('.admin-kpi-card');
    const card = cards[index];
    
    if (!card) return;
    
    const valueElement = card.querySelector('.admin-kpi-value');
    const trendElement = card.querySelector('.admin-kpi-trend');
    const trendValueElement = card.querySelector('.admin-kpi-trend span');
    
    if (valueElement) {
        // Animar cambio de valor
        valueElement.style.transform = 'scale(1.1)';
        valueElement.textContent = data.value;
        
        setTimeout(() => {
            valueElement.style.transform = 'scale(1)';
        }, 200);
    }
    
    if (trendElement) {
        trendElement.className = `admin-kpi-trend ${data.trend}`;
        
        if (trendValueElement) {
            trendValueElement.textContent = data.trendValue;
        }
    }
}

/**
 * Actualizar gráficos
 */
function updateCharts(data, period) {
    // Actualizar gráfico de línea
    updateLineChart(data, period);
    
    // Actualizar gráfico de barras
    updateBarChart(data, period);
    
    // Actualizar gráfico donut
    updateDonutChart(data, period);
    
    // Actualizar gráfico de burbujas
    updateBubbleChart(data, period);
}

/**
 * Actualizar gráfico de línea
 */
function updateLineChart(data, period) {
    const lineChart = document.querySelector('.line-chart svg path');
    if (!lineChart) return;
    
    // Generar nuevos puntos basados en los datos
    const points = generateLineChartPoints(data.pageViews, period);
    lineChart.setAttribute('d', points);
    
    // Animar el path
    const pathLength = lineChart.getTotalLength();
    lineChart.style.strokeDasharray = pathLength;
    lineChart.style.strokeDashoffset = pathLength;
    
    setTimeout(() => {
        lineChart.style.transition = 'stroke-dashoffset 1s ease-in-out';
        lineChart.style.strokeDashoffset = 0;
    }, 100);
}

/**
 * Actualizar gráfico de barras
 */
function updateBarChart(data, period) {
    const barChart = document.querySelector('.bar-chart svg');
    if (!barChart) return;
    
    const bars = barChart.querySelectorAll('rect');
    const values = generateBarChartValues(data.mobileViews, period);
    
    bars.forEach((bar, index) => {
        if (values[index]) {
            const newHeight = values[index];
            const newY = 40 - newHeight;
            
            bar.style.transition = 'height 0.8s ease, y 0.8s ease';
            bar.setAttribute('height', newHeight);
            bar.setAttribute('y', newY);
        }
    });
}

/**
 * Actualizar gráfico donut
 */
function updateDonutChart(data, period) {
    const donutChart = document.querySelector('.donut-chart svg circle:nth-child(2)');
    const textElement = document.querySelector('.donut-chart svg text');
    
    if (!donutChart || !textElement) return;
    
    const percentage = Math.round(data.mobileViews);
    const circumference = 2 * Math.PI * 15; // radio = 15
    const strokeDasharray = (percentage / 100) * circumference;
    
    donutChart.style.transition = 'stroke-dasharray 1s ease';
    donutChart.setAttribute('stroke-dasharray', `${strokeDasharray} ${circumference}`);
    
    // Actualizar texto
    textElement.textContent = `${percentage}%`;
}

/**
 * Actualizar gráfico de burbujas
 */
function updateBubbleChart(data, period) {
    const bubbleChart = document.querySelector('.bubble-chart svg');
    if (!bubbleChart) return;
    
    const circles = bubbleChart.querySelectorAll('circle');
    const values = generateBubbleChartValues(data.shares, period);
    
    circles.forEach((circle, index) => {
        if (values[index]) {
            const newRadius = values[index];
            
            circle.style.transition = 'r 0.6s ease';
            circle.setAttribute('r', newRadius);
        }
    });
}

// ==================== GENERADORES DE DATOS ====================

/**
 * Generar puntos para gráfico de línea
 */
function generateLineChartPoints(baseValue, period) {
    const points = [];
    const numPoints = 10;
    
    for (let i = 0; i <= numPoints; i++) {
        const x = (i / numPoints) * 100;
        const variation = (Math.random() - 0.5) * 20;
        const y = Math.max(5, Math.min(35, 35 - (i * 2) + variation));
        points.push(`${x},${y}`);
    }
    
    return `M${points.join(' L')}`;
}

/**
 * Generar valores para gráfico de barras
 */
function generateBarChartValues(baseValue, period) {
    const values = [];
    const numBars = 9;
    
    for (let i = 0; i < numBars; i++) {
        const variation = (Math.random() - 0.5) * 10;
        const height = Math.max(10, Math.min(35, 25 + variation));
        values.push(height);
    }
    
    return values;
}

/**
 * Generar valores para gráfico de burbujas
 */
function generateBubbleChartValues(baseValue, period) {
    const values = [];
    const numBubbles = 5;
    
    for (let i = 0; i < numBubbles; i++) {
        const variation = (Math.random() - 0.5) * 4;
        const radius = Math.max(3, Math.min(12, 7 + variation));
        values.push(radius);
    }
    
    return values;
}

// ==================== ESTADÍSTICAS GENERALES ====================

/**
 * Actualizar estadísticas generales
 */
function updateGeneralStats(data) {
    const statCards = document.querySelectorAll('.stat-card');
    
    if (statCards.length >= 4) {
        // Total Productos
        updateStatCard(statCards[0], {
            value: '124',
            change: 'positive',
            changeText: '12% desde el mes pasado'
        });
        
        // Categorías
        updateStatCard(statCards[1], {
            value: categories.length.toString(),
            change: 'positive',
            changeText: '2 nuevas este mes'
        });
        
        // Productos en Oferta
        updateStatCard(statCards[2], {
            value: '32',
            change: 'negative',
            changeText: '5% desde el mes pasado'
        });
        
        // Productos Destacados
        updateStatCard(statCards[3], {
            value: '16',
            change: 'positive',
            changeText: '4 nuevos este mes'
        });
    }
}

/**
 * Actualizar tarjeta de estadística
 */
function updateStatCard(card, data) {
    const valueElement = card.querySelector('.stat-value');
    const changeElement = card.querySelector('.stat-change');
    const changeTextElement = card.querySelector('.stat-change span');
    
    if (valueElement) {
        valueElement.style.transform = 'scale(1.1)';
        valueElement.textContent = data.value;
        
        setTimeout(() => {
            valueElement.style.transform = 'scale(1)';
        }, 200);
    }
    
    if (changeElement) {
        changeElement.className = `stat-change ${data.change}`;
        
        if (changeTextElement) {
            changeTextElement.textContent = data.changeText;
        }
    }
}

// ==================== UTILIDADES ====================

/**
 * Formatear números
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * Calcular tendencia
 */
function calculateTrend(currentValue, metric) {
    // Simulación de cálculo de tendencia
    const randomTrend = Math.random();
    return randomTrend > 0.5 ? 'positive' : 'negative';
}

/**
 * Iniciar actualizaciones periódicas
 */
function startPeriodicUpdates() {
    // Actualizar cada 30 segundos
    setInterval(() => {
        const activeButton = document.querySelector('.admin-kpi-period-btn.active');
        if (activeButton) {
            updateChartsAndData(activeButton.textContent.trim());
        }
    }, 30000);
}

// ==================== INICIALIZACIÓN ====================

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    initializeKPISystem();
    
    // Animar tarjetas de estadísticas
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 * index);
    });
});

console.log('📊 Admin KPIs Script Loaded Successfully');
