/**
 * ADMIN PANEL - GESTIÓN DE PRODUCTOS Y FUNCIONALIDADES ADICIONALES
 * Sistema de productos, búsqueda avanzada, reportes y ordenamiento
 */

// ==================== GESTIÓN DE PRODUCTOS ====================

/**
 * Abrir modal de producto
 */
function openProductModal(product = null) {
    // Implementación del modal de productos
    console.log('Abriendo modal de producto:', product);
    showNotification('Modal de producto abierto', 'info');
}

/**
 * Editar producto
 */
function editProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    console.log('Editando producto:', productName);
    trackProductView(productName);
    showNotification(`Editando producto: ${productName}`, 'info');
}

/**
 * Eliminar producto
 */
function deleteProduct(productCard) {
    const productName = productCard.querySelector('.product-name').textContent;
    
    if (confirm(`¿Estás seguro de que quieres eliminar "${productName}"?`)) {
        productCard.remove();
        showNotification(`Producto "${productName}" eliminado`, 'success');
        updateProductCounters();
        markAsChanged();
    }
}

/**
 * Alternar estado del producto
 */
function toggleProductStatus(toggle) {
    const productCard = toggle.closest('.product-card');
    const productName = productCard.querySelector('.product-name').textContent;
    const isActive = toggle.checked;
    
    productCard.classList.toggle('inactive', !isActive);
    
    const status = isActive ? 'activado' : 'desactivado';
    showNotification(`Producto "${productName}" ${status}`, 'success');
    markAsChanged();
}

/**
 * Actualizar contadores de productos
 */
function updateProductCounters() {
    const totalProducts = document.querySelectorAll('.product-card:not(.add-product-card)').length;
    const activeProducts = document.querySelectorAll('.product-card:not(.add-product-card) .product-toggle input:checked').length;
    
    // Actualizar estadísticas en el dashboard
    const statCards = document.querySelectorAll('.stat-card');
    if (statCards.length > 0) {
        const totalCard = statCards[0];
        const totalValue = totalCard.querySelector('.stat-value');
        if (totalValue) {
            totalValue.textContent = totalProducts;
        }
    }
    
    console.log(`Productos: ${totalProducts} total, ${activeProducts} activos`);
}

/**
 * Actualizar select de categorías en productos
 */
function updateProductCategorySelect() {
    const categorySelects = document.querySelectorAll('select[name="productCategory"]');
    
    categorySelects.forEach(select => {
        // Limpiar opciones existentes
        select.innerHTML = '<option value="">Seleccionar categoría</option>';
        
        // Agregar categorías disponibles
        categories.forEach(category => {
            if (category.visible) {
                const option = document.createElement('option');
                option.value = category.name;
                option.textContent = category.name;
                select.appendChild(option);
            }
        });
    });
}

// ==================== SISTEMA DE BÚSQUEDA AVANZADA ====================

/**
 * Crear interfaz de búsqueda
 */
function createSearchInterface() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'admin-search-container';
    searchContainer.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    `;

    searchContainer.innerHTML = `
        <div class="search-header">
            <h3 style="margin: 0 0 15px 0; color: var(--purple-primary);">
                <i class="fas fa-search"></i> Búsqueda Avanzada
            </h3>
        </div>
        <div class="search-filters" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div class="search-field">
                <label class="admin-label">Buscar por nombre</label>
                <input type="text" id="searchName" class="admin-input" placeholder="Nombre del producto...">
            </div>
            <div class="search-field">
                <label class="admin-label">Categoría</label>
                <select id="searchCategory" class="admin-select">
                    <option value="">Todas las categorías</option>
                </select>
            </div>
            <div class="search-field">
                <label class="admin-label">Rango de precio</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="priceMin" class="admin-input" placeholder="Mín">
                    <input type="number" id="priceMax" class="admin-input" placeholder="Máx">
                </div>
            </div>
            <div class="search-field">
                <label class="admin-label">Estado</label>
                <select id="searchStatus" class="admin-select">
                    <option value="">Todos</option>
                    <option value="active">Activo</option>
                    <option value="inactive">Inactivo</option>
                </select>
            </div>
        </div>
        <div class="search-actions" style="margin-top: 15px; display: flex; gap: 10px;">
            <button id="applySearch" class="admin-btn admin-btn-primary">
                <i class="fas fa-search"></i> Buscar
            </button>
            <button id="clearSearch" class="admin-btn admin-btn-secondary">
                <i class="fas fa-times"></i> Limpiar
            </button>
        </div>
    `;

    return searchContainer;
}

/**
 * Inicializar búsqueda avanzada
 */
function initializeAdvancedSearch() {
    const productsContainer = document.querySelector('.container-products .admin-card-body');
    if (productsContainer) {
        const searchInterface = createSearchInterface();
        productsContainer.insertBefore(searchInterface, productsContainer.firstChild);

        // Llenar select de categorías
        const categorySelect = document.getElementById('searchCategory');
        categories.forEach(cat => {
            const option = document.createElement('option');
            option.value = cat.name.toLowerCase();
            option.textContent = cat.name;
            categorySelect.appendChild(option);
        });

        // Event listeners
        document.getElementById('applySearch').addEventListener('click', performAdvancedSearch);
        document.getElementById('clearSearch').addEventListener('click', clearAdvancedSearch);

        // Búsqueda en tiempo real
        document.getElementById('searchName').addEventListener('input', debounce(performAdvancedSearch, 300));
    }
}

/**
 * Realizar búsqueda avanzada
 */
function performAdvancedSearch() {
    const searchName = document.getElementById('searchName').value.toLowerCase();
    const searchCategory = document.getElementById('searchCategory').value.toLowerCase();
    const priceMin = parseFloat(document.getElementById('priceMin').value) || 0;
    const priceMax = parseFloat(document.getElementById('priceMax').value) || Infinity;
    const searchStatus = document.getElementById('searchStatus').value;

    const products = document.querySelectorAll('.product-card:not(.add-product-card)');
    let visibleCount = 0;

    products.forEach(product => {
        const name = product.querySelector('.product-name').textContent.toLowerCase();
        const category = product.querySelector('.product-category').textContent.toLowerCase();
        const priceText = product.querySelector('.current-price').textContent.replace(/[^\d]/g, '');
        const price = parseFloat(priceText) || 0;
        const isActive = product.querySelector('.product-toggle input').checked;
        const status = isActive ? 'active' : 'inactive';

        let matches = true;

        if (searchName && !name.includes(searchName)) matches = false;
        if (searchCategory && !category.includes(searchCategory)) matches = false;
        if (price < priceMin || price > priceMax) matches = false;
        if (searchStatus && status !== searchStatus) matches = false;

        if (matches) {
            product.style.display = 'block';
            visibleCount++;
        } else {
            product.style.display = 'none';
        }
    });

    // Mostrar resultado de búsqueda
    updateSearchResults(visibleCount);
    
    // Trackear búsqueda
    if (searchName) {
        trackSearch(searchName);
    }
}

/**
 * Actualizar resultados de búsqueda
 */
function updateSearchResults(count) {
    const resultMessage = document.querySelector('.search-results');
    if (resultMessage) {
        resultMessage.remove();
    }

    const searchContainer = document.querySelector('.admin-search-container');
    const resultDiv = document.createElement('div');
    resultDiv.className = 'search-results';
    resultDiv.style.cssText = `
        margin-top: 10px;
        padding: 10px;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 4px;
        color: var(--purple-primary);
        font-weight: 500;
    `;
    resultDiv.innerHTML = `<i class="fas fa-info-circle"></i> Se encontraron ${count} productos`;
    searchContainer.appendChild(resultDiv);
}

/**
 * Limpiar búsqueda avanzada
 */
function clearAdvancedSearch() {
    document.getElementById('searchName').value = '';
    document.getElementById('searchCategory').value = '';
    document.getElementById('priceMin').value = '';
    document.getElementById('priceMax').value = '';
    document.getElementById('searchStatus').value = '';

    const products = document.querySelectorAll('.product-card:not(.add-product-card)');
    products.forEach(product => {
        product.style.display = 'block';
    });

    const resultMessage = document.querySelector('.search-results');
    if (resultMessage) {
        resultMessage.remove();
    }

    showNotification('Búsqueda limpiada', 'info');
}

/**
 * Búsqueda simple
 */
function performSearch() {
    const searchInput = document.getElementById('searchName');
    if (searchInput) {
        performAdvancedSearch();
    }
}

// ==================== SISTEMA DE ORDENAMIENTO ====================

/**
 * Ordenar productos
 */
function sortProducts(criteria, order = 'asc') {
    const activeTabContent = document.querySelector('.admin-tab-content.active');
    if (!activeTabContent) return;

    const productsGrid = activeTabContent.querySelector('.products-grid');
    const products = Array.from(productsGrid.querySelectorAll('.product-card:not(.add-product-card)'));
    const addCard = productsGrid.querySelector('.add-product-card');

    products.sort((a, b) => {
        let valueA, valueB;

        switch (criteria) {
            case 'name':
                valueA = a.querySelector('.product-name').textContent.toLowerCase();
                valueB = b.querySelector('.product-name').textContent.toLowerCase();
                break;
            case 'price':
                valueA = parseFloat(a.querySelector('.current-price').textContent.replace(/[^\d]/g, '')) || 0;
                valueB = parseFloat(b.querySelector('.current-price').textContent.replace(/[^\d]/g, '')) || 0;
                break;
            case 'category':
                valueA = a.querySelector('.product-category').textContent.toLowerCase();
                valueB = b.querySelector('.product-category').textContent.toLowerCase();
                break;
            default:
                return 0;
        }

        if (order === 'asc') {
            return valueA > valueB ? 1 : -1;
        } else {
            return valueA < valueB ? 1 : -1;
        }
    });

    // Limpiar grid y reordenar
    productsGrid.innerHTML = '';
    productsGrid.appendChild(addCard);
    products.forEach(product => {
        productsGrid.appendChild(product);
    });

    showNotification(`Productos ordenados por ${criteria} (${order === 'asc' ? 'ascendente' : 'descendente'})`, 'info');
}

/**
 * Crear interfaz de ordenamiento
 */
function createSortingInterface() {
    const sortContainer = document.createElement('div');
    sortContainer.className = 'admin-sort-container';
    sortContainer.style.cssText = `
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    `;

    sortContainer.innerHTML = `
        <span style="font-weight: 600; color: var(--text-primary);">
            <i class="fas fa-sort"></i> Ordenar por:
        </span>
        <select id="sortCriteria" class="admin-select" style="width: auto; min-width: 150px;">
            <option value="name">Nombre</option>
            <option value="price">Precio</option>
            <option value="category">Categoría</option>
        </select>
        <select id="sortOrder" class="admin-select" style="width: auto; min-width: 120px;">
            <option value="asc">Ascendente</option>
            <option value="desc">Descendente</option>
        </select>
        <button id="applySortBtn" class="admin-btn admin-btn-primary" style="padding: 8px 15px;">
            <i class="fas fa-sort-amount-down"></i> Aplicar
        </button>
        <button id="generateReportBtn" class="admin-btn admin-btn-secondary" style="padding: 8px 15px; margin-left: auto;">
            <i class="fas fa-chart-bar"></i> Generar Reporte
        </button>
    `;

    return sortContainer;
}

/**
 * Inicializar interfaz de ordenamiento
 */
function initializeSortingInterface() {
    const tabsContainer = document.querySelector('.admin-tabs');
    if (tabsContainer) {
        const sortInterface = createSortingInterface();
        tabsContainer.parentNode.insertBefore(sortInterface, tabsContainer.nextSibling);

        // Event listeners
        document.getElementById('applySortBtn').addEventListener('click', () => {
            const criteria = document.getElementById('sortCriteria').value;
            const order = document.getElementById('sortOrder').value;
            sortProducts(criteria, order);
        });

        document.getElementById('generateReportBtn').addEventListener('click', () => {
            const report = generateProductReport();
            displayReport(report);
        });
    }
}

// ==================== SISTEMA DE PESTAÑAS ====================

/**
 * Cambiar pestaña
 */
function switchTab(tab) {
    const tabs = document.querySelectorAll('.admin-tab');
    const tabContents = document.querySelectorAll('.admin-tab-content');

    // Remover clase active de todas las pestañas
    tabs.forEach(t => t.classList.remove('active'));
    tabContents.forEach(content => content.classList.remove('active'));

    // Activar pestaña seleccionada
    tab.classList.add('active');
    
    const targetId = tab.getAttribute('data-tab');
    const targetContent = document.getElementById(targetId);
    
    if (targetContent) {
        targetContent.classList.add('active');
    }

    // Trackear vista de categoría
    const categoryName = tab.textContent.trim();
    trackCategoryView(categoryName);

    showNotification(`Pestaña "${categoryName}" activada`, 'info');
}

console.log('📦 Admin Products Script Loaded Successfully');
