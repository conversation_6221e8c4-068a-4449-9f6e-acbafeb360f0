// ==================== ADMINISTRADOR DASHBOARD ====================

// Variables globales
let currentSection = 'dashboard';
let currentTab = 'productos';
let currentPeriod = 'mes';

// Datos de ejemplo para KPIs
const kpiData = {
    mes: {
        ventas: 125000,
        pedidos: 342,
        visitantes: 1250,
        conversion: 2.8
    },
    semana: {
        ventas: 28500,
        pedidos: 78,
        visitantes: 285,
        conversion: 3.1
    },
    dia: {
        ventas: 4200,
        pedidos: 12,
        visitantes: 42,
        conversion: 2.9
    }
};

// ==================== FUNCIONES PRINCIPALES ====================

/**
 * Cambiar sección activa
 */
function switchSection(sectionName) {
    console.log('Cambiando a sección:', sectionName);
    
    // Ocultar todas las secciones
    document.querySelectorAll('.content-area').forEach(area => {
        area.style.display = 'none';
    });
    
    // Mostrar la sección seleccionada
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.style.display = 'block';
        currentSection = sectionName;
    }
    
    // Actualizar navegación activa
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    const activeNavItem = document.querySelector(`[onclick="switchSection('${sectionName}')"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
    
    console.log('Sección cambiada a:', sectionName);
}

/**
 * Cambiar tab activo
 */
function switchTab(tabName) {
    console.log('Cambiando a tab:', tabName);
    
    // Actualizar tabs
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Activar tab seleccionado
    const activeTabBtn = document.querySelector(`[onclick="switchTab('${tabName}')"]`);
    const activeTabContent = document.getElementById(tabName);
    
    if (activeTabBtn) activeTabBtn.classList.add('active');
    if (activeTabContent) activeTabContent.classList.add('active');
    
    currentTab = tabName;
    console.log('Tab cambiado a:', tabName);
}

/**
 * Cambiar período de KPIs
 */
function changePeriod(period) {
    console.log('Cambiando período a:', period);
    currentPeriod = period;
    
    // Actualizar botones de período
    document.querySelectorAll('.period-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeBtn = document.querySelector(`[onclick="changePeriod('${period}')"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
    
    // Actualizar KPIs
    updateKPIs();
}

/**
 * Actualizar KPIs
 */
function updateKPIs() {
    const data = kpiData[currentPeriod];
    if (!data) return;
    
    // Actualizar valores
    const ventasElement = document.querySelector('.kpi-card:nth-child(1) .kpi-value');
    const pedidosElement = document.querySelector('.kpi-card:nth-child(2) .kpi-value');
    const visitantesElement = document.querySelector('.kpi-card:nth-child(3) .kpi-value');
    const conversionElement = document.querySelector('.kpi-card:nth-child(4) .kpi-value');
    
    if (ventasElement) ventasElement.textContent = formatCurrency(data.ventas);
    if (pedidosElement) pedidosElement.textContent = formatNumber(data.pedidos);
    if (visitantesElement) visitantesElement.textContent = formatNumber(data.visitantes);
    if (conversionElement) conversionElement.textContent = data.conversion + '%';
    
    console.log('KPIs actualizados para período:', currentPeriod);
}

/**
 * Mostrar notificación
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove después de 5 segundos
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// ==================== FUNCIONES UTILITARIAS ====================

/**
 * Formatear moneda
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

/**
 * Formatear número
 */
function formatNumber(number) {
    return new Intl.NumberFormat('es-ES').format(number);
}

/**
 * Inicialización del dashboard
 */
function initializeDashboard() {
    console.log('Dashboard inicializado correctamente');
    
    // Actualizar KPIs iniciales
    updateKPIs();
    
    // Configurar sección inicial
    switchSection('dashboard');
}

// ==================== INICIALIZACIÓN ====================

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, inicializando dashboard...');
    initializeDashboard();
});

// ==================== EXPORT PARA DEBUGGING ====================
window.AdminApp = {
    switchSection,
    switchTab,
    changePeriod,
    updateKPIs,
    showNotification,
    formatCurrency,
    formatNumber,
    currentSection,
    currentTab,
    currentPeriod
};

console.log('✅ Administrador Scripts cargado correctamente');
